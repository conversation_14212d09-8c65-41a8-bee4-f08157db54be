# Chat 模块路由功能说明

## 路由结构

本模块实现了嵌套路由功能，支持以下路由：

### 主要路由

- `/chat` - 聊天首页（默认聊天界面）
- `/chat/task/:sessionid` - 任务会话页面
- `/chat/expert/:id` - 专家咨询页面

## ✅ 实现状态

所有路由功能已成功实现并测试通过：

- ✅ 嵌套路由配置
- ✅ 动态路由参数支持
- ✅ 侧边栏导航功能
- ✅ 路由状态高亮
- ✅ ChatStore 访问问题已解决

## 功能特性

### 1. 侧边栏导航

在聊天页面的侧边栏中添加了导航按钮：

- **聊天首页** - 返回默认聊天界面
- **任务会话** - 跳转到当前会话的任务页面
- **专家咨询** - 下拉选择不同专家进行咨询

### 2. 会话操作菜单

每个聊天会话项都有操作菜单，包含：

- 打开任务会话 - 快速跳转到该会话的任务页面
- 编辑标题
- 删除会话

### 3. 路由高亮

导航按钮会根据当前路由状态显示不同的样式：

- 当前页面的按钮会高亮显示（primary 类型）
- 其他页面的按钮显示默认样式

## 文件结构

```
src/pages/chat/
├── index.tsx                    # 主聊天页面（包含路由出口）
├── components/
│   ├── ChatWindow.tsx          # 聊天窗口组件
│   ├── ChatList.tsx            # 聊天列表组件（包含导航功能）
│   └── ...                     # 其他组件
├── task/
│   └── [sessionid].tsx        # 任务会话页面
├── expert/
│   └── [id].tsx               # 专家详情页面
└── ...                        # 其他文件
```

## 使用方法

### 访问不同页面

1. 访问 `/chat` 查看默认聊天界面
2. 点击侧边栏的"任务会话"按钮跳转到任务页面
3. 点击侧边栏的"专家咨询"下拉菜单选择专家

### 任务会话页面

- URL 格式：`/chat/task/:sessionid`
- 显示指定会话的详细信息
- 支持任务相关的消息交互

### 专家咨询页面

- URL 格式：`/chat/expert/:id`
- 显示专家的详细信息
- 支持开始咨询功能

## 技术实现

### 路由配置

- 使用 React Router 的嵌套路由功能
- 主页面作为布局组件，包含 `<Outlet />` 作为子路由出口
- 支持动态路由参数（sessionid、expert id）

### 状态管理

- 继续使用现有的 MobX store 管理聊天状态
- 路由状态通过 `useLocation` 和 `useNavigate` 管理

### 样式设计

- 保持与现有设计风格一致
- 导航按钮支持状态高亮
- 响应式设计支持移动端
