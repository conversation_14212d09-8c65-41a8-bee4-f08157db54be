import React, { useState, useCallback } from "react";
import { Layout, Typography, Button, Space, Dropdown, message } from "antd";
import {
  SettingOutlined,
  DeleteOutlined,
  ExportOutlined,
  MoreOutlined,
  EditOutlined,
  ShareAltOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { MessageList } from "./MessageList";
import { MessageInput } from "./MessageInput";
import { Settings } from "./Settings";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { downloadAs, getMessageTextContent } from "../utils/message";
import { defaultChatClient } from "../api/client";

const { Header, Content } = Layout;
const { Title } = Typography;

interface ChatWindowProps {
  onOpenSettings?: () => void;
}

export const ChatWindow: React.FC<ChatWindowProps> = observer(
  ({ onOpenSettings }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();
    const [sending, setSending] = useState(false);
    const [settingsVisible, setSettingsVisible] = useState(false);

    const currentSession = chatStore.currentSession;

    const handleSendMessage = useCallback(
      async (content: string, attachImages?: string[]) => {
        if (sending) return;

        setSending(true);

        try {
          // 添加用户消息
          chatStore.onUserInput(content, attachImages);

          // 获取对话历史
          const messages = chatStore.getMessagesWithMemory();

          // 创建AI回复消息
          const botMessage = chatStore.onBotMessage("", true);

          try {
            // 调用AI API进行流式回复
            let fullResponse = "";
            let isFirstChunk = true;

            for await (const chunk of defaultChatClient.streamChat(messages)) {
              fullResponse = chunk;

              // 更新消息内容
              chatStore.updateMessage(
                chatStore.currentSessionIndex,
                currentSession.messages.length - 1,
                (message) => {
                  if (message) {
                    message.content = fullResponse;
                    message.streaming = true;

                    // 第一个chunk时，添加一个小延迟来显示思考过程
                    if (isFirstChunk) {
                      isFirstChunk = false;
                    }
                  }
                }
              );

              // 添加小延迟，让用户能看到流式效果
              await new Promise((resolve) => setTimeout(resolve, 50));
            }

            // 完成流式回复
            chatStore.updateMessage(
              chatStore.currentSessionIndex,
              currentSession.messages.length - 1,
              (message) => {
                if (message) {
                  message.streaming = false;
                }
              }
            );

            // 如果启用了自动生成标题且这是第一次对话
            if (
              configStore.enableAutoGenerateTitle &&
              currentSession.messages.length === 2 &&
              currentSession.topic === "新的聊天"
            ) {
              try {
                const title = await defaultChatClient.generateTitle(messages);
                chatStore.updateMessage(
                  chatStore.currentSessionIndex,
                  -1,
                  () => {
                    currentSession.topic = title;
                  }
                );
              } catch (error) {
                console.warn("生成标题失败:", error);
              }
            }
          } catch (apiError) {
            console.error("AI回复失败:", apiError);

            // 标记消息为错误状态
            chatStore.updateMessage(
              chatStore.currentSessionIndex,
              currentSession.messages.length - 1,
              (message) => {
                if (message) {
                  message.content =
                    "抱歉，我现在无法回复您的消息。请稍后重试。";
                  message.isError = true;
                  message.streaming = false;
                }
              }
            );

            message.error("AI回复失败，请重试");
          }
        } catch (error) {
          console.error("发送消息失败:", error);
          message.error("发送消息失败，请重试");
        } finally {
          setSending(false);
        }
      },
      [chatStore, configStore, currentSession, sending]
    );

    const handleDeleteMessage = useCallback(
      (messageId: string) => {
        const sessionIndex = chatStore.currentSessionIndex;
        const messageIndex = currentSession.messages.findIndex(
          (m) => m.id === messageId
        );

        if (messageIndex >= 0) {
          chatStore.updateMessage(sessionIndex, messageIndex, () => {
            currentSession.messages.splice(messageIndex, 1);
          });
          message.success("消息已删除");
        }
      },
      [chatStore, currentSession]
    );

    const handleClearSession = useCallback(() => {
      chatStore.resetSession();
      message.success("会话已清空");
    }, [chatStore]);

    const handleExportSession = useCallback(() => {
      const content = currentSession.messages
        .map((msg) => {
          const role = msg.role === "user" ? "用户" : "AI助手";
          const textContent =
            typeof msg.content === "string"
              ? msg.content
              : msg.content
                  .filter((c) => c.type === "text")
                  .map((c) => c.text)
                  .join("");
          return `${role} (${msg.date}):\n${textContent}\n`;
        })
        .join("\n---\n\n");

      const filename = `聊天记录_${
        currentSession.topic
      }_${new Date().toLocaleDateString()}.txt`;
      downloadAs(content, filename);
      message.success("聊天记录已导出");
    }, [currentSession]);

    const headerMenuItems = [
      {
        key: "export",
        label: "导出聊天记录",
        icon: <ExportOutlined />,
        onClick: handleExportSession,
      },
      {
        key: "clear",
        label: "清空会话",
        icon: <DeleteOutlined />,
        onClick: handleClearSession,
        danger: true,
      },
      {
        key: "settings",
        label: "设置",
        icon: <SettingOutlined />,
        onClick: () => setSettingsVisible(true),
      },
    ];

    return (
      <>
        <Layout style={{ height: "100%", backgroundColor: "#fff" }}>
          {/* 聊天头部 */}
          <Header
            style={{
              backgroundColor: "#fff",
              borderBottom: "1px solid #f0f0f0",
              padding: "0 16px",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              height: "64px",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <Title
                level={4}
                style={{
                  margin: 0,
                  fontSize: configStore.fontSize * 1.2,
                  color: "#333",
                }}
              >
                {currentSession.topic}
              </Title>

              {currentSession.messages.length > 0 && (
                <span
                  style={{
                    fontSize: configStore.fontSize * 0.8,
                    color: "#999",
                  }}
                >
                  {currentSession.messages.length} 条消息
                </span>
              )}
            </div>

            <Space>
              <Dropdown
                menu={{ items: headerMenuItems }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  icon={<MoreOutlined />}
                  style={{ fontSize: "16px" }}
                />
              </Dropdown>
            </Space>
          </Header>

          {/* 消息列表 */}
          <Content
            style={{
              display: "flex",
              flexDirection: "column",
              height: "calc(100% - 64px)",
              overflow: "hidden",
            }}
          >
            <MessageList
              messages={currentSession.messages}
              onDeleteMessage={handleDeleteMessage}
              loading={sending}
            />

            {/* 消息输入框 */}
            <MessageInput
              onSend={handleSendMessage}
              disabled={sending}
              placeholder={sending ? "AI正在思考中..." : "输入消息..."}
            />
          </Content>
        </Layout>

        {/* 设置弹窗 */}
        <Settings
          visible={settingsVisible}
          onClose={() => setSettingsVisible(false)}
        />
      </>
    );
  }
);
