/* 聊天界面样式 */

/* 消息项悬停效果 */
.message-item {
  transition: all 0.2s ease;
     .message-actions {

      display: flex;
       margin-top: 4px;
              opacity: 0;
              transition: opacity 0.2s;
    }
  &:hover {
    .message-actions {
      opacity: 1 !important;
      display: flex;
    }
  }
}

/* 聊天列表项样式 */
.chat-list-item {
  transition: all 0.2s ease !important;
  border-left: 3px solid transparent !important;
  position: relative;

  &:hover {
    background-color: #f5f5f5 !important;
    transform: translateX(2px);
  }

  &.active {
    background-color: #e6f7ff !important;
    border-left: 3px solid #1890ff !important;
    border-radius: 0 8px 8px 0 !important;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
    margin: 2px 0 !important;
    position: relative !important;

    &:hover {
      background-color: #e6f7ff !important;
      transform: translateX(0);
    }

    /* 添加更明显的选中指示器 */
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: #1890ff;
      z-index: 10;
    }
  }
}

/* 全局样式覆盖，确保样式生效 */
:global(.ant-list-item.chat-list-item.active) {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff !important;
  border-radius: 0 8px 8px 0 !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
}


/* 用户消息气泡 */
.user-bubble {
  
  color: white;
  box-shadow: 0 2px 8px rgba(98, 98, 98, 0.2);
  

}

/* AI消息气泡 */
.ai-bubble {
  background: #ffffff;
  color: #333;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  /* 流式回复时的动画效果 */
  &.streaming {
    animation: bubbleGrow 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
               streamingPulse 2s ease-in-out infinite;
    transform-origin: left center;

    /* 流式回复时的特殊样式 */
    border-color: rgba(82, 196, 26, 0.3);
  }

  /* 内容变化时的平滑过渡 */
  transition: max-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              min-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s ease-out;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -9px;
    width: 0;
    height: 0;
    border-right: 8px solid #e8e8e8;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    z-index: -1;
  }
}

/* 消息列表滚动条样式 */
.message-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

/* 输入框动画 */
.message-input {
  transition: all 0.3s ease;
  
  &:focus-within {
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
    border-color: #40a9ff;
  }
}

/* 加载动画 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #999;
    animation: typing 1.4s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 气泡增长动画 */
@keyframes bubbleGrow {
  0% {
    transform: scaleX(0.1) scaleY(0.8);
    opacity: 0.6;
  }
  30% {
    transform: scaleX(0.6) scaleY(0.95);
    opacity: 0.8;
  }
  70% {
    transform: scaleX(1.02) scaleY(1.02);
    opacity: 0.95;
  }
  100% {
    transform: scaleX(1) scaleY(1);
    opacity: 1;
  }
}

/* 流式回复时的脉动效果 */
@keyframes streamingPulse {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow: 0 2px 12px rgba(82, 196, 26, 0.15);
  }
}

/* 流式文字出现动画 */
@keyframes textAppear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息时间戳 */
.message-timestamp {
  font-size: 0.75em;
  color: #999;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  
  .message-item:hover & {
    opacity: 1;
  }
}

/* 代码块优化 */
.markdown-content {
  pre {
    position: relative;
    
    &:hover::after {
      content: '复制';
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
    }
  }
  
  /* 表格样式优化 */
  table {
    border-collapse: collapse;
    margin: 12px 0;
    
    th, td {
      border: 1px solid rgba(0, 0, 0, 0.1);
      padding: 8px 12px;
      text-align: left;
    }
    
    th {
      background-color: rgba(0, 0, 0, 0.05);
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
  
  /* 引用块样式 */
  blockquote {
    border-left: 4px solid #1890ff;
    background-color: rgba(24, 144, 255, 0.05);
    margin: 12px 0;
    padding: 12px 16px;
    border-radius: 0 6px 6px 0;
    
    p {
      margin: 0;
    }
  }
  
  /* 列表样式 */
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
    
    li {
      margin: 4px 0;
      line-height: 1.5;
    }
  }
}

/* 响应式设计 */


@media (min-width: 769px) {
  .user-bubble,
  .ai-bubble {
    max-width: 90% !important; /* 桌面端更宽 */
  min-width: 50px !important;
  }
}

@media (min-width: 1200px) {
  .user-bubble,
  .ai-bubble {
    max-width: 85% !important; /* 大屏幕适中宽度 */
    min-width: 50px !important;
  }
}

/* 确保内容区域充分利用空间 */
.message-content {
  width: 100%;

  .markdown-content {
    width: 100%;

    pre {
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
    }

    table {
      width: 100%;
      max-width: 100%;
    }
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .ai-bubble {
    background: #2f2f2f;
    color: #e6e6e6;
    border-color: #404040;
    
    &::before {
      border-right-color: #2f2f2f;
    }
    
    &::after {
      border-right-color: #404040;
    }
  }
  
  .markdown-content {
    pre {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
    
    code {
      background-color: rgba(255, 255, 255, 0.15) !important;
    }
    
    blockquote {
      background-color: rgba(24, 144, 255, 0.1);
    }
    
    table {
      th {
        background-color: rgba(255, 255, 255, 0.1);
      }
      
      tr:nth-child(even) {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }
}
