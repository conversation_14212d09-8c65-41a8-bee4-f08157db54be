import React, { useState, useRef, useCallback, useEffect } from "react";
import { Input, Button, Upload, message, Tooltip, Space } from "antd";
import {
  SendOutlined,
  PictureOutlined,
  SmileOutlined,
  PaperClipOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import EmojiPicker from "emoji-picker-react";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { useSubmitHandler, useClickOutside } from "../utils/hooks";
import { compressImage, isImageFile, formatFileSize } from "../utils/message";

const { TextArea } = Input;

interface MessageInputProps {
  onSend: (content: string, attachImages?: string[]) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const MessageInput: React.FC<MessageInputProps> = observer(
  ({ onSend, disabled = false, placeholder = "输入消息..." }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();

    const [input, setInput] = useState("");
    const [attachedImages, setAttachedImages] = useState<string[]>([]);
    const [showEmojiPicker, setShowEmojiPicker] = useState(false);
    const [uploading, setUploading] = useState(false);

    const textareaRef = useRef<any>(null);
    const emojiPickerRef = useRef<HTMLDivElement>(null);

    const { shouldSubmit, onCompositionStart, onCompositionEnd } =
      useSubmitHandler();

    useClickOutside(emojiPickerRef, () => setShowEmojiPicker(false));

    const handleSend = useCallback(() => {
      if (!input.trim() && attachedImages.length === 0) return;
      if (disabled) return;

      onSend(
        input.trim(),
        attachedImages.length > 0 ? attachedImages : undefined
      );
      setInput("");
      setAttachedImages([]);

      // 重置文本框高度
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }, [input, attachedImages, onSend, disabled]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (shouldSubmit(e)) {
          e.preventDefault();
          handleSend();
        }
      },
      [shouldSubmit, handleSend]
    );

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setInput(e.target.value);

        // 自动调整高度
        const textarea = e.target;
        textarea.style.height = "auto";
        const newHeight = Math.min(textarea.scrollHeight, 120); // 最大高度120px
        textarea.style.height = newHeight + "px";
      },
      []
    );

    const handleEmojiClick = useCallback((emojiData: any) => {
      setInput((prev) => prev + emojiData.emoji);
      setShowEmojiPicker(false);
      textareaRef.current?.focus();
    }, []);

    const handleImageUpload = useCallback(async (file: File) => {
      if (!isImageFile(file)) {
        message.error("只支持图片文件");
        return false;
      }

      if (file.size > 10 * 1024 * 1024) {
        // 10MB限制
        message.error("图片大小不能超过10MB");
        return false;
      }

      setUploading(true);

      try {
        const compressedImage = await compressImage(file, 1024);
        setAttachedImages((prev) => [...prev, compressedImage]);
        message.success("图片上传成功");
      } catch (error) {
        console.error("图片压缩失败:", error);
        message.error("图片处理失败");
      } finally {
        setUploading(false);
      }

      return false; // 阻止默认上传行为
    }, []);

    const removeImage = useCallback((index: number) => {
      setAttachedImages((prev) => prev.filter((_, i) => i !== index));
    }, []);

    // 自动聚焦
    useEffect(() => {
      if (textareaRef.current && !disabled) {
        textareaRef.current.focus();
      }
    }, [disabled]);

    return (
      <div
        className="message-input-container"
        style={{ padding: "16px", borderTop: "1px solid #f0f0f0" }}
      >
        {/* 附加的图片预览 */}
        {attachedImages.length > 0 && (
          <div style={{ marginBottom: "12px" }}>
            <Space wrap>
              {attachedImages.map((image, index) => (
                <div
                  key={index}
                  style={{
                    position: "relative",
                    display: "inline-block",
                    border: "1px solid #d9d9d9",
                    borderRadius: "6px",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={image}
                    alt={`附件 ${index + 1}`}
                    style={{
                      width: "60px",
                      height: "60px",
                      objectFit: "cover",
                      display: "block",
                    }}
                  />
                  <Button
                    type="text"
                    size="small"
                    danger
                    onClick={() => removeImage(index)}
                    style={{
                      position: "absolute",
                      top: "2px",
                      right: "2px",
                      minWidth: "20px",
                      height: "20px",
                      padding: 0,
                      fontSize: "12px",
                      backgroundColor: "rgba(0,0,0,0.5)",
                      color: "white",
                      borderRadius: "50%",
                    }}
                  >
                    ×
                  </Button>
                </div>
              ))}
            </Space>
          </div>
        )}

        <div style={{ background: "#fff" }}>
          <div>
            <TextArea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onCompositionStart={onCompositionStart}
              onCompositionEnd={onCompositionEnd}
              placeholder={placeholder}
              disabled={disabled}
              autoSize={{ minRows: 3, maxRows: 30 }}
              style={{
                resize: "none",
                fontSize: configStore.fontSize,
              }}
            />

            {/* Emoji选择器 */}
            {showEmojiPicker && (
              <div
                ref={emojiPickerRef}
                style={{
                  position: "absolute",
                  bottom: "100%",
                  right: 0,
                  zIndex: 1000,
                  marginBottom: "8px",
                }}
              >
                <EmojiPicker
                  onEmojiClick={handleEmojiClick}
                  width={300}
                  height={400}
                />
              </div>
            )}
          </div>

          <Space>
            {/* 图片上传 */}
            <Upload
              beforeUpload={handleImageUpload}
              showUploadList={false}
              accept="image/*"
              disabled={disabled || uploading}
            >
              <Tooltip title="上传图片">
                <Button
                  type="text"
                  icon={<PictureOutlined />}
                  loading={uploading}
                  disabled={disabled}
                />
              </Tooltip>
            </Upload>

            {/* Emoji选择器触发按钮 */}
            <Tooltip title="表情">
              <Button
                type="text"
                icon={<SmileOutlined />}
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                disabled={disabled}
              />
            </Tooltip>

            {/* 发送按钮 */}
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSend}
              disabled={
                disabled || (!input.trim() && attachedImages.length === 0)
              }
            >
              发送
            </Button>
          </Space>
        </div>

        {/* 提示信息 */}
        {/* <div
          style={{
            marginTop: "8px",
            fontSize: "12px",
            color: "#999",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span>
            {configStore.submitKey === "Enter"
              ? "Enter发送，Shift+Enter换行"
              : "Ctrl+Enter发送"}
          </span>
          {input.length > 0 && <span>{input.length} 字符</span>}
        </div> */}
      </div>
    );
  }
);
