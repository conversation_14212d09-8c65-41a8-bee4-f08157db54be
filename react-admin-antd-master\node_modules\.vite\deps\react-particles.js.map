{"version": 3, "sources": ["../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/Particles.js", "../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/Utils.js", "../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/index.js"], "sourcesContent": ["import React, { Component } from \"react\";\nimport { tsParticles } from \"tsparticles-engine\";\nimport { deepCompare } from \"./Utils\";\nconst defaultId = \"tsparticles\";\nclass Particles extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            init: false,\n            library: undefined,\n        };\n    }\n    destroy() {\n        if (!this.state.library) {\n            return;\n        }\n        this.state.library.destroy();\n        this.setState({\n            library: undefined,\n        });\n    }\n    shouldComponentUpdate(nextProps) {\n        const nextOptions = nextProps.options ?? nextProps.params, currentOptions = this.props.options ?? this.props.params;\n        return (nextProps.url !== this.props.url ||\n            nextProps.id !== this.props.id ||\n            nextProps.canvasClassName !== this.props.canvasClassName ||\n            nextProps.className !== this.props.className ||\n            nextProps.height !== this.props.height ||\n            nextProps.width !== this.props.width ||\n            !deepCompare(nextProps.style, this.props.style) ||\n            nextProps.init !== this.props.init ||\n            nextProps.loaded !== this.props.loaded ||\n            !deepCompare(nextOptions, currentOptions, key => key.startsWith(\"_\")));\n    }\n    componentDidUpdate() {\n        this.refresh();\n    }\n    forceUpdate() {\n        this.refresh().then(() => {\n            super.forceUpdate();\n        });\n    }\n    componentDidMount() {\n        (async () => {\n            if (this.props.init) {\n                await this.props.init(tsParticles);\n            }\n            this.setState({\n                init: true,\n            }, async () => {\n                await this.loadParticles();\n            });\n        })();\n    }\n    componentWillUnmount() {\n        this.destroy();\n    }\n    render() {\n        const { width, height, className, canvasClassName, id } = this.props;\n        return (React.createElement(\"div\", { className: className, id: id },\n            React.createElement(\"canvas\", { className: canvasClassName, style: {\n                    ...this.props.style,\n                    width,\n                    height,\n                } })));\n    }\n    async refresh() {\n        this.destroy();\n        await this.loadParticles();\n    }\n    async loadParticles() {\n        if (!this.state.init) {\n            return;\n        }\n        const id = this.props.id ?? Particles.defaultProps.id ?? defaultId, container = await tsParticles.load({\n            url: this.props.url,\n            id,\n            options: this.props.options ?? this.props.params,\n        });\n        if (this.props.container) {\n            this.props.container.current = container;\n        }\n        this.setState({\n            library: container,\n        });\n        if (this.props.loaded) {\n            await this.props.loaded(container);\n        }\n    }\n}\nParticles.defaultProps = {\n    width: \"100%\",\n    height: \"100%\",\n    options: {},\n    style: {},\n    url: undefined,\n    id: defaultId,\n};\nexport default Particles;\n", "const isObject = (val) => typeof val === \"object\" && val !== null;\nexport function deepCompare(obj1, obj2, excludeKeyFn = () => false) {\n    if (!isObject(obj1) || !isObject(obj2)) {\n        return obj1 === obj2;\n    }\n    const keys1 = Object.keys(obj1).filter(key => !excludeKeyFn(key)), keys2 = Object.keys(obj2).filter(key => !excludeKeyFn(key));\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const value1 = obj1[key], value2 = obj2[key];\n        if (isObject(value1) && isObject(value2)) {\n            if (value1 === obj2 && value2 === obj1) {\n                continue;\n            }\n            if (!deepCompare(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (Array.isArray(value1) && Array.isArray(value2)) {\n            if (!deepCompareArrays(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (value1 !== value2) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction deepCompareArrays(arr1, arr2, excludeKeyFn) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (let i = 0; i < arr1.length; i++) {\n        const val1 = arr1[i], val2 = arr2[i];\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n            if (!deepCompareArrays(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (isObject(val1) && isObject(val2)) {\n            if (!deepCompare(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (val1 !== val2) {\n            return false;\n        }\n    }\n    return true;\n}\n", "import Particles from \"./Particles\";\nexport default Particles;\nexport { Particles };\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAiC;;;ACAjC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,QAAQ;AACtD,SAAS,YAAY,MAAM,MAAM,eAAe,MAAM,OAAO;AAChE,MAAI,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,IAAI,GAAG;AACpC,WAAO,SAAS;AAAA,EACpB;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI,EAAE,OAAO,SAAO,CAAC,aAAa,GAAG,CAAC,GAAG,QAAQ,OAAO,KAAK,IAAI,EAAE,OAAO,SAAO,CAAC,aAAa,GAAG,CAAC;AAC7H,MAAI,MAAM,WAAW,MAAM,QAAQ;AAC/B,WAAO;AAAA,EACX;AACA,aAAW,OAAO,OAAO;AACrB,UAAM,SAAS,KAAK,GAAG,GAAG,SAAS,KAAK,GAAG;AAC3C,QAAI,SAAS,MAAM,KAAK,SAAS,MAAM,GAAG;AACtC,UAAI,WAAW,QAAQ,WAAW,MAAM;AACpC;AAAA,MACJ;AACA,UAAI,CAAC,YAAY,QAAQ,QAAQ,YAAY,GAAG;AAC5C,eAAO;AAAA,MACX;AAAA,IACJ,WACS,MAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAI,CAAC,kBAAkB,QAAQ,QAAQ,YAAY,GAAG;AAClD,eAAO;AAAA,MACX;AAAA,IACJ,WACS,WAAW,QAAQ;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,MAAM,cAAc;AACjD,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAM,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC;AACnC,QAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG;AAC5C,UAAI,CAAC,kBAAkB,MAAM,MAAM,YAAY,GAAG;AAC9C,eAAO;AAAA,MACX;AAAA,IACJ,WACS,SAAS,IAAI,KAAK,SAAS,IAAI,GAAG;AACvC,UAAI,CAAC,YAAY,MAAM,MAAM,YAAY,GAAG;AACxC,eAAO;AAAA,MACX;AAAA,IACJ,WACS,SAAS,MAAM;AACpB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ADhDA,IAAM,YAAY;AAClB,IAAM,YAAN,MAAM,mBAAkB,uBAAU;AAAA,EAC9B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM,SAAS;AACrB;AAAA,IACJ;AACA,SAAK,MAAM,QAAQ,QAAQ;AAC3B,SAAK,SAAS;AAAA,MACV,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB,WAAW;AAC7B,UAAM,cAAc,UAAU,WAAW,UAAU,QAAQ,iBAAiB,KAAK,MAAM,WAAW,KAAK,MAAM;AAC7G,WAAQ,UAAU,QAAQ,KAAK,MAAM,OACjC,UAAU,OAAO,KAAK,MAAM,MAC5B,UAAU,oBAAoB,KAAK,MAAM,mBACzC,UAAU,cAAc,KAAK,MAAM,aACnC,UAAU,WAAW,KAAK,MAAM,UAChC,UAAU,UAAU,KAAK,MAAM,SAC/B,CAAC,YAAY,UAAU,OAAO,KAAK,MAAM,KAAK,KAC9C,UAAU,SAAS,KAAK,MAAM,QAC9B,UAAU,WAAW,KAAK,MAAM,UAChC,CAAC,YAAY,aAAa,gBAAgB,SAAO,IAAI,WAAW,GAAG,CAAC;AAAA,EAC5E;AAAA,EACA,qBAAqB;AACjB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,cAAc;AACV,SAAK,QAAQ,EAAE,KAAK,MAAM;AACtB,YAAM,YAAY;AAAA,IACtB,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,KAAC,YAAY;AACT,UAAI,KAAK,MAAM,MAAM;AACjB,cAAM,KAAK,MAAM,KAAK,WAAW;AAAA,MACrC;AACA,WAAK,SAAS;AAAA,QACV,MAAM;AAAA,MACV,GAAG,YAAY;AACX,cAAM,KAAK,cAAc;AAAA,MAC7B,CAAC;AAAA,IACL,GAAG;AAAA,EACP;AAAA,EACA,uBAAuB;AACnB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,QAAQ,WAAW,iBAAiB,GAAG,IAAI,KAAK;AAC/D,WAAQ,aAAAA,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,WAAsB,GAAO;AAAA,MAC9D,aAAAA,QAAM,cAAc,UAAU,EAAE,WAAW,iBAAiB,OAAO;AAAA,QAC3D,GAAG,KAAK,MAAM;AAAA,QACd;AAAA,QACA;AAAA,MACJ,EAAE,CAAC;AAAA,IAAC;AAAA,EAChB;AAAA,EACA,MAAM,UAAU;AACZ,SAAK,QAAQ;AACb,UAAM,KAAK,cAAc;AAAA,EAC7B;AAAA,EACA,MAAM,gBAAgB;AAClB,QAAI,CAAC,KAAK,MAAM,MAAM;AAClB;AAAA,IACJ;AACA,UAAM,KAAK,KAAK,MAAM,MAAM,WAAU,aAAa,MAAM,WAAW,YAAY,MAAM,YAAY,KAAK;AAAA,MACnG,KAAK,KAAK,MAAM;AAAA,MAChB;AAAA,MACA,SAAS,KAAK,MAAM,WAAW,KAAK,MAAM;AAAA,IAC9C,CAAC;AACD,QAAI,KAAK,MAAM,WAAW;AACtB,WAAK,MAAM,UAAU,UAAU;AAAA,IACnC;AACA,SAAK,SAAS;AAAA,MACV,SAAS;AAAA,IACb,CAAC;AACD,QAAI,KAAK,MAAM,QAAQ;AACnB,YAAM,KAAK,MAAM,OAAO,SAAS;AAAA,IACrC;AAAA,EACJ;AACJ;AACA,UAAU,eAAe;AAAA,EACrB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,OAAO,CAAC;AAAA,EACR,KAAK;AAAA,EACL,IAAI;AACR;AACA,IAAO,oBAAQ;;;AEjGf,IAAO,cAAQ;", "names": ["React"]}