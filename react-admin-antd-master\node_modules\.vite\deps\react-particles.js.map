{"version": 3, "sources": ["../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/Particles.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Constants.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Vector3d.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Vector.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/NumberUtils.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/Utils.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/ColorUtils.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/CanvasUtils.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Canvas.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/EventListeners.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/OptionsColor.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Background/Background.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/BackgroundMask/BackgroundMaskCover.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/BackgroundMask/BackgroundMask.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/FullScreen/FullScreen.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/ClickEvent.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/DivEvent.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/Parallax.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/HoverEvent.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/ResizeEvent.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Events/Events.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Modes/Modes.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Interactivity/Interactivity.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/ManualParticle.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Responsive.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Theme/ThemeDefault.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Theme/Theme.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/ColorAnimation.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/HslAnimation.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/AnimatableColor.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Collisions/CollisionsAbsorb.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Collisions/CollisionsOverlap.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/AnimationOptions.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Random.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/ValueWithRandom.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Bounce/ParticlesBounceFactor.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Bounce/ParticlesBounce.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Collisions/Collisions.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveAngle.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveAttract.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveCenter.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveGravity.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/Path/MovePath.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveTrailFill.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/MoveTrail.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/OutModes.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/Spin.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Move/Move.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Opacity/OpacityAnimation.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Opacity/Opacity.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Number/ParticlesDensity.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Number/ParticlesNumber.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Shadow.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Shape/Shape.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Size/SizeAnimation.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Size/Size.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/Stroke.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/ZIndex/ZIndex.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Particles/ParticlesOptions.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/OptionsUtils.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Options/Classes/Options.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/InteractionManager.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Particle.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Point.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Range.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Rectangle.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Circle.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/QuadTree.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Particles.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Retina.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Container.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/EventDispatcher.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Utils/Plugins.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Core/Engine.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/HslColorManager.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/Utils/RgbColorManager.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/init.js", "../../.pnpm/tsparticles-engine@2.12.0/node_modules/tsparticles-engine/esm/index.js", "../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/Utils.js", "../../.pnpm/react-particles@2.12.2_react@18.3.1/node_modules/react-particles/esm/index.js"], "sourcesContent": ["import React, { Component } from \"react\";\nimport { tsParticles } from \"tsparticles-engine\";\nimport { deepCompare } from \"./Utils\";\nconst defaultId = \"tsparticles\";\nclass Particles extends Component {\n    constructor(props) {\n        super(props);\n        this.state = {\n            init: false,\n            library: undefined,\n        };\n    }\n    destroy() {\n        if (!this.state.library) {\n            return;\n        }\n        this.state.library.destroy();\n        this.setState({\n            library: undefined,\n        });\n    }\n    shouldComponentUpdate(nextProps) {\n        const nextOptions = nextProps.options ?? nextProps.params, currentOptions = this.props.options ?? this.props.params;\n        return (nextProps.url !== this.props.url ||\n            nextProps.id !== this.props.id ||\n            nextProps.canvasClassName !== this.props.canvasClassName ||\n            nextProps.className !== this.props.className ||\n            nextProps.height !== this.props.height ||\n            nextProps.width !== this.props.width ||\n            !deepCompare(nextProps.style, this.props.style) ||\n            nextProps.init !== this.props.init ||\n            nextProps.loaded !== this.props.loaded ||\n            !deepCompare(nextOptions, currentOptions, key => key.startsWith(\"_\")));\n    }\n    componentDidUpdate() {\n        this.refresh();\n    }\n    forceUpdate() {\n        this.refresh().then(() => {\n            super.forceUpdate();\n        });\n    }\n    componentDidMount() {\n        (async () => {\n            if (this.props.init) {\n                await this.props.init(tsParticles);\n            }\n            this.setState({\n                init: true,\n            }, async () => {\n                await this.loadParticles();\n            });\n        })();\n    }\n    componentWillUnmount() {\n        this.destroy();\n    }\n    render() {\n        const { width, height, className, canvasClassName, id } = this.props;\n        return (React.createElement(\"div\", { className: className, id: id },\n            React.createElement(\"canvas\", { className: canvasClassName, style: {\n                    ...this.props.style,\n                    width,\n                    height,\n                } })));\n    }\n    async refresh() {\n        this.destroy();\n        await this.loadParticles();\n    }\n    async loadParticles() {\n        if (!this.state.init) {\n            return;\n        }\n        const id = this.props.id ?? Particles.defaultProps.id ?? defaultId, container = await tsParticles.load({\n            url: this.props.url,\n            id,\n            options: this.props.options ?? this.props.params,\n        });\n        if (this.props.container) {\n            this.props.container.current = container;\n        }\n        this.setState({\n            library: container,\n        });\n        if (this.props.loaded) {\n            await this.props.loaded(container);\n        }\n    }\n}\nParticles.defaultProps = {\n    width: \"100%\",\n    height: \"100%\",\n    options: {},\n    style: {},\n    url: undefined,\n    id: defaultId,\n};\nexport default Particles;\n", "export const generatedAttribute = \"generated\";\nexport const mouseDownEvent = \"pointerdown\";\nexport const mouseUpEvent = \"pointerup\";\nexport const mouseLeaveEvent = \"pointerleave\";\nexport const mouseOutEvent = \"pointerout\";\nexport const mouseMoveEvent = \"pointermove\";\nexport const touchStartEvent = \"touchstart\";\nexport const touchEndEvent = \"touchend\";\nexport const touchMoveEvent = \"touchmove\";\nexport const touchCancelEvent = \"touchcancel\";\nexport const resizeEvent = \"resize\";\nexport const visibilityChangeEvent = \"visibilitychange\";\nexport const errorPrefix = \"tsParticles - Error\";\n", "import { errorPrefix } from \"./Constants\";\nimport { isNumber } from \"../../Utils/Utils\";\nexport class Vector3d {\n    constructor(xOrCoords, y, z) {\n        this._updateFromAngle = (angle, length) => {\n            this.x = Math.cos(angle) * length;\n            this.y = Math.sin(angle) * length;\n        };\n        if (!isNumber(xOrCoords) && xOrCoords) {\n            this.x = xOrCoords.x;\n            this.y = xOrCoords.y;\n            const coords3d = xOrCoords;\n            this.z = coords3d.z ? coords3d.z : 0;\n        }\n        else if (xOrCoords !== undefined && y !== undefined) {\n            this.x = xOrCoords;\n            this.y = y;\n            this.z = z ?? 0;\n        }\n        else {\n            throw new Error(`${errorPrefix} Vector3d not initialized correctly`);\n        }\n    }\n    static get origin() {\n        return Vector3d.create(0, 0, 0);\n    }\n    get angle() {\n        return Math.atan2(this.y, this.x);\n    }\n    set angle(angle) {\n        this._updateFromAngle(angle, this.length);\n    }\n    get length() {\n        return Math.sqrt(this.getLengthSq());\n    }\n    set length(length) {\n        this._updateFromAngle(this.angle, length);\n    }\n    static clone(source) {\n        return Vector3d.create(source.x, source.y, source.z);\n    }\n    static create(x, y, z) {\n        return new Vector3d(x, y, z);\n    }\n    add(v) {\n        return Vector3d.create(this.x + v.x, this.y + v.y, this.z + v.z);\n    }\n    addTo(v) {\n        this.x += v.x;\n        this.y += v.y;\n        this.z += v.z;\n    }\n    copy() {\n        return Vector3d.clone(this);\n    }\n    distanceTo(v) {\n        return this.sub(v).length;\n    }\n    distanceToSq(v) {\n        return this.sub(v).getLengthSq();\n    }\n    div(n) {\n        return Vector3d.create(this.x / n, this.y / n, this.z / n);\n    }\n    divTo(n) {\n        this.x /= n;\n        this.y /= n;\n        this.z /= n;\n    }\n    getLengthSq() {\n        return this.x ** 2 + this.y ** 2;\n    }\n    mult(n) {\n        return Vector3d.create(this.x * n, this.y * n, this.z * n);\n    }\n    multTo(n) {\n        this.x *= n;\n        this.y *= n;\n        this.z *= n;\n    }\n    normalize() {\n        const length = this.length;\n        if (length != 0) {\n            this.multTo(1.0 / length);\n        }\n    }\n    rotate(angle) {\n        return Vector3d.create(this.x * Math.cos(angle) - this.y * Math.sin(angle), this.x * Math.sin(angle) + this.y * Math.cos(angle), 0);\n    }\n    setTo(c) {\n        this.x = c.x;\n        this.y = c.y;\n        const v3d = c;\n        this.z = v3d.z ? v3d.z : 0;\n    }\n    sub(v) {\n        return Vector3d.create(this.x - v.x, this.y - v.y, this.z - v.z);\n    }\n    subFrom(v) {\n        this.x -= v.x;\n        this.y -= v.y;\n        this.z -= v.z;\n    }\n}\n", "import { Vector3d } from \"./Vector3d\";\nexport class Vector extends Vector3d {\n    constructor(xOrCoords, y) {\n        super(xOrCoords, y, 0);\n    }\n    static get origin() {\n        return Vector.create(0, 0);\n    }\n    static clone(source) {\n        return Vector.create(source.x, source.y);\n    }\n    static create(x, y) {\n        return new Vector(x, y);\n    }\n}\n", "import { isBoolean, isNumber } from \"./Utils\";\nimport { Vector } from \"../Core/Utils/Vector\";\nlet _random = Math.random;\nconst easings = new Map();\nexport function addEasing(name, easing) {\n    if (easings.get(name)) {\n        return;\n    }\n    easings.set(name, easing);\n}\nexport function getEasing(name) {\n    return easings.get(name) || ((value) => value);\n}\nexport function setRandom(rnd = Math.random) {\n    _random = rnd;\n}\nexport function getRandom() {\n    return clamp(_random(), 0, 1 - 1e-16);\n}\nexport function clamp(num, min, max) {\n    return Math.min(Math.max(num, min), max);\n}\nexport function mix(comp1, comp2, weight1, weight2) {\n    return Math.floor((comp1 * weight1 + comp2 * weight2) / (weight1 + weight2));\n}\nexport function randomInRange(r) {\n    const max = getRangeMax(r);\n    let min = getRangeMin(r);\n    if (max === min) {\n        min = 0;\n    }\n    return getRandom() * (max - min) + min;\n}\nexport function getRangeValue(value) {\n    return isNumber(value) ? value : randomInRange(value);\n}\nexport function getRangeMin(value) {\n    return isNumber(value) ? value : value.min;\n}\nexport function getRangeMax(value) {\n    return isNumber(value) ? value : value.max;\n}\nexport function setRangeValue(source, value) {\n    if (source === value || (value === undefined && isNumber(source))) {\n        return source;\n    }\n    const min = getRangeMin(source), max = getRangeMax(source);\n    return value !== undefined\n        ? {\n            min: Math.min(min, value),\n            max: Math.max(max, value),\n        }\n        : setRangeValue(min, max);\n}\nexport function getValue(options) {\n    const random = options.random, { enable, minimumValue } = isBoolean(random)\n        ? {\n            enable: random,\n            minimumValue: 0,\n        }\n        : random;\n    return enable ? getRangeValue(setRangeValue(options.value, minimumValue)) : getRangeValue(options.value);\n}\nexport function getDistances(pointA, pointB) {\n    const dx = pointA.x - pointB.x, dy = pointA.y - pointB.y;\n    return { dx: dx, dy: dy, distance: Math.sqrt(dx ** 2 + dy ** 2) };\n}\nexport function getDistance(pointA, pointB) {\n    return getDistances(pointA, pointB).distance;\n}\nexport function getParticleDirectionAngle(direction, position, center) {\n    if (isNumber(direction)) {\n        return (direction * Math.PI) / 180;\n    }\n    switch (direction) {\n        case \"top\":\n            return -Math.PI / 2;\n        case \"top-right\":\n            return -Math.PI / 4;\n        case \"right\":\n            return 0;\n        case \"bottom-right\":\n            return Math.PI / 4;\n        case \"bottom\":\n            return Math.PI / 2;\n        case \"bottom-left\":\n            return (3 * Math.PI) / 4;\n        case \"left\":\n            return Math.PI;\n        case \"top-left\":\n            return (-3 * Math.PI) / 4;\n        case \"inside\":\n            return Math.atan2(center.y - position.y, center.x - position.x);\n        case \"outside\":\n            return Math.atan2(position.y - center.y, position.x - center.x);\n        default:\n            return getRandom() * Math.PI * 2;\n    }\n}\nexport function getParticleBaseVelocity(direction) {\n    const baseVelocity = Vector.origin;\n    baseVelocity.length = 1;\n    baseVelocity.angle = direction;\n    return baseVelocity;\n}\nexport function collisionVelocity(v1, v2, m1, m2) {\n    return Vector.create((v1.x * (m1 - m2)) / (m1 + m2) + (v2.x * 2 * m2) / (m1 + m2), v1.y);\n}\nexport function calcPositionFromSize(data) {\n    return data.position && data.position.x !== undefined && data.position.y !== undefined\n        ? {\n            x: (data.position.x * data.size.width) / 100,\n            y: (data.position.y * data.size.height) / 100,\n        }\n        : undefined;\n}\nexport function calcPositionOrRandomFromSize(data) {\n    return {\n        x: ((data.position?.x ?? getRandom() * 100) * data.size.width) / 100,\n        y: ((data.position?.y ?? getRandom() * 100) * data.size.height) / 100,\n    };\n}\nexport function calcPositionOrRandomFromSizeRanged(data) {\n    const position = {\n        x: data.position?.x !== undefined ? getRangeValue(data.position.x) : undefined,\n        y: data.position?.y !== undefined ? getRangeValue(data.position.y) : undefined,\n    };\n    return calcPositionOrRandomFromSize({ size: data.size, position });\n}\nexport function calcExactPositionOrRandomFromSize(data) {\n    return {\n        x: data.position?.x ?? getRandom() * data.size.width,\n        y: data.position?.y ?? getRandom() * data.size.height,\n    };\n}\nexport function calcExactPositionOrRandomFromSizeRanged(data) {\n    const position = {\n        x: data.position?.x !== undefined ? getRangeValue(data.position.x) : undefined,\n        y: data.position?.y !== undefined ? getRangeValue(data.position.y) : undefined,\n    };\n    return calcExactPositionOrRandomFromSize({ size: data.size, position });\n}\nexport function parseAlpha(input) {\n    return input ? (input.endsWith(\"%\") ? parseFloat(input) / 100 : parseFloat(input)) : 1;\n}\n", "import { collisionVelocity, getDistances, getRandom, getRangeMax, getRangeMin, getRangeValue, getValue, randomInRange, } from \"./NumberUtils\";\nimport { Vector } from \"../Core/Utils/Vector\";\nconst _logger = {\n    debug: console.debug,\n    error: console.error,\n    info: console.info,\n    log: console.log,\n    verbose: console.log,\n    warning: console.warn,\n};\nexport function setLogger(logger) {\n    _logger.debug = logger.debug || _logger.debug;\n    _logger.error = logger.error || _logger.error;\n    _logger.info = logger.info || _logger.info;\n    _logger.log = logger.log || _logger.log;\n    _logger.verbose = logger.verbose || _logger.verbose;\n    _logger.warning = logger.warning || _logger.warning;\n}\nexport function getLogger() {\n    return _logger;\n}\nfunction rectSideBounce(data) {\n    const res = { bounced: false }, { pSide, pOtherSide, rectSide, rectOtherSide, velocity, factor } = data;\n    if (pOtherSide.min < rectOtherSide.min ||\n        pOtherSide.min > rectOtherSide.max ||\n        pOtherSide.max < rectOtherSide.min ||\n        pOtherSide.max > rectOtherSide.max) {\n        return res;\n    }\n    if ((pSide.max >= rectSide.min && pSide.max <= (rectSide.max + rectSide.min) / 2 && velocity > 0) ||\n        (pSide.min <= rectSide.max && pSide.min > (rectSide.max + rectSide.min) / 2 && velocity < 0)) {\n        res.velocity = velocity * -factor;\n        res.bounced = true;\n    }\n    return res;\n}\nfunction checkSelector(element, selectors) {\n    const res = executeOnSingleOrMultiple(selectors, (selector) => {\n        return element.matches(selector);\n    });\n    return isArray(res) ? res.some((t) => t) : res;\n}\nexport function isSsr() {\n    return typeof window === \"undefined\" || !window || typeof window.document === \"undefined\" || !window.document;\n}\nexport function hasMatchMedia() {\n    return !isSsr() && typeof matchMedia !== \"undefined\";\n}\nexport function safeMatchMedia(query) {\n    if (!hasMatchMedia()) {\n        return;\n    }\n    return matchMedia(query);\n}\nexport function safeMutationObserver(callback) {\n    if (isSsr() || typeof MutationObserver === \"undefined\") {\n        return;\n    }\n    return new MutationObserver(callback);\n}\nexport function isInArray(value, array) {\n    return value === array || (isArray(array) && array.indexOf(value) > -1);\n}\nexport async function loadFont(font, weight) {\n    try {\n        await document.fonts.load(`${weight ?? \"400\"} 36px '${font ?? \"Verdana\"}'`);\n    }\n    catch {\n    }\n}\nexport function arrayRandomIndex(array) {\n    return Math.floor(getRandom() * array.length);\n}\nexport function itemFromArray(array, index, useIndex = true) {\n    return array[index !== undefined && useIndex ? index % array.length : arrayRandomIndex(array)];\n}\nexport function isPointInside(point, size, offset, radius, direction) {\n    return areBoundsInside(calculateBounds(point, radius ?? 0), size, offset, direction);\n}\nexport function areBoundsInside(bounds, size, offset, direction) {\n    let inside = true;\n    if (!direction || direction === \"bottom\") {\n        inside = bounds.top < size.height + offset.x;\n    }\n    if (inside && (!direction || direction === \"left\")) {\n        inside = bounds.right > offset.x;\n    }\n    if (inside && (!direction || direction === \"right\")) {\n        inside = bounds.left < size.width + offset.y;\n    }\n    if (inside && (!direction || direction === \"top\")) {\n        inside = bounds.bottom > offset.y;\n    }\n    return inside;\n}\nexport function calculateBounds(point, radius) {\n    return {\n        bottom: point.y + radius,\n        left: point.x - radius,\n        right: point.x + radius,\n        top: point.y - radius,\n    };\n}\nexport function deepExtend(destination, ...sources) {\n    for (const source of sources) {\n        if (source === undefined || source === null) {\n            continue;\n        }\n        if (!isObject(source)) {\n            destination = source;\n            continue;\n        }\n        const sourceIsArray = Array.isArray(source);\n        if (sourceIsArray && (isObject(destination) || !destination || !Array.isArray(destination))) {\n            destination = [];\n        }\n        else if (!sourceIsArray && (isObject(destination) || !destination || Array.isArray(destination))) {\n            destination = {};\n        }\n        for (const key in source) {\n            if (key === \"__proto__\") {\n                continue;\n            }\n            const sourceDict = source, value = sourceDict[key], destDict = destination;\n            destDict[key] =\n                isObject(value) && Array.isArray(value)\n                    ? value.map((v) => deepExtend(destDict[key], v))\n                    : deepExtend(destDict[key], value);\n        }\n    }\n    return destination;\n}\nexport function isDivModeEnabled(mode, divs) {\n    return !!findItemFromSingleOrMultiple(divs, (t) => t.enable && isInArray(mode, t.mode));\n}\nexport function divModeExecute(mode, divs, callback) {\n    executeOnSingleOrMultiple(divs, (div) => {\n        const divMode = div.mode, divEnabled = div.enable;\n        if (divEnabled && isInArray(mode, divMode)) {\n            singleDivModeExecute(div, callback);\n        }\n    });\n}\nexport function singleDivModeExecute(div, callback) {\n    const selectors = div.selectors;\n    executeOnSingleOrMultiple(selectors, (selector) => {\n        callback(selector, div);\n    });\n}\nexport function divMode(divs, element) {\n    if (!element || !divs) {\n        return;\n    }\n    return findItemFromSingleOrMultiple(divs, (div) => {\n        return checkSelector(element, div.selectors);\n    });\n}\nexport function circleBounceDataFromParticle(p) {\n    return {\n        position: p.getPosition(),\n        radius: p.getRadius(),\n        mass: p.getMass(),\n        velocity: p.velocity,\n        factor: Vector.create(getValue(p.options.bounce.horizontal), getValue(p.options.bounce.vertical)),\n    };\n}\nexport function circleBounce(p1, p2) {\n    const { x: xVelocityDiff, y: yVelocityDiff } = p1.velocity.sub(p2.velocity), [pos1, pos2] = [p1.position, p2.position], { dx: xDist, dy: yDist } = getDistances(pos2, pos1);\n    if (xVelocityDiff * xDist + yVelocityDiff * yDist < 0) {\n        return;\n    }\n    const angle = -Math.atan2(yDist, xDist), m1 = p1.mass, m2 = p2.mass, u1 = p1.velocity.rotate(angle), u2 = p2.velocity.rotate(angle), v1 = collisionVelocity(u1, u2, m1, m2), v2 = collisionVelocity(u2, u1, m1, m2), vFinal1 = v1.rotate(-angle), vFinal2 = v2.rotate(-angle);\n    p1.velocity.x = vFinal1.x * p1.factor.x;\n    p1.velocity.y = vFinal1.y * p1.factor.y;\n    p2.velocity.x = vFinal2.x * p2.factor.x;\n    p2.velocity.y = vFinal2.y * p2.factor.y;\n}\nexport function rectBounce(particle, divBounds) {\n    const pPos = particle.getPosition(), size = particle.getRadius(), bounds = calculateBounds(pPos, size), resH = rectSideBounce({\n        pSide: {\n            min: bounds.left,\n            max: bounds.right,\n        },\n        pOtherSide: {\n            min: bounds.top,\n            max: bounds.bottom,\n        },\n        rectSide: {\n            min: divBounds.left,\n            max: divBounds.right,\n        },\n        rectOtherSide: {\n            min: divBounds.top,\n            max: divBounds.bottom,\n        },\n        velocity: particle.velocity.x,\n        factor: getValue(particle.options.bounce.horizontal),\n    });\n    if (resH.bounced) {\n        if (resH.velocity !== undefined) {\n            particle.velocity.x = resH.velocity;\n        }\n        if (resH.position !== undefined) {\n            particle.position.x = resH.position;\n        }\n    }\n    const resV = rectSideBounce({\n        pSide: {\n            min: bounds.top,\n            max: bounds.bottom,\n        },\n        pOtherSide: {\n            min: bounds.left,\n            max: bounds.right,\n        },\n        rectSide: {\n            min: divBounds.top,\n            max: divBounds.bottom,\n        },\n        rectOtherSide: {\n            min: divBounds.left,\n            max: divBounds.right,\n        },\n        velocity: particle.velocity.y,\n        factor: getValue(particle.options.bounce.vertical),\n    });\n    if (resV.bounced) {\n        if (resV.velocity !== undefined) {\n            particle.velocity.y = resV.velocity;\n        }\n        if (resV.position !== undefined) {\n            particle.position.y = resV.position;\n        }\n    }\n}\nexport function executeOnSingleOrMultiple(obj, callback) {\n    return isArray(obj) ? obj.map((item, index) => callback(item, index)) : callback(obj, 0);\n}\nexport function itemFromSingleOrMultiple(obj, index, useIndex) {\n    return isArray(obj) ? itemFromArray(obj, index, useIndex) : obj;\n}\nexport function findItemFromSingleOrMultiple(obj, callback) {\n    return isArray(obj) ? obj.find((t, index) => callback(t, index)) : callback(obj, 0) ? obj : undefined;\n}\nexport function initParticleNumericAnimationValue(options, pxRatio) {\n    const valueRange = options.value, animationOptions = options.animation, res = {\n        delayTime: getRangeValue(animationOptions.delay) * 1000,\n        enable: animationOptions.enable,\n        value: getRangeValue(options.value) * pxRatio,\n        max: getRangeMax(valueRange) * pxRatio,\n        min: getRangeMin(valueRange) * pxRatio,\n        loops: 0,\n        maxLoops: getRangeValue(animationOptions.count),\n        time: 0,\n    };\n    if (animationOptions.enable) {\n        res.decay = 1 - getRangeValue(animationOptions.decay);\n        switch (animationOptions.mode) {\n            case \"increase\":\n                res.status = \"increasing\";\n                break;\n            case \"decrease\":\n                res.status = \"decreasing\";\n                break;\n            case \"random\":\n                res.status = getRandom() >= 0.5 ? \"increasing\" : \"decreasing\";\n                break;\n        }\n        const autoStatus = animationOptions.mode === \"auto\";\n        switch (animationOptions.startValue) {\n            case \"min\":\n                res.value = res.min;\n                if (autoStatus) {\n                    res.status = \"increasing\";\n                }\n                break;\n            case \"max\":\n                res.value = res.max;\n                if (autoStatus) {\n                    res.status = \"decreasing\";\n                }\n                break;\n            case \"random\":\n            default:\n                res.value = randomInRange(res);\n                if (autoStatus) {\n                    res.status = getRandom() >= 0.5 ? \"increasing\" : \"decreasing\";\n                }\n                break;\n        }\n    }\n    res.initialValue = res.value;\n    return res;\n}\nfunction getPositionOrSize(positionOrSize, canvasSize) {\n    const isPercent = positionOrSize.mode === \"percent\";\n    if (!isPercent) {\n        const { mode: _, ...rest } = positionOrSize;\n        return rest;\n    }\n    const isPosition = \"x\" in positionOrSize;\n    if (isPosition) {\n        return {\n            x: (positionOrSize.x / 100) * canvasSize.width,\n            y: (positionOrSize.y / 100) * canvasSize.height,\n        };\n    }\n    else {\n        return {\n            width: (positionOrSize.width / 100) * canvasSize.width,\n            height: (positionOrSize.height / 100) * canvasSize.height,\n        };\n    }\n}\nexport function getPosition(position, canvasSize) {\n    return getPositionOrSize(position, canvasSize);\n}\nexport function getSize(size, canvasSize) {\n    return getPositionOrSize(size, canvasSize);\n}\nexport function isBoolean(arg) {\n    return typeof arg === \"boolean\";\n}\nexport function isString(arg) {\n    return typeof arg === \"string\";\n}\nexport function isNumber(arg) {\n    return typeof arg === \"number\";\n}\nexport function isFunction(arg) {\n    return typeof arg === \"function\";\n}\nexport function isObject(arg) {\n    return typeof arg === \"object\" && arg !== null;\n}\nexport function isArray(arg) {\n    return Array.isArray(arg);\n}\n", "import { getRandom, getRangeValue, mix, randomInRange, setRangeValue } from \"./NumberUtils\";\nimport { isArray, isString, itemFromArray } from \"./Utils\";\nconst randomColorValue = \"random\", midColorValue = \"mid\", colorManagers = new Map();\nexport function addColorManager(manager) {\n    colorManagers.set(manager.key, manager);\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * 6 * t;\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\nfunction stringToRgba(input) {\n    for (const [, manager] of colorManagers) {\n        if (input.startsWith(manager.stringPrefix)) {\n            return manager.parseString(input);\n        }\n    }\n    const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])([a-f\\d])?$/i, hexFixed = input.replace(shorthandRegex, (_, r, g, b, a) => {\n        return r + r + g + g + b + b + (a !== undefined ? a + a : \"\");\n    }), regex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})?$/i, result = regex.exec(hexFixed);\n    return result\n        ? {\n            a: result[4] !== undefined ? parseInt(result[4], 16) / 0xff : 1,\n            b: parseInt(result[3], 16),\n            g: parseInt(result[2], 16),\n            r: parseInt(result[1], 16),\n        }\n        : undefined;\n}\nexport function rangeColorToRgb(input, index, useIndex = true) {\n    if (!input) {\n        return;\n    }\n    const color = isString(input) ? { value: input } : input;\n    if (isString(color.value)) {\n        return colorToRgb(color.value, index, useIndex);\n    }\n    if (isArray(color.value)) {\n        return rangeColorToRgb({\n            value: itemFromArray(color.value, index, useIndex),\n        });\n    }\n    for (const [, manager] of colorManagers) {\n        const res = manager.handleRangeColor(color);\n        if (res) {\n            return res;\n        }\n    }\n}\nexport function colorToRgb(input, index, useIndex = true) {\n    if (!input) {\n        return;\n    }\n    const color = isString(input) ? { value: input } : input;\n    if (isString(color.value)) {\n        return color.value === randomColorValue ? getRandomRgbColor() : stringToRgb(color.value);\n    }\n    if (isArray(color.value)) {\n        return colorToRgb({\n            value: itemFromArray(color.value, index, useIndex),\n        });\n    }\n    for (const [, manager] of colorManagers) {\n        const res = manager.handleColor(color);\n        if (res) {\n            return res;\n        }\n    }\n}\nexport function colorToHsl(color, index, useIndex = true) {\n    const rgb = colorToRgb(color, index, useIndex);\n    return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rangeColorToHsl(color, index, useIndex = true) {\n    const rgb = rangeColorToRgb(color, index, useIndex);\n    return rgb ? rgbToHsl(rgb) : undefined;\n}\nexport function rgbToHsl(color) {\n    const r1 = color.r / 255, g1 = color.g / 255, b1 = color.b / 255, max = Math.max(r1, g1, b1), min = Math.min(r1, g1, b1), res = {\n        h: 0,\n        l: (max + min) / 2,\n        s: 0,\n    };\n    if (max !== min) {\n        res.s = res.l < 0.5 ? (max - min) / (max + min) : (max - min) / (2.0 - max - min);\n        res.h =\n            r1 === max\n                ? (g1 - b1) / (max - min)\n                : (res.h = g1 === max ? 2.0 + (b1 - r1) / (max - min) : 4.0 + (r1 - g1) / (max - min));\n    }\n    res.l *= 100;\n    res.s *= 100;\n    res.h *= 60;\n    if (res.h < 0) {\n        res.h += 360;\n    }\n    if (res.h >= 360) {\n        res.h -= 360;\n    }\n    return res;\n}\nexport function stringToAlpha(input) {\n    return stringToRgba(input)?.a;\n}\nexport function stringToRgb(input) {\n    return stringToRgba(input);\n}\nexport function hslToRgb(hsl) {\n    const result = { b: 0, g: 0, r: 0 }, hslPercent = {\n        h: hsl.h / 360,\n        l: hsl.l / 100,\n        s: hsl.s / 100,\n    };\n    if (!hslPercent.s) {\n        result.r = result.g = result.b = hslPercent.l;\n    }\n    else {\n        const q = hslPercent.l < 0.5\n            ? hslPercent.l * (1 + hslPercent.s)\n            : hslPercent.l + hslPercent.s - hslPercent.l * hslPercent.s, p = 2 * hslPercent.l - q;\n        result.r = hue2rgb(p, q, hslPercent.h + 1 / 3);\n        result.g = hue2rgb(p, q, hslPercent.h);\n        result.b = hue2rgb(p, q, hslPercent.h - 1 / 3);\n    }\n    result.r = Math.floor(result.r * 255);\n    result.g = Math.floor(result.g * 255);\n    result.b = Math.floor(result.b * 255);\n    return result;\n}\nexport function hslaToRgba(hsla) {\n    const rgbResult = hslToRgb(hsla);\n    return {\n        a: hsla.a,\n        b: rgbResult.b,\n        g: rgbResult.g,\n        r: rgbResult.r,\n    };\n}\nexport function getRandomRgbColor(min) {\n    const fixedMin = min ?? 0;\n    return {\n        b: Math.floor(randomInRange(setRangeValue(fixedMin, 256))),\n        g: Math.floor(randomInRange(setRangeValue(fixedMin, 256))),\n        r: Math.floor(randomInRange(setRangeValue(fixedMin, 256))),\n    };\n}\nexport function getStyleFromRgb(color, opacity) {\n    return `rgba(${color.r}, ${color.g}, ${color.b}, ${opacity ?? 1})`;\n}\nexport function getStyleFromHsl(color, opacity) {\n    return `hsla(${color.h}, ${color.s}%, ${color.l}%, ${opacity ?? 1})`;\n}\nexport function colorMix(color1, color2, size1, size2) {\n    let rgb1 = color1, rgb2 = color2;\n    if (rgb1.r === undefined) {\n        rgb1 = hslToRgb(color1);\n    }\n    if (rgb2.r === undefined) {\n        rgb2 = hslToRgb(color2);\n    }\n    return {\n        b: mix(rgb1.b, rgb2.b, size1, size2),\n        g: mix(rgb1.g, rgb2.g, size1, size2),\n        r: mix(rgb1.r, rgb2.r, size1, size2),\n    };\n}\nexport function getLinkColor(p1, p2, linkColor) {\n    if (linkColor === randomColorValue) {\n        return getRandomRgbColor();\n    }\n    else if (linkColor === midColorValue) {\n        const sourceColor = p1.getFillColor() ?? p1.getStrokeColor(), destColor = p2?.getFillColor() ?? p2?.getStrokeColor();\n        if (sourceColor && destColor && p2) {\n            return colorMix(sourceColor, destColor, p1.getRadius(), p2.getRadius());\n        }\n        else {\n            const hslColor = sourceColor ?? destColor;\n            if (hslColor) {\n                return hslToRgb(hslColor);\n            }\n        }\n    }\n    else {\n        return linkColor;\n    }\n}\nexport function getLinkRandomColor(optColor, blink, consent) {\n    const color = isString(optColor) ? optColor : optColor.value;\n    if (color === randomColorValue) {\n        if (consent) {\n            return rangeColorToRgb({\n                value: color,\n            });\n        }\n        if (blink) {\n            return randomColorValue;\n        }\n        return midColorValue;\n    }\n    else if (color === midColorValue) {\n        return midColorValue;\n    }\n    else {\n        return rangeColorToRgb({\n            value: color,\n        });\n    }\n}\nexport function getHslFromAnimation(animation) {\n    return animation !== undefined\n        ? {\n            h: animation.h.value,\n            s: animation.s.value,\n            l: animation.l.value,\n        }\n        : undefined;\n}\nexport function getHslAnimationFromHsl(hsl, animationOptions, reduceFactor) {\n    const resColor = {\n        h: {\n            enable: false,\n            value: hsl.h,\n        },\n        s: {\n            enable: false,\n            value: hsl.s,\n        },\n        l: {\n            enable: false,\n            value: hsl.l,\n        },\n    };\n    if (animationOptions) {\n        setColorAnimation(resColor.h, animationOptions.h, reduceFactor);\n        setColorAnimation(resColor.s, animationOptions.s, reduceFactor);\n        setColorAnimation(resColor.l, animationOptions.l, reduceFactor);\n    }\n    return resColor;\n}\nfunction setColorAnimation(colorValue, colorAnimation, reduceFactor) {\n    colorValue.enable = colorAnimation.enable;\n    if (colorValue.enable) {\n        colorValue.velocity = (getRangeValue(colorAnimation.speed) / 100) * reduceFactor;\n        colorValue.decay = 1 - getRangeValue(colorAnimation.decay);\n        colorValue.status = \"increasing\";\n        colorValue.loops = 0;\n        colorValue.maxLoops = getRangeValue(colorAnimation.count);\n        colorValue.time = 0;\n        colorValue.delayTime = getRangeValue(colorAnimation.delay) * 1000;\n        if (!colorAnimation.sync) {\n            colorValue.velocity *= getRandom();\n            colorValue.value *= getRandom();\n        }\n        colorValue.initialValue = colorValue.value;\n    }\n    else {\n        colorValue.velocity = 0;\n    }\n}\n", "import { getStyleFromRgb } from \"./ColorUtils\";\nexport function drawLine(context, begin, end) {\n    context.beginPath();\n    context.moveTo(begin.x, begin.y);\n    context.lineTo(end.x, end.y);\n    context.closePath();\n}\nexport function drawTriangle(context, p1, p2, p3) {\n    context.beginPath();\n    context.moveTo(p1.x, p1.y);\n    context.lineTo(p2.x, p2.y);\n    context.lineTo(p3.x, p3.y);\n    context.closePath();\n}\nexport function paintBase(context, dimension, baseColor) {\n    context.fillStyle = baseColor ?? \"rgba(0,0,0,0)\";\n    context.fillRect(0, 0, dimension.width, dimension.height);\n}\nexport function paintImage(context, dimension, image, opacity) {\n    if (!image) {\n        return;\n    }\n    context.globalAlpha = opacity;\n    context.drawImage(image, 0, 0, dimension.width, dimension.height);\n    context.globalAlpha = 1;\n}\nexport function clear(context, dimension) {\n    context.clearRect(0, 0, dimension.width, dimension.height);\n}\nexport function drawParticle(data) {\n    const { container, context, particle, delta, colorStyles, backgroundMask, composite, radius, opacity, shadow, transform, } = data;\n    const pos = particle.getPosition(), angle = particle.rotation + (particle.pathRotation ? particle.velocity.angle : 0), rotateData = {\n        sin: Math.sin(angle),\n        cos: Math.cos(angle),\n    }, transformData = {\n        a: rotateData.cos * (transform.a ?? 1),\n        b: rotateData.sin * (transform.b ?? 1),\n        c: -rotateData.sin * (transform.c ?? 1),\n        d: rotateData.cos * (transform.d ?? 1),\n    };\n    context.setTransform(transformData.a, transformData.b, transformData.c, transformData.d, pos.x, pos.y);\n    context.beginPath();\n    if (backgroundMask) {\n        context.globalCompositeOperation = composite;\n    }\n    const shadowColor = particle.shadowColor;\n    if (shadow.enable && shadowColor) {\n        context.shadowBlur = shadow.blur;\n        context.shadowColor = getStyleFromRgb(shadowColor);\n        context.shadowOffsetX = shadow.offset.x;\n        context.shadowOffsetY = shadow.offset.y;\n    }\n    if (colorStyles.fill) {\n        context.fillStyle = colorStyles.fill;\n    }\n    const strokeWidth = particle.strokeWidth ?? 0;\n    context.lineWidth = strokeWidth;\n    if (colorStyles.stroke) {\n        context.strokeStyle = colorStyles.stroke;\n    }\n    drawShape(container, context, particle, radius, opacity, delta);\n    if (strokeWidth > 0) {\n        context.stroke();\n    }\n    if (particle.close) {\n        context.closePath();\n    }\n    if (particle.fill) {\n        context.fill();\n    }\n    drawShapeAfterEffect(container, context, particle, radius, opacity, delta);\n    context.globalCompositeOperation = \"source-over\";\n    context.setTransform(1, 0, 0, 1, 0, 0);\n}\nexport function drawShape(container, context, particle, radius, opacity, delta) {\n    if (!particle.shape) {\n        return;\n    }\n    const drawer = container.drawers.get(particle.shape);\n    if (!drawer) {\n        return;\n    }\n    drawer.draw(context, particle, radius, opacity, delta, container.retina.pixelRatio);\n}\nexport function drawShapeAfterEffect(container, context, particle, radius, opacity, delta) {\n    if (!particle.shape) {\n        return;\n    }\n    const drawer = container.drawers.get(particle.shape);\n    if (!drawer || !drawer.afterEffect) {\n        return;\n    }\n    drawer.afterEffect(context, particle, radius, opacity, delta, container.retina.pixelRatio);\n}\nexport function drawPlugin(context, plugin, delta) {\n    if (!plugin.draw) {\n        return;\n    }\n    plugin.draw(context, delta);\n}\nexport function drawParticlePlugin(context, plugin, particle, delta) {\n    if (!plugin.drawParticle) {\n        return;\n    }\n    plugin.drawParticle(context, particle, delta);\n}\nexport function alterHsl(color, type, value) {\n    return {\n        h: color.h,\n        s: color.s,\n        l: color.l + (type === \"darken\" ? -1 : 1) * value,\n    };\n}\n", "import { clear, drawParticle, drawParticlePlugin, drawPlugin, paintBase, paintImage } from \"../Utils/CanvasUtils\";\nimport { deepExtend, getLogger, safeMutationObserver } from \"../Utils/Utils\";\nimport { getStyleFromHsl, getStyleFromRgb, rangeColorToHsl, rangeColorToRgb } from \"../Utils/ColorUtils\";\nimport { generatedAttribute } from \"./Utils/Constants\";\nfunction setTransformValue(factor, newFactor, key) {\n    const newValue = newFactor[key];\n    if (newValue !== undefined) {\n        factor[key] = (factor[key] ?? 1) * newValue;\n    }\n}\nexport class Canvas {\n    constructor(container) {\n        this.container = container;\n        this._applyPostDrawUpdaters = (particle) => {\n            for (const updater of this._postDrawUpdaters) {\n                updater.afterDraw && updater.afterDraw(particle);\n            }\n        };\n        this._applyPreDrawUpdaters = (ctx, particle, radius, zOpacity, colorStyles, transform) => {\n            for (const updater of this._preDrawUpdaters) {\n                if (updater.getColorStyles) {\n                    const { fill, stroke } = updater.getColorStyles(particle, ctx, radius, zOpacity);\n                    if (fill) {\n                        colorStyles.fill = fill;\n                    }\n                    if (stroke) {\n                        colorStyles.stroke = stroke;\n                    }\n                }\n                if (updater.getTransformValues) {\n                    const updaterTransform = updater.getTransformValues(particle);\n                    for (const key in updaterTransform) {\n                        setTransformValue(transform, updaterTransform, key);\n                    }\n                }\n                updater.beforeDraw && updater.beforeDraw(particle);\n            }\n        };\n        this._applyResizePlugins = () => {\n            for (const plugin of this._resizePlugins) {\n                plugin.resize && plugin.resize();\n            }\n        };\n        this._getPluginParticleColors = (particle) => {\n            let fColor, sColor;\n            for (const plugin of this._colorPlugins) {\n                if (!fColor && plugin.particleFillColor) {\n                    fColor = rangeColorToHsl(plugin.particleFillColor(particle));\n                }\n                if (!sColor && plugin.particleStrokeColor) {\n                    sColor = rangeColorToHsl(plugin.particleStrokeColor(particle));\n                }\n                if (fColor && sColor) {\n                    break;\n                }\n            }\n            return [fColor, sColor];\n        };\n        this._initCover = () => {\n            const options = this.container.actualOptions, cover = options.backgroundMask.cover, color = cover.color, coverRgb = rangeColorToRgb(color);\n            if (coverRgb) {\n                const coverColor = {\n                    ...coverRgb,\n                    a: cover.opacity,\n                };\n                this._coverColorStyle = getStyleFromRgb(coverColor, coverColor.a);\n            }\n        };\n        this._initStyle = () => {\n            const element = this.element, options = this.container.actualOptions;\n            if (!element) {\n                return;\n            }\n            if (this._fullScreen) {\n                this._originalStyle = deepExtend({}, element.style);\n                this._setFullScreenStyle();\n            }\n            else {\n                this._resetOriginalStyle();\n            }\n            for (const key in options.style) {\n                if (!key || !options.style) {\n                    continue;\n                }\n                const value = options.style[key];\n                if (!value) {\n                    continue;\n                }\n                element.style.setProperty(key, value, \"important\");\n            }\n        };\n        this._initTrail = async () => {\n            const options = this.container.actualOptions, trail = options.particles.move.trail, trailFill = trail.fill;\n            if (!trail.enable) {\n                return;\n            }\n            if (trailFill.color) {\n                const fillColor = rangeColorToRgb(trailFill.color);\n                if (!fillColor) {\n                    return;\n                }\n                const trail = options.particles.move.trail;\n                this._trailFill = {\n                    color: {\n                        ...fillColor,\n                    },\n                    opacity: 1 / trail.length,\n                };\n            }\n            else {\n                await new Promise((resolve, reject) => {\n                    if (!trailFill.image) {\n                        return;\n                    }\n                    const img = document.createElement(\"img\");\n                    img.addEventListener(\"load\", () => {\n                        this._trailFill = {\n                            image: img,\n                            opacity: 1 / trail.length,\n                        };\n                        resolve();\n                    });\n                    img.addEventListener(\"error\", (evt) => {\n                        reject(evt.error);\n                    });\n                    img.src = trailFill.image;\n                });\n            }\n        };\n        this._paintBase = (baseColor) => {\n            this.draw((ctx) => paintBase(ctx, this.size, baseColor));\n        };\n        this._paintImage = (image, opacity) => {\n            this.draw((ctx) => paintImage(ctx, this.size, image, opacity));\n        };\n        this._repairStyle = () => {\n            const element = this.element;\n            if (!element) {\n                return;\n            }\n            this._safeMutationObserver((observer) => observer.disconnect());\n            this._initStyle();\n            this.initBackground();\n            this._safeMutationObserver((observer) => observer.observe(element, { attributes: true }));\n        };\n        this._resetOriginalStyle = () => {\n            const element = this.element, originalStyle = this._originalStyle;\n            if (!(element && originalStyle)) {\n                return;\n            }\n            const style = element.style;\n            style.position = originalStyle.position;\n            style.zIndex = originalStyle.zIndex;\n            style.top = originalStyle.top;\n            style.left = originalStyle.left;\n            style.width = originalStyle.width;\n            style.height = originalStyle.height;\n        };\n        this._safeMutationObserver = (callback) => {\n            if (!this._mutationObserver) {\n                return;\n            }\n            callback(this._mutationObserver);\n        };\n        this._setFullScreenStyle = () => {\n            const element = this.element;\n            if (!element) {\n                return;\n            }\n            const priority = \"important\", style = element.style;\n            style.setProperty(\"position\", \"fixed\", priority);\n            style.setProperty(\"z-index\", this.container.actualOptions.fullScreen.zIndex.toString(10), priority);\n            style.setProperty(\"top\", \"0\", priority);\n            style.setProperty(\"left\", \"0\", priority);\n            style.setProperty(\"width\", \"100%\", priority);\n            style.setProperty(\"height\", \"100%\", priority);\n        };\n        this.size = {\n            height: 0,\n            width: 0,\n        };\n        this._context = null;\n        this._generated = false;\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        this._resizePlugins = [];\n        this._colorPlugins = [];\n    }\n    get _fullScreen() {\n        return this.container.actualOptions.fullScreen.enable;\n    }\n    clear() {\n        const options = this.container.actualOptions, trail = options.particles.move.trail, trailFill = this._trailFill;\n        if (options.backgroundMask.enable) {\n            this.paint();\n        }\n        else if (trail.enable && trail.length > 0 && trailFill) {\n            if (trailFill.color) {\n                this._paintBase(getStyleFromRgb(trailFill.color, trailFill.opacity));\n            }\n            else if (trailFill.image) {\n                this._paintImage(trailFill.image, trailFill.opacity);\n            }\n        }\n        else {\n            this.draw((ctx) => {\n                clear(ctx, this.size);\n            });\n        }\n    }\n    destroy() {\n        this.stop();\n        if (this._generated) {\n            const element = this.element;\n            element && element.remove();\n        }\n        else {\n            this._resetOriginalStyle();\n        }\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        this._resizePlugins = [];\n        this._colorPlugins = [];\n    }\n    draw(cb) {\n        const ctx = this._context;\n        if (!ctx) {\n            return;\n        }\n        return cb(ctx);\n    }\n    drawParticle(particle, delta) {\n        if (particle.spawning || particle.destroyed) {\n            return;\n        }\n        const radius = particle.getRadius();\n        if (radius <= 0) {\n            return;\n        }\n        const pfColor = particle.getFillColor(), psColor = particle.getStrokeColor() ?? pfColor;\n        let [fColor, sColor] = this._getPluginParticleColors(particle);\n        if (!fColor) {\n            fColor = pfColor;\n        }\n        if (!sColor) {\n            sColor = psColor;\n        }\n        if (!fColor && !sColor) {\n            return;\n        }\n        this.draw((ctx) => {\n            const container = this.container, options = container.actualOptions, zIndexOptions = particle.options.zIndex, zOpacityFactor = (1 - particle.zIndexFactor) ** zIndexOptions.opacityRate, opacity = particle.bubble.opacity ?? particle.opacity?.value ?? 1, strokeOpacity = particle.strokeOpacity ?? opacity, zOpacity = opacity * zOpacityFactor, zStrokeOpacity = strokeOpacity * zOpacityFactor, transform = {}, colorStyles = {\n                fill: fColor ? getStyleFromHsl(fColor, zOpacity) : undefined,\n            };\n            colorStyles.stroke = sColor ? getStyleFromHsl(sColor, zStrokeOpacity) : colorStyles.fill;\n            this._applyPreDrawUpdaters(ctx, particle, radius, zOpacity, colorStyles, transform);\n            drawParticle({\n                container,\n                context: ctx,\n                particle,\n                delta,\n                colorStyles,\n                backgroundMask: options.backgroundMask.enable,\n                composite: options.backgroundMask.composite,\n                radius: radius * (1 - particle.zIndexFactor) ** zIndexOptions.sizeRate,\n                opacity: zOpacity,\n                shadow: particle.options.shadow,\n                transform,\n            });\n            this._applyPostDrawUpdaters(particle);\n        });\n    }\n    drawParticlePlugin(plugin, particle, delta) {\n        this.draw((ctx) => drawParticlePlugin(ctx, plugin, particle, delta));\n    }\n    drawPlugin(plugin, delta) {\n        this.draw((ctx) => drawPlugin(ctx, plugin, delta));\n    }\n    async init() {\n        this._safeMutationObserver((obs) => obs.disconnect());\n        this._mutationObserver = safeMutationObserver((records) => {\n            for (const record of records) {\n                if (record.type === \"attributes\" && record.attributeName === \"style\") {\n                    this._repairStyle();\n                }\n            }\n        });\n        this.resize();\n        this._initStyle();\n        this._initCover();\n        try {\n            await this._initTrail();\n        }\n        catch (e) {\n            getLogger().error(e);\n        }\n        this.initBackground();\n        this._safeMutationObserver((obs) => {\n            if (!this.element) {\n                return;\n            }\n            obs.observe(this.element, { attributes: true });\n        });\n        this.initUpdaters();\n        this.initPlugins();\n        this.paint();\n    }\n    initBackground() {\n        const options = this.container.actualOptions, background = options.background, element = this.element;\n        if (!element) {\n            return;\n        }\n        const elementStyle = element.style;\n        if (!elementStyle) {\n            return;\n        }\n        if (background.color) {\n            const color = rangeColorToRgb(background.color);\n            elementStyle.backgroundColor = color ? getStyleFromRgb(color, background.opacity) : \"\";\n        }\n        else {\n            elementStyle.backgroundColor = \"\";\n        }\n        elementStyle.backgroundImage = background.image || \"\";\n        elementStyle.backgroundPosition = background.position || \"\";\n        elementStyle.backgroundRepeat = background.repeat || \"\";\n        elementStyle.backgroundSize = background.size || \"\";\n    }\n    initPlugins() {\n        this._resizePlugins = [];\n        for (const [, plugin] of this.container.plugins) {\n            if (plugin.resize) {\n                this._resizePlugins.push(plugin);\n            }\n            if (plugin.particleFillColor || plugin.particleStrokeColor) {\n                this._colorPlugins.push(plugin);\n            }\n        }\n    }\n    initUpdaters() {\n        this._preDrawUpdaters = [];\n        this._postDrawUpdaters = [];\n        for (const updater of this.container.particles.updaters) {\n            if (updater.afterDraw) {\n                this._postDrawUpdaters.push(updater);\n            }\n            if (updater.getColorStyles || updater.getTransformValues || updater.beforeDraw) {\n                this._preDrawUpdaters.push(updater);\n            }\n        }\n    }\n    loadCanvas(canvas) {\n        if (this._generated && this.element) {\n            this.element.remove();\n        }\n        this._generated =\n            canvas.dataset && generatedAttribute in canvas.dataset\n                ? canvas.dataset[generatedAttribute] === \"true\"\n                : this._generated;\n        this.element = canvas;\n        this.element.ariaHidden = \"true\";\n        this._originalStyle = deepExtend({}, this.element.style);\n        this.size.height = canvas.offsetHeight;\n        this.size.width = canvas.offsetWidth;\n        this._context = this.element.getContext(\"2d\");\n        this._safeMutationObserver((obs) => {\n            if (!this.element) {\n                return;\n            }\n            obs.observe(this.element, { attributes: true });\n        });\n        this.container.retina.init();\n        this.initBackground();\n    }\n    paint() {\n        const options = this.container.actualOptions;\n        this.draw((ctx) => {\n            if (options.backgroundMask.enable && options.backgroundMask.cover) {\n                clear(ctx, this.size);\n                this._paintBase(this._coverColorStyle);\n            }\n            else {\n                this._paintBase();\n            }\n        });\n    }\n    resize() {\n        if (!this.element) {\n            return false;\n        }\n        const container = this.container, pxRatio = container.retina.pixelRatio, size = container.canvas.size, newSize = {\n            width: this.element.offsetWidth * pxRatio,\n            height: this.element.offsetHeight * pxRatio,\n        };\n        if (newSize.height === size.height &&\n            newSize.width === size.width &&\n            newSize.height === this.element.height &&\n            newSize.width === this.element.width) {\n            return false;\n        }\n        const oldSize = { ...size };\n        this.element.width = size.width = this.element.offsetWidth * pxRatio;\n        this.element.height = size.height = this.element.offsetHeight * pxRatio;\n        if (this.container.started) {\n            this.resizeFactor = {\n                width: size.width / oldSize.width,\n                height: size.height / oldSize.height,\n            };\n        }\n        return true;\n    }\n    stop() {\n        this._safeMutationObserver((obs) => obs.disconnect());\n        this._mutationObserver = undefined;\n        this.draw((ctx) => clear(ctx, this.size));\n    }\n    async windowResize() {\n        if (!this.element || !this.resize()) {\n            return;\n        }\n        const container = this.container, needsRefresh = container.updateActualOptions();\n        container.particles.setDensity();\n        this._applyResizePlugins();\n        if (needsRefresh) {\n            await container.refresh();\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, isBoolean, safeMatchMedia } from \"../../Utils/Utils\";\nimport { mouseDownEvent, mouseLeaveEvent, mouseMoveEvent, mouseOutEvent, mouseUpEvent, resizeEvent, touchCancelEvent, touchEndEvent, touchMoveEvent, touchStartEvent, visibilityChangeEvent, } from \"./Constants\";\nfunction manageListener(element, event, handler, add, options) {\n    if (add) {\n        let addOptions = { passive: true };\n        if (isBoolean(options)) {\n            addOptions.capture = options;\n        }\n        else if (options !== undefined) {\n            addOptions = options;\n        }\n        element.addEventListener(event, handler, addOptions);\n    }\n    else {\n        const removeOptions = options;\n        element.removeEventListener(event, handler, removeOptions);\n    }\n}\nexport class EventListeners {\n    constructor(container) {\n        this.container = container;\n        this._doMouseTouchClick = (e) => {\n            const container = this.container, options = container.actualOptions;\n            if (this._canPush) {\n                const mouseInteractivity = container.interactivity.mouse, mousePos = mouseInteractivity.position;\n                if (!mousePos) {\n                    return;\n                }\n                mouseInteractivity.clickPosition = { ...mousePos };\n                mouseInteractivity.clickTime = new Date().getTime();\n                const onClick = options.interactivity.events.onClick;\n                executeOnSingleOrMultiple(onClick.mode, (mode) => this.container.handleClickMode(mode));\n            }\n            if (e.type === \"touchend\") {\n                setTimeout(() => this._mouseTouchFinish(), 500);\n            }\n        };\n        this._handleThemeChange = (e) => {\n            const mediaEvent = e, container = this.container, options = container.options, defaultThemes = options.defaultThemes, themeName = mediaEvent.matches ? defaultThemes.dark : defaultThemes.light, theme = options.themes.find((theme) => theme.name === themeName);\n            if (theme && theme.default.auto) {\n                container.loadTheme(themeName);\n            }\n        };\n        this._handleVisibilityChange = () => {\n            const container = this.container, options = container.actualOptions;\n            this._mouseTouchFinish();\n            if (!options.pauseOnBlur) {\n                return;\n            }\n            if (document && document.hidden) {\n                container.pageHidden = true;\n                container.pause();\n            }\n            else {\n                container.pageHidden = false;\n                if (container.getAnimationStatus()) {\n                    container.play(true);\n                }\n                else {\n                    container.draw(true);\n                }\n            }\n        };\n        this._handleWindowResize = async () => {\n            if (this._resizeTimeout) {\n                clearTimeout(this._resizeTimeout);\n                delete this._resizeTimeout;\n            }\n            this._resizeTimeout = setTimeout(async () => {\n                const canvas = this.container.canvas;\n                canvas && (await canvas.windowResize());\n            }, this.container.actualOptions.interactivity.events.resize.delay * 1000);\n        };\n        this._manageInteractivityListeners = (mouseLeaveTmpEvent, add) => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions;\n            const interactivityEl = container.interactivity.element;\n            if (!interactivityEl) {\n                return;\n            }\n            const html = interactivityEl, canvasEl = container.canvas.element;\n            if (canvasEl) {\n                canvasEl.style.pointerEvents = html === canvasEl ? \"initial\" : \"none\";\n            }\n            if (!(options.interactivity.events.onHover.enable || options.interactivity.events.onClick.enable)) {\n                return;\n            }\n            manageListener(interactivityEl, mouseMoveEvent, handlers.mouseMove, add);\n            manageListener(interactivityEl, touchStartEvent, handlers.touchStart, add);\n            manageListener(interactivityEl, touchMoveEvent, handlers.touchMove, add);\n            if (!options.interactivity.events.onClick.enable) {\n                manageListener(interactivityEl, touchEndEvent, handlers.touchEnd, add);\n            }\n            else {\n                manageListener(interactivityEl, touchEndEvent, handlers.touchEndClick, add);\n                manageListener(interactivityEl, mouseUpEvent, handlers.mouseUp, add);\n                manageListener(interactivityEl, mouseDownEvent, handlers.mouseDown, add);\n            }\n            manageListener(interactivityEl, mouseLeaveTmpEvent, handlers.mouseLeave, add);\n            manageListener(interactivityEl, touchCancelEvent, handlers.touchCancel, add);\n        };\n        this._manageListeners = (add) => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions, detectType = options.interactivity.detectsOn, canvasEl = container.canvas.element;\n            let mouseLeaveTmpEvent = mouseLeaveEvent;\n            if (detectType === \"window\") {\n                container.interactivity.element = window;\n                mouseLeaveTmpEvent = mouseOutEvent;\n            }\n            else if (detectType === \"parent\" && canvasEl) {\n                container.interactivity.element = canvasEl.parentElement ?? canvasEl.parentNode;\n            }\n            else {\n                container.interactivity.element = canvasEl;\n            }\n            this._manageMediaMatch(add);\n            this._manageResize(add);\n            this._manageInteractivityListeners(mouseLeaveTmpEvent, add);\n            if (document) {\n                manageListener(document, visibilityChangeEvent, handlers.visibilityChange, add, false);\n            }\n        };\n        this._manageMediaMatch = (add) => {\n            const handlers = this._handlers, mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\");\n            if (!mediaMatch) {\n                return;\n            }\n            if (mediaMatch.addEventListener !== undefined) {\n                manageListener(mediaMatch, \"change\", handlers.themeChange, add);\n                return;\n            }\n            if (mediaMatch.addListener === undefined) {\n                return;\n            }\n            if (add) {\n                mediaMatch.addListener(handlers.oldThemeChange);\n            }\n            else {\n                mediaMatch.removeListener(handlers.oldThemeChange);\n            }\n        };\n        this._manageResize = (add) => {\n            const handlers = this._handlers, container = this.container, options = container.actualOptions;\n            if (!options.interactivity.events.resize) {\n                return;\n            }\n            if (typeof ResizeObserver === \"undefined\") {\n                manageListener(window, resizeEvent, handlers.resize, add);\n                return;\n            }\n            const canvasEl = container.canvas.element;\n            if (this._resizeObserver && !add) {\n                if (canvasEl) {\n                    this._resizeObserver.unobserve(canvasEl);\n                }\n                this._resizeObserver.disconnect();\n                delete this._resizeObserver;\n            }\n            else if (!this._resizeObserver && add && canvasEl) {\n                this._resizeObserver = new ResizeObserver(async (entries) => {\n                    const entry = entries.find((e) => e.target === canvasEl);\n                    if (!entry) {\n                        return;\n                    }\n                    await this._handleWindowResize();\n                });\n                this._resizeObserver.observe(canvasEl);\n            }\n        };\n        this._mouseDown = () => {\n            const { interactivity } = this.container;\n            if (!interactivity) {\n                return;\n            }\n            const { mouse } = interactivity;\n            mouse.clicking = true;\n            mouse.downPosition = mouse.position;\n        };\n        this._mouseTouchClick = (e) => {\n            const container = this.container, options = container.actualOptions, { mouse } = container.interactivity;\n            mouse.inside = true;\n            let handled = false;\n            const mousePosition = mouse.position;\n            if (!mousePosition || !options.interactivity.events.onClick.enable) {\n                return;\n            }\n            for (const [, plugin] of container.plugins) {\n                if (!plugin.clickPositionValid) {\n                    continue;\n                }\n                handled = plugin.clickPositionValid(mousePosition);\n                if (handled) {\n                    break;\n                }\n            }\n            if (!handled) {\n                this._doMouseTouchClick(e);\n            }\n            mouse.clicking = false;\n        };\n        this._mouseTouchFinish = () => {\n            const interactivity = this.container.interactivity;\n            if (!interactivity) {\n                return;\n            }\n            const mouse = interactivity.mouse;\n            delete mouse.position;\n            delete mouse.clickPosition;\n            delete mouse.downPosition;\n            interactivity.status = mouseLeaveEvent;\n            mouse.inside = false;\n            mouse.clicking = false;\n        };\n        this._mouseTouchMove = (e) => {\n            const container = this.container, options = container.actualOptions, interactivity = container.interactivity, canvasEl = container.canvas.element;\n            if (!interactivity || !interactivity.element) {\n                return;\n            }\n            interactivity.mouse.inside = true;\n            let pos;\n            if (e.type.startsWith(\"pointer\")) {\n                this._canPush = true;\n                const mouseEvent = e;\n                if (interactivity.element === window) {\n                    if (canvasEl) {\n                        const clientRect = canvasEl.getBoundingClientRect();\n                        pos = {\n                            x: mouseEvent.clientX - clientRect.left,\n                            y: mouseEvent.clientY - clientRect.top,\n                        };\n                    }\n                }\n                else if (options.interactivity.detectsOn === \"parent\") {\n                    const source = mouseEvent.target, target = mouseEvent.currentTarget;\n                    if (source && target && canvasEl) {\n                        const sourceRect = source.getBoundingClientRect(), targetRect = target.getBoundingClientRect(), canvasRect = canvasEl.getBoundingClientRect();\n                        pos = {\n                            x: mouseEvent.offsetX + 2 * sourceRect.left - (targetRect.left + canvasRect.left),\n                            y: mouseEvent.offsetY + 2 * sourceRect.top - (targetRect.top + canvasRect.top),\n                        };\n                    }\n                    else {\n                        pos = {\n                            x: mouseEvent.offsetX ?? mouseEvent.clientX,\n                            y: mouseEvent.offsetY ?? mouseEvent.clientY,\n                        };\n                    }\n                }\n                else if (mouseEvent.target === canvasEl) {\n                    pos = {\n                        x: mouseEvent.offsetX ?? mouseEvent.clientX,\n                        y: mouseEvent.offsetY ?? mouseEvent.clientY,\n                    };\n                }\n            }\n            else {\n                this._canPush = e.type !== \"touchmove\";\n                if (canvasEl) {\n                    const touchEvent = e, lastTouch = touchEvent.touches[touchEvent.touches.length - 1], canvasRect = canvasEl.getBoundingClientRect();\n                    pos = {\n                        x: lastTouch.clientX - (canvasRect.left ?? 0),\n                        y: lastTouch.clientY - (canvasRect.top ?? 0),\n                    };\n                }\n            }\n            const pxRatio = container.retina.pixelRatio;\n            if (pos) {\n                pos.x *= pxRatio;\n                pos.y *= pxRatio;\n            }\n            interactivity.mouse.position = pos;\n            interactivity.status = mouseMoveEvent;\n        };\n        this._touchEnd = (e) => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.delete(touch.identifier);\n            }\n            this._mouseTouchFinish();\n        };\n        this._touchEndClick = (e) => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.delete(touch.identifier);\n            }\n            this._mouseTouchClick(e);\n        };\n        this._touchStart = (e) => {\n            const evt = e, touches = Array.from(evt.changedTouches);\n            for (const touch of touches) {\n                this._touches.set(touch.identifier, performance.now());\n            }\n            this._mouseTouchMove(e);\n        };\n        this._canPush = true;\n        this._touches = new Map();\n        this._handlers = {\n            mouseDown: () => this._mouseDown(),\n            mouseLeave: () => this._mouseTouchFinish(),\n            mouseMove: (e) => this._mouseTouchMove(e),\n            mouseUp: (e) => this._mouseTouchClick(e),\n            touchStart: (e) => this._touchStart(e),\n            touchMove: (e) => this._mouseTouchMove(e),\n            touchEnd: (e) => this._touchEnd(e),\n            touchCancel: (e) => this._touchEnd(e),\n            touchEndClick: (e) => this._touchEndClick(e),\n            visibilityChange: () => this._handleVisibilityChange(),\n            themeChange: (e) => this._handleThemeChange(e),\n            oldThemeChange: (e) => this._handleThemeChange(e),\n            resize: () => {\n                this._handleWindowResize();\n            },\n        };\n    }\n    addListeners() {\n        this._manageListeners(true);\n    }\n    removeListeners() {\n        this._manageListeners(false);\n    }\n}\n", "import { isArray, isString } from \"../../Utils/Utils\";\nexport class OptionsColor {\n    constructor() {\n        this.value = \"\";\n    }\n    static create(source, data) {\n        const color = new OptionsColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (isString(data) || isArray(data)) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        if (data?.value === undefined) {\n            return;\n        }\n        this.value = data.value;\n    }\n}\n", "import { OptionsColor } from \"../OptionsColor\";\nexport class Background {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"\";\n        this.image = \"\";\n        this.position = \"\";\n        this.repeat = \"\";\n        this.size = \"\";\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n        if (data.position !== undefined) {\n            this.position = data.position;\n        }\n        if (data.repeat !== undefined) {\n            this.repeat = data.repeat;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n", "import { OptionsColor } from \"../OptionsColor\";\nexport class BackgroundMaskCover {\n    constructor() {\n        this.color = new OptionsColor();\n        this.color.value = \"#fff\";\n        this.opacity = 1;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = data.opacity;\n        }\n    }\n}\n", "import { BackgroundMaskCover } from \"./BackgroundMaskCover\";\nimport { isString } from \"../../../Utils/Utils\";\nexport class BackgroundMask {\n    constructor() {\n        this.composite = \"destination-out\";\n        this.cover = new BackgroundMaskCover();\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.composite !== undefined) {\n            this.composite = data.composite;\n        }\n        if (data.cover !== undefined) {\n            const cover = data.cover;\n            const color = (isString(data.cover) ? { color: data.cover } : data.cover);\n            this.cover.load(cover.color !== undefined ? cover : { color: color });\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n", "export class FullScreen {\n    constructor() {\n        this.enable = true;\n        this.zIndex = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.zIndex !== undefined) {\n            this.zIndex = data.zIndex;\n        }\n    }\n}\n", "export class ClickEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple } from \"../../../../Utils/Utils\";\nexport class DivEvent {\n    constructor() {\n        this.selectors = [];\n        this.enable = false;\n        this.mode = [];\n        this.type = \"circle\";\n    }\n    get el() {\n        return this.elementId;\n    }\n    set el(value) {\n        this.elementId = value;\n    }\n    get elementId() {\n        return this.ids;\n    }\n    set elementId(value) {\n        this.ids = value;\n    }\n    get ids() {\n        return executeOnSingleOrMultiple(this.selectors, (t) => t.replace(\"#\", \"\"));\n    }\n    set ids(value) {\n        this.selectors = executeOnSingleOrMultiple(value, (t) => `#${t}`);\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const ids = data.ids ?? data.elementId ?? data.el;\n        if (ids !== undefined) {\n            this.ids = ids;\n        }\n        if (data.selectors !== undefined) {\n            this.selectors = data.selectors;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n", "export class Parallax {\n    constructor() {\n        this.enable = false;\n        this.force = 2;\n        this.smooth = 10;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.force !== undefined) {\n            this.force = data.force;\n        }\n        if (data.smooth !== undefined) {\n            this.smooth = data.smooth;\n        }\n    }\n}\n", "import { Parallax } from \"./Parallax\";\nexport class HoverEvent {\n    constructor() {\n        this.enable = false;\n        this.mode = [];\n        this.parallax = new Parallax();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.parallax.load(data.parallax);\n    }\n}\n", "export class ResizeEvent {\n    constructor() {\n        this.delay = 0.5;\n        this.enable = true;\n    }\n    load(data) {\n        if (data === undefined) {\n            return;\n        }\n        if (data.delay !== undefined) {\n            this.delay = data.delay;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple, isBoolean } from \"../../../../Utils/Utils\";\nimport { ClickEvent } from \"./ClickEvent\";\nimport { DivEvent } from \"./DivEvent\";\nimport { HoverEvent } from \"./HoverEvent\";\nimport { ResizeEvent } from \"./ResizeEvent\";\nexport class Events {\n    constructor() {\n        this.onClick = new ClickEvent();\n        this.onDiv = new DivEvent();\n        this.onHover = new HoverEvent();\n        this.resize = new ResizeEvent();\n    }\n    get onclick() {\n        return this.onClick;\n    }\n    set onclick(value) {\n        this.onClick = value;\n    }\n    get ondiv() {\n        return this.onDiv;\n    }\n    set ondiv(value) {\n        this.onDiv = value;\n    }\n    get onhover() {\n        return this.onHover;\n    }\n    set onhover(value) {\n        this.onHover = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.onClick.load(data.onClick ?? data.onclick);\n        const onDiv = data.onDiv ?? data.ondiv;\n        if (onDiv !== undefined) {\n            this.onDiv = executeOnSingleOrMultiple(onDiv, (t) => {\n                const tmp = new DivEvent();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        this.onHover.load(data.onHover ?? data.onhover);\n        if (isBoolean(data.resize)) {\n            this.resize.enable = data.resize;\n        }\n        else {\n            this.resize.load(data.resize);\n        }\n    }\n}\n", "export class Modes {\n    constructor(engine, container) {\n        this._engine = engine;\n        this._container = container;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (!this._container) {\n            return;\n        }\n        const interactors = this._engine.plugins.interactors.get(this._container);\n        if (!interactors) {\n            return;\n        }\n        for (const interactor of interactors) {\n            if (!interactor.loadModeOptions) {\n                continue;\n            }\n            interactor.loadModeOptions(this, data);\n        }\n    }\n}\n", "import { Events } from \"./Events/Events\";\nimport { Modes } from \"./Modes/Modes\";\nexport class Interactivity {\n    constructor(engine, container) {\n        this.detectsOn = \"window\";\n        this.events = new Events();\n        this.modes = new Modes(engine, container);\n    }\n    get detect_on() {\n        return this.detectsOn;\n    }\n    set detect_on(value) {\n        this.detectsOn = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const detectsOn = data.detectsOn ?? data.detect_on;\n        if (detectsOn !== undefined) {\n            this.detectsOn = detectsOn;\n        }\n        this.events.load(data.events);\n        this.modes.load(data.modes);\n    }\n}\n", "import { deepExtend } from \"../../Utils/Utils\";\nexport class ManualParticle {\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.position) {\n            this.position = {\n                x: data.position.x ?? 50,\n                y: data.position.y ?? 50,\n                mode: data.position.mode ?? \"percent\",\n            };\n        }\n        if (data.options) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n", "import { deepExtend } from \"../../Utils/Utils\";\nexport class Responsive {\n    constructor() {\n        this.maxWidth = Infinity;\n        this.options = {};\n        this.mode = \"canvas\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.maxWidth !== undefined) {\n            this.maxWidth = data.maxWidth;\n        }\n        if (data.mode !== undefined) {\n            if (data.mode === \"screen\") {\n                this.mode = \"screen\";\n            }\n            else {\n                this.mode = \"canvas\";\n            }\n        }\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n", "export class ThemeDefault {\n    constructor() {\n        this.auto = false;\n        this.mode = \"any\";\n        this.value = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.auto !== undefined) {\n            this.auto = data.auto;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n", "import { ThemeDefault } from \"./ThemeDefault\";\nimport { deepExtend } from \"../../../Utils/Utils\";\nexport class Theme {\n    constructor() {\n        this.name = \"\";\n        this.default = new ThemeDefault();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.name !== undefined) {\n            this.name = data.name;\n        }\n        this.default.load(data.default);\n        if (data.options !== undefined) {\n            this.options = deepExtend({}, data.options);\n        }\n    }\n}\n", "import { setRangeValue } from \"../../Utils/NumberUtils\";\nexport class ColorAnimation {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.offset = 0;\n        this.speed = 1;\n        this.delay = 0;\n        this.decay = 0;\n        this.sync = true;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\n", "import { ColorAnimation } from \"./ColorAnimation\";\nexport class HslAnimation {\n    constructor() {\n        this.h = new ColorAnimation();\n        this.s = new ColorAnimation();\n        this.l = new ColorAnimation();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.h.load(data.h);\n        this.s.load(data.s);\n        this.l.load(data.l);\n    }\n}\n", "import { isArray, isString } from \"../../Utils/Utils\";\nimport { HslAnimation } from \"./HslAnimation\";\nimport { OptionsColor } from \"./OptionsColor\";\nexport class AnimatableColor extends OptionsColor {\n    constructor() {\n        super();\n        this.animation = new HslAnimation();\n    }\n    static create(source, data) {\n        const color = new AnimatableColor();\n        color.load(source);\n        if (data !== undefined) {\n            if (isString(data) || isArray(data)) {\n                color.load({ value: data });\n            }\n            else {\n                color.load(data);\n            }\n        }\n        return color;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const colorAnimation = data.animation;\n        if (colorAnimation !== undefined) {\n            if (colorAnimation.enable !== undefined) {\n                this.animation.h.load(colorAnimation);\n            }\n            else {\n                this.animation.load(data.animation);\n            }\n        }\n    }\n}\n", "export class CollisionsAbsorb {\n    constructor() {\n        this.speed = 2;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.speed !== undefined) {\n            this.speed = data.speed;\n        }\n    }\n}\n", "export class CollisionsOverlap {\n    constructor() {\n        this.enable = true;\n        this.retries = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.retries !== undefined) {\n            this.retries = data.retries;\n        }\n    }\n}\n", "import { setRangeValue } from \"../../Utils/NumberUtils\";\nexport class AnimationOptions {\n    constructor() {\n        this.count = 0;\n        this.enable = false;\n        this.speed = 1;\n        this.decay = 0;\n        this.delay = 0;\n        this.sync = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.count !== undefined) {\n            this.count = setRangeValue(data.count);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        if (data.sync !== undefined) {\n            this.sync = data.sync;\n        }\n    }\n}\nexport class RangedAnimationOptions extends AnimationOptions {\n    constructor() {\n        super();\n        this.mode = \"auto\";\n        this.startValue = \"random\";\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.minimumValue !== undefined) {\n            this.minimumValue = data.minimumValue;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.startValue !== undefined) {\n            this.startValue = data.startValue;\n        }\n    }\n}\n", "export class Random {\n    constructor() {\n        this.enable = false;\n        this.minimumValue = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.minimumValue !== undefined) {\n            this.minimumValue = data.minimumValue;\n        }\n    }\n}\n", "import { AnimationOptions, RangedAnimationOptions } from \"./AnimationOptions\";\nimport { Random } from \"./Random\";\nimport { isBoolean } from \"../../Utils/Utils\";\nimport { setRangeValue } from \"../../Utils/NumberUtils\";\nexport class ValueWithRandom {\n    constructor() {\n        this.random = new Random();\n        this.value = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (isBoolean(data.random)) {\n            this.random.enable = data.random;\n        }\n        else {\n            this.random.load(data.random);\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value, this.random.enable ? this.random.minimumValue : undefined);\n        }\n    }\n}\nexport class AnimationValueWithRandom extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new AnimationOptions();\n    }\n    get anim() {\n        return this.animation;\n    }\n    set anim(value) {\n        this.animation = value;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const animation = data.animation ?? data.anim;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n        }\n    }\n}\nexport class RangedAnimationValueWithRandom extends AnimationValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new RangedAnimationOptions();\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const animation = data.animation ?? data.anim;\n        if (animation !== undefined) {\n            this.value = setRangeValue(this.value, this.animation.enable ? this.animation.minimumValue : undefined);\n        }\n    }\n}\n", "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class ParticlesBounceFactor extends ValueWithRandom {\n    constructor() {\n        super();\n        this.random.minimumValue = 0.1;\n        this.value = 1;\n    }\n}\n", "import { ParticlesBounceFactor } from \"./ParticlesBounceFactor\";\nexport class ParticlesBounce {\n    constructor() {\n        this.horizontal = new ParticlesBounceFactor();\n        this.vertical = new ParticlesBounceFactor();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.horizontal.load(data.horizontal);\n        this.vertical.load(data.vertical);\n    }\n}\n", "import { CollisionsAbsorb } from \"./CollisionsAbsorb\";\nimport { CollisionsOverlap } from \"./CollisionsOverlap\";\nimport { ParticlesBounce } from \"../Bounce/ParticlesBounce\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class Collisions {\n    constructor() {\n        this.absorb = new CollisionsAbsorb();\n        this.bounce = new ParticlesBounce();\n        this.enable = false;\n        this.maxSpeed = 50;\n        this.mode = \"bounce\";\n        this.overlap = new CollisionsOverlap();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.absorb.load(data.absorb);\n        this.bounce.load(data.bounce);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = setRangeValue(data.maxSpeed);\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        this.overlap.load(data.overlap);\n    }\n}\n", "import { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class MoveAngle {\n    constructor() {\n        this.offset = 0;\n        this.value = 90;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.offset !== undefined) {\n            this.offset = setRangeValue(data.offset);\n        }\n        if (data.value !== undefined) {\n            this.value = setRangeValue(data.value);\n        }\n    }\n}\n", "import { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class MoveAttract {\n    constructor() {\n        this.distance = 200;\n        this.enable = false;\n        this.rotate = {\n            x: 3000,\n            y: 3000,\n        };\n    }\n    get rotateX() {\n        return this.rotate.x;\n    }\n    set rotateX(value) {\n        this.rotate.x = value;\n    }\n    get rotateY() {\n        return this.rotate.y;\n    }\n    set rotateY(value) {\n        this.rotate.y = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.distance !== undefined) {\n            this.distance = setRangeValue(data.distance);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const rotateX = data.rotate?.x ?? data.rotateX;\n        if (rotateX !== undefined) {\n            this.rotate.x = rotateX;\n        }\n        const rotateY = data.rotate?.y ?? data.rotateY;\n        if (rotateY !== undefined) {\n            this.rotate.y = rotateY;\n        }\n    }\n}\n", "export class MoveCenter {\n    constructor() {\n        this.x = 50;\n        this.y = 50;\n        this.mode = \"percent\";\n        this.radius = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.x !== undefined) {\n            this.x = data.x;\n        }\n        if (data.y !== undefined) {\n            this.y = data.y;\n        }\n        if (data.mode !== undefined) {\n            this.mode = data.mode;\n        }\n        if (data.radius !== undefined) {\n            this.radius = data.radius;\n        }\n    }\n}\n", "import { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class MoveGravity {\n    constructor() {\n        this.acceleration = 9.81;\n        this.enable = false;\n        this.inverse = false;\n        this.maxSpeed = 50;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.inverse !== undefined) {\n            this.inverse = data.inverse;\n        }\n        if (data.maxSpeed !== undefined) {\n            this.maxSpeed = setRangeValue(data.maxSpeed);\n        }\n    }\n}\n", "import { ValueWithRandom } from \"../../../ValueWithRandom\";\nimport { deepExtend } from \"../../../../../Utils/Utils\";\nexport class MovePath {\n    constructor() {\n        this.clamp = true;\n        this.delay = new ValueWithRandom();\n        this.enable = false;\n        this.options = {};\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.clamp !== undefined) {\n            this.clamp = data.clamp;\n        }\n        this.delay.load(data.delay);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.generator = data.generator;\n        if (data.options) {\n            this.options = deepExtend(this.options, data.options);\n        }\n    }\n}\n", "import { OptionsColor } from \"../../OptionsColor\";\nexport class MoveTrailFill {\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = OptionsColor.create(this.color, data.color);\n        }\n        if (data.image !== undefined) {\n            this.image = data.image;\n        }\n    }\n}\n", "import { MoveTrailFill } from \"./MoveTrailFill\";\nexport class MoveTrail {\n    constructor() {\n        this.enable = false;\n        this.length = 10;\n        this.fill = new MoveTrailFill();\n    }\n    get fillColor() {\n        return this.fill.color;\n    }\n    set fillColor(value) {\n        this.fill.load({ color: value });\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.fill !== undefined || data.fillColor !== undefined) {\n            this.fill.load(data.fill || { color: data.fillColor });\n        }\n        if (data.length !== undefined) {\n            this.length = data.length;\n        }\n    }\n}\n", "export class OutModes {\n    constructor() {\n        this.default = \"out\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.default !== undefined) {\n            this.default = data.default;\n        }\n        this.bottom = data.bottom ?? data.default;\n        this.left = data.left ?? data.default;\n        this.right = data.right ?? data.default;\n        this.top = data.top ?? data.default;\n    }\n}\n", "import { deepExtend } from \"../../../../Utils/Utils\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class Spin {\n    constructor() {\n        this.acceleration = 0;\n        this.enable = false;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.acceleration !== undefined) {\n            this.acceleration = setRangeValue(data.acceleration);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.position) {\n            this.position = deepExtend({}, data.position);\n        }\n    }\n}\n", "import { isNumber, isObject } from \"../../../../Utils/Utils\";\nimport { MoveAngle } from \"./MoveAngle\";\nimport { MoveAttract } from \"./MoveAttract\";\nimport { MoveCenter } from \"./MoveCenter\";\nimport { MoveGravity } from \"./MoveGravity\";\nimport { MovePath } from \"./Path/MovePath\";\nimport { MoveTrail } from \"./MoveTrail\";\nimport { OutModes } from \"./OutModes\";\nimport { Spin } from \"./Spin\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class Move {\n    constructor() {\n        this.angle = new MoveAngle();\n        this.attract = new MoveAttract();\n        this.center = new MoveCenter();\n        this.decay = 0;\n        this.distance = {};\n        this.direction = \"none\";\n        this.drift = 0;\n        this.enable = false;\n        this.gravity = new MoveGravity();\n        this.path = new MovePath();\n        this.outModes = new OutModes();\n        this.random = false;\n        this.size = false;\n        this.speed = 2;\n        this.spin = new Spin();\n        this.straight = false;\n        this.trail = new MoveTrail();\n        this.vibrate = false;\n        this.warp = false;\n    }\n    get bounce() {\n        return this.collisions;\n    }\n    set bounce(value) {\n        this.collisions = value;\n    }\n    get collisions() {\n        return false;\n    }\n    set collisions(_) {\n    }\n    get noise() {\n        return this.path;\n    }\n    set noise(value) {\n        this.path = value;\n    }\n    get outMode() {\n        return this.outModes.default;\n    }\n    set outMode(value) {\n        this.outModes.default = value;\n    }\n    get out_mode() {\n        return this.outMode;\n    }\n    set out_mode(value) {\n        this.outMode = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.angle.load(isNumber(data.angle) ? { value: data.angle } : data.angle);\n        this.attract.load(data.attract);\n        this.center.load(data.center);\n        if (data.decay !== undefined) {\n            this.decay = setRangeValue(data.decay);\n        }\n        if (data.direction !== undefined) {\n            this.direction = data.direction;\n        }\n        if (data.distance !== undefined) {\n            this.distance = isNumber(data.distance)\n                ? {\n                    horizontal: data.distance,\n                    vertical: data.distance,\n                }\n                : { ...data.distance };\n        }\n        if (data.drift !== undefined) {\n            this.drift = setRangeValue(data.drift);\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        this.gravity.load(data.gravity);\n        const outModes = data.outModes ?? data.outMode ?? data.out_mode;\n        if (outModes !== undefined) {\n            if (isObject(outModes)) {\n                this.outModes.load(outModes);\n            }\n            else {\n                this.outModes.load({\n                    default: outModes,\n                });\n            }\n        }\n        this.path.load(data.path ?? data.noise);\n        if (data.random !== undefined) {\n            this.random = data.random;\n        }\n        if (data.size !== undefined) {\n            this.size = data.size;\n        }\n        if (data.speed !== undefined) {\n            this.speed = setRangeValue(data.speed);\n        }\n        this.spin.load(data.spin);\n        if (data.straight !== undefined) {\n            this.straight = data.straight;\n        }\n        this.trail.load(data.trail);\n        if (data.vibrate !== undefined) {\n            this.vibrate = data.vibrate;\n        }\n        if (data.warp !== undefined) {\n            this.warp = data.warp;\n        }\n    }\n}\n", "import { RangedAnimationOptions } from \"../../AnimationOptions\";\nexport class OpacityAnimation extends RangedAnimationOptions {\n    constructor() {\n        super();\n        this.destroy = \"none\";\n        this.speed = 2;\n    }\n    get opacity_min() {\n        return this.minimumValue;\n    }\n    set opacity_min(value) {\n        this.minimumValue = value;\n    }\n    load(data) {\n        if (data?.opacity_min !== undefined && data.minimumValue === undefined) {\n            data.minimumValue = data.opacity_min;\n        }\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n    }\n}\n", "import { OpacityAnimation } from \"./OpacityAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class Opacity extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new OpacityAnimation();\n        this.random.minimumValue = 0.1;\n        this.value = 1;\n    }\n    get anim() {\n        return this.animation;\n    }\n    set anim(value) {\n        this.animation = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        super.load(data);\n        const animation = data.animation ?? data.anim;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n            this.value = setRangeValue(this.value, this.animation.enable ? this.animation.minimumValue : undefined);\n        }\n    }\n}\n", "export class ParticlesDensity {\n    constructor() {\n        this.enable = false;\n        this.width = 1920;\n        this.height = 1080;\n    }\n    get area() {\n        return this.width;\n    }\n    set area(value) {\n        this.width = value;\n    }\n    get factor() {\n        return this.height;\n    }\n    set factor(value) {\n        this.height = value;\n    }\n    get value_area() {\n        return this.area;\n    }\n    set value_area(value) {\n        this.area = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        const width = data.width ?? data.area ?? data.value_area;\n        if (width !== undefined) {\n            this.width = width;\n        }\n        const height = data.height ?? data.factor;\n        if (height !== undefined) {\n            this.height = height;\n        }\n    }\n}\n", "import { ParticlesDensity } from \"./ParticlesDensity\";\nexport class ParticlesNumber {\n    constructor() {\n        this.density = new ParticlesDensity();\n        this.limit = 0;\n        this.value = 0;\n    }\n    get max() {\n        return this.limit;\n    }\n    set max(value) {\n        this.limit = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.density.load(data.density);\n        const limit = data.limit ?? data.max;\n        if (limit !== undefined) {\n            this.limit = limit;\n        }\n        if (data.value !== undefined) {\n            this.value = data.value;\n        }\n    }\n}\n", "import { OptionsColor } from \"../OptionsColor\";\nexport class Shadow {\n    constructor() {\n        this.blur = 0;\n        this.color = new OptionsColor();\n        this.enable = false;\n        this.offset = {\n            x: 0,\n            y: 0,\n        };\n        this.color.value = \"#000\";\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.blur !== undefined) {\n            this.blur = data.blur;\n        }\n        this.color = OptionsColor.create(this.color, data.color);\n        if (data.enable !== undefined) {\n            this.enable = data.enable;\n        }\n        if (data.offset === undefined) {\n            return;\n        }\n        if (data.offset.x !== undefined) {\n            this.offset.x = data.offset.x;\n        }\n        if (data.offset.y !== undefined) {\n            this.offset.y = data.offset.y;\n        }\n    }\n}\n", "import { deepExtend, isArray } from \"../../../../Utils/Utils\";\nconst charKey = \"character\", charAltKey = \"char\", imageKey = \"image\", imageAltKey = \"images\", polygonKey = \"polygon\", polygonAltKey = \"star\";\nexport class Shape {\n    constructor() {\n        this.loadShape = (item, mainKey, altKey, altOverride) => {\n            if (!item) {\n                return;\n            }\n            const itemIsArray = isArray(item), emptyValue = itemIsArray ? [] : {}, mainDifferentValues = itemIsArray !== isArray(this.options[mainKey]), altDifferentValues = itemIsArray !== isArray(this.options[altKey]);\n            if (mainDifferentValues) {\n                this.options[mainKey] = emptyValue;\n            }\n            if (altDifferentValues && altOverride) {\n                this.options[altKey] = emptyValue;\n            }\n            this.options[mainKey] = deepExtend(this.options[mainKey] ?? emptyValue, item);\n            if (!this.options[altKey] || altOverride) {\n                this.options[altKey] = deepExtend(this.options[altKey] ?? emptyValue, item);\n            }\n        };\n        this.close = true;\n        this.fill = true;\n        this.options = {};\n        this.type = \"circle\";\n    }\n    get character() {\n        return (this.options[charKey] ?? this.options[charAltKey]);\n    }\n    set character(value) {\n        this.options[charAltKey] = this.options[charKey] = value;\n    }\n    get custom() {\n        return this.options;\n    }\n    set custom(value) {\n        this.options = value;\n    }\n    get image() {\n        return (this.options[imageKey] ?? this.options[imageAltKey]);\n    }\n    set image(value) {\n        this.options[imageAltKey] = this.options[imageKey] = value;\n    }\n    get images() {\n        return this.image;\n    }\n    set images(value) {\n        this.image = value;\n    }\n    get polygon() {\n        return (this.options[polygonKey] ?? this.options[polygonAltKey]);\n    }\n    set polygon(value) {\n        this.options[polygonAltKey] = this.options[polygonKey] = value;\n    }\n    get stroke() {\n        return [];\n    }\n    set stroke(_value) {\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        const options = data.options ?? data.custom;\n        if (options !== undefined) {\n            for (const shape in options) {\n                const item = options[shape];\n                if (item) {\n                    this.options[shape] = deepExtend(this.options[shape] ?? {}, item);\n                }\n            }\n        }\n        this.loadShape(data.character, charKey, charAltKey, true);\n        this.loadShape(data.polygon, polygonKey, polygonAltKey, false);\n        this.loadShape(data.image ?? data.images, imageKey, imageAltKey, true);\n        if (data.close !== undefined) {\n            this.close = data.close;\n        }\n        if (data.fill !== undefined) {\n            this.fill = data.fill;\n        }\n        if (data.type !== undefined) {\n            this.type = data.type;\n        }\n    }\n}\n", "import { RangedAnimationOptions } from \"../../AnimationOptions\";\nexport class SizeAnimation extends RangedAnimationOptions {\n    constructor() {\n        super();\n        this.destroy = \"none\";\n        this.speed = 5;\n    }\n    get size_min() {\n        return this.minimumValue;\n    }\n    set size_min(value) {\n        this.minimumValue = value;\n    }\n    load(data) {\n        if (data?.size_min !== undefined && data.minimumValue === undefined) {\n            data.minimumValue = data.size_min;\n        }\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.destroy !== undefined) {\n            this.destroy = data.destroy;\n        }\n    }\n}\n", "import { SizeAnimation } from \"./SizeAnimation\";\nimport { ValueWithRandom } from \"../../ValueWithRandom\";\nimport { setRangeValue } from \"../../../../Utils/NumberUtils\";\nexport class Size extends ValueWithRandom {\n    constructor() {\n        super();\n        this.animation = new SizeAnimation();\n        this.random.minimumValue = 1;\n        this.value = 3;\n    }\n    get anim() {\n        return this.animation;\n    }\n    set anim(value) {\n        this.animation = value;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        const animation = data.animation ?? data.anim;\n        if (animation !== undefined) {\n            this.animation.load(animation);\n            this.value = setRangeValue(this.value, this.animation.enable ? this.animation.minimumValue : undefined);\n        }\n    }\n}\n", "import { AnimatableColor } from \"../AnimatableColor\";\nimport { setRangeValue } from \"../../../Utils/NumberUtils\";\nexport class Stroke {\n    constructor() {\n        this.width = 0;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.color !== undefined) {\n            this.color = AnimatableColor.create(this.color, data.color);\n        }\n        if (data.width !== undefined) {\n            this.width = setRangeValue(data.width);\n        }\n        if (data.opacity !== undefined) {\n            this.opacity = setRangeValue(data.opacity);\n        }\n    }\n}\n", "import { ValueWithRandom } from \"../../ValueWithRandom\";\nexport class ZIndex extends ValueWithRandom {\n    constructor() {\n        super();\n        this.opacityRate = 1;\n        this.sizeRate = 1;\n        this.velocityRate = 1;\n    }\n    load(data) {\n        super.load(data);\n        if (!data) {\n            return;\n        }\n        if (data.opacityRate !== undefined) {\n            this.opacityRate = data.opacityRate;\n        }\n        if (data.sizeRate !== undefined) {\n            this.sizeRate = data.sizeRate;\n        }\n        if (data.velocityRate !== undefined) {\n            this.velocityRate = data.velocityRate;\n        }\n    }\n}\n", "import { deepExtend, executeOnSingleOrMultiple } from \"../../../Utils/Utils\";\nimport { AnimatableColor } from \"../AnimatableColor\";\nimport { Collisions } from \"./Collisions/Collisions\";\nimport { Move } from \"./Move/Move\";\nimport { Opacity } from \"./Opacity/Opacity\";\nimport { ParticlesBounce } from \"./Bounce/ParticlesBounce\";\nimport { ParticlesNumber } from \"./Number/ParticlesNumber\";\nimport { Shadow } from \"./Shadow\";\nimport { Shape } from \"./Shape/Shape\";\nimport { Size } from \"./Size/Size\";\nimport { Stroke } from \"./Stroke\";\nimport { ZIndex } from \"./ZIndex/ZIndex\";\nexport class ParticlesOptions {\n    constructor(engine, container) {\n        this._engine = engine;\n        this._container = container;\n        this.bounce = new ParticlesBounce();\n        this.collisions = new Collisions();\n        this.color = new AnimatableColor();\n        this.color.value = \"#fff\";\n        this.groups = {};\n        this.move = new Move();\n        this.number = new ParticlesNumber();\n        this.opacity = new Opacity();\n        this.reduceDuplicates = false;\n        this.shadow = new Shadow();\n        this.shape = new Shape();\n        this.size = new Size();\n        this.stroke = new Stroke();\n        this.zIndex = new ZIndex();\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        this.bounce.load(data.bounce);\n        this.color.load(AnimatableColor.create(this.color, data.color));\n        if (data.groups !== undefined) {\n            for (const group in data.groups) {\n                const item = data.groups[group];\n                if (item !== undefined) {\n                    this.groups[group] = deepExtend(this.groups[group] ?? {}, item);\n                }\n            }\n        }\n        this.move.load(data.move);\n        this.number.load(data.number);\n        this.opacity.load(data.opacity);\n        if (data.reduceDuplicates !== undefined) {\n            this.reduceDuplicates = data.reduceDuplicates;\n        }\n        this.shape.load(data.shape);\n        this.size.load(data.size);\n        this.shadow.load(data.shadow);\n        this.zIndex.load(data.zIndex);\n        const collisions = data.move?.collisions ?? data.move?.bounce;\n        if (collisions !== undefined) {\n            this.collisions.enable = collisions;\n        }\n        this.collisions.load(data.collisions);\n        if (data.interactivity !== undefined) {\n            this.interactivity = deepExtend({}, data.interactivity);\n        }\n        const strokeToLoad = data.stroke ?? data.shape?.stroke;\n        if (strokeToLoad) {\n            this.stroke = executeOnSingleOrMultiple(strokeToLoad, (t) => {\n                const tmp = new Stroke();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        if (this._container) {\n            const updaters = this._engine.plugins.updaters.get(this._container);\n            if (updaters) {\n                for (const updater of updaters) {\n                    if (updater.loadOptions) {\n                        updater.loadOptions(this, data);\n                    }\n                }\n            }\n            const interactors = this._engine.plugins.interactors.get(this._container);\n            if (interactors) {\n                for (const interactor of interactors) {\n                    if (interactor.loadParticlesOptions) {\n                        interactor.loadParticlesOptions(this, data);\n                    }\n                }\n            }\n        }\n    }\n}\n", "import { ParticlesOptions } from \"../Options/Classes/Particles/ParticlesOptions\";\nexport function loadOptions(options, ...sourceOptionsArr) {\n    for (const sourceOptions of sourceOptionsArr) {\n        options.load(sourceOptions);\n    }\n}\nexport function loadParticlesOptions(engine, container, ...sourceOptionsArr) {\n    const options = new ParticlesOptions(engine, container);\n    loadOptions(options, ...sourceOptionsArr);\n    return options;\n}\n", "import { deepExtend, executeOnSingleOrMultiple, isBoolean, safeMatchMedia } from \"../../Utils/Utils\";\nimport { Background } from \"./Background/Background\";\nimport { BackgroundMask } from \"./BackgroundMask/BackgroundMask\";\nimport { FullScreen } from \"./FullScreen/FullScreen\";\nimport { Interactivity } from \"./Interactivity/Interactivity\";\nimport { ManualParticle } from \"./ManualParticle\";\nimport { Responsive } from \"./Responsive\";\nimport { Theme } from \"./Theme/Theme\";\nimport { loadParticlesOptions } from \"../../Utils/OptionsUtils\";\nimport { setRangeValue } from \"../../Utils/NumberUtils\";\nexport class Options {\n    constructor(engine, container) {\n        this._findDefaultTheme = (mode) => {\n            return (this.themes.find((theme) => theme.default.value && theme.default.mode === mode) ??\n                this.themes.find((theme) => theme.default.value && theme.default.mode === \"any\"));\n        };\n        this._importPreset = (preset) => {\n            this.load(this._engine.plugins.getPreset(preset));\n        };\n        this._engine = engine;\n        this._container = container;\n        this.autoPlay = true;\n        this.background = new Background();\n        this.backgroundMask = new BackgroundMask();\n        this.defaultThemes = {};\n        this.delay = 0;\n        this.fullScreen = new FullScreen();\n        this.detectRetina = true;\n        this.duration = 0;\n        this.fpsLimit = 120;\n        this.interactivity = new Interactivity(engine, container);\n        this.manualParticles = [];\n        this.particles = loadParticlesOptions(this._engine, this._container);\n        this.pauseOnBlur = true;\n        this.pauseOnOutsideViewport = true;\n        this.responsive = [];\n        this.smooth = false;\n        this.style = {};\n        this.themes = [];\n        this.zLayers = 100;\n    }\n    get backgroundMode() {\n        return this.fullScreen;\n    }\n    set backgroundMode(value) {\n        this.fullScreen.load(value);\n    }\n    get fps_limit() {\n        return this.fpsLimit;\n    }\n    set fps_limit(value) {\n        this.fpsLimit = value;\n    }\n    get retina_detect() {\n        return this.detectRetina;\n    }\n    set retina_detect(value) {\n        this.detectRetina = value;\n    }\n    load(data) {\n        if (!data) {\n            return;\n        }\n        if (data.preset !== undefined) {\n            executeOnSingleOrMultiple(data.preset, (preset) => this._importPreset(preset));\n        }\n        if (data.autoPlay !== undefined) {\n            this.autoPlay = data.autoPlay;\n        }\n        if (data.delay !== undefined) {\n            this.delay = setRangeValue(data.delay);\n        }\n        const detectRetina = data.detectRetina ?? data.retina_detect;\n        if (detectRetina !== undefined) {\n            this.detectRetina = detectRetina;\n        }\n        if (data.duration !== undefined) {\n            this.duration = setRangeValue(data.duration);\n        }\n        const fpsLimit = data.fpsLimit ?? data.fps_limit;\n        if (fpsLimit !== undefined) {\n            this.fpsLimit = fpsLimit;\n        }\n        if (data.pauseOnBlur !== undefined) {\n            this.pauseOnBlur = data.pauseOnBlur;\n        }\n        if (data.pauseOnOutsideViewport !== undefined) {\n            this.pauseOnOutsideViewport = data.pauseOnOutsideViewport;\n        }\n        if (data.zLayers !== undefined) {\n            this.zLayers = data.zLayers;\n        }\n        this.background.load(data.background);\n        const fullScreen = data.fullScreen ?? data.backgroundMode;\n        if (isBoolean(fullScreen)) {\n            this.fullScreen.enable = fullScreen;\n        }\n        else {\n            this.fullScreen.load(fullScreen);\n        }\n        this.backgroundMask.load(data.backgroundMask);\n        this.interactivity.load(data.interactivity);\n        if (data.manualParticles) {\n            this.manualParticles = data.manualParticles.map((t) => {\n                const tmp = new ManualParticle();\n                tmp.load(t);\n                return tmp;\n            });\n        }\n        this.particles.load(data.particles);\n        this.style = deepExtend(this.style, data.style);\n        this._engine.plugins.loadOptions(this, data);\n        if (data.smooth !== undefined) {\n            this.smooth = data.smooth;\n        }\n        const interactors = this._engine.plugins.interactors.get(this._container);\n        if (interactors) {\n            for (const interactor of interactors) {\n                if (interactor.loadOptions) {\n                    interactor.loadOptions(this, data);\n                }\n            }\n        }\n        if (data.responsive !== undefined) {\n            for (const responsive of data.responsive) {\n                const optResponsive = new Responsive();\n                optResponsive.load(responsive);\n                this.responsive.push(optResponsive);\n            }\n        }\n        this.responsive.sort((a, b) => a.maxWidth - b.maxWidth);\n        if (data.themes !== undefined) {\n            for (const theme of data.themes) {\n                const existingTheme = this.themes.find((t) => t.name === theme.name);\n                if (!existingTheme) {\n                    const optTheme = new Theme();\n                    optTheme.load(theme);\n                    this.themes.push(optTheme);\n                }\n                else {\n                    existingTheme.load(theme);\n                }\n            }\n        }\n        this.defaultThemes.dark = this._findDefaultTheme(\"dark\")?.name;\n        this.defaultThemes.light = this._findDefaultTheme(\"light\")?.name;\n    }\n    setResponsive(width, pxRatio, defaultOptions) {\n        this.load(defaultOptions);\n        const responsiveOptions = this.responsive.find((t) => t.mode === \"screen\" && screen ? t.maxWidth > screen.availWidth : t.maxWidth * pxRatio > width);\n        this.load(responsiveOptions?.options);\n        return responsiveOptions?.maxWidth;\n    }\n    setTheme(name) {\n        if (name) {\n            const chosenTheme = this.themes.find((theme) => theme.name === name);\n            if (chosenTheme) {\n                this.load(chosenTheme.options);\n            }\n        }\n        else {\n            const mediaMatch = safeMatchMedia(\"(prefers-color-scheme: dark)\"), clientDarkMode = mediaMatch && mediaMatch.matches, defaultTheme = this._findDefaultTheme(clientDarkMode ? \"dark\" : \"light\");\n            if (defaultTheme) {\n                this.load(defaultTheme.options);\n            }\n        }\n    }\n}\n", "export class InteractionManager {\n    constructor(engine, container) {\n        this.container = container;\n        this._engine = engine;\n        this._interactors = engine.plugins.getInteractors(this.container, true);\n        this._externalInteractors = [];\n        this._particleInteractors = [];\n    }\n    async externalInteract(delta) {\n        for (const interactor of this._externalInteractors) {\n            interactor.isEnabled() && (await interactor.interact(delta));\n        }\n    }\n    handleClickMode(mode) {\n        for (const interactor of this._externalInteractors) {\n            interactor.handleClickMode && interactor.handleClickMode(mode);\n        }\n    }\n    init() {\n        this._externalInteractors = [];\n        this._particleInteractors = [];\n        for (const interactor of this._interactors) {\n            switch (interactor.type) {\n                case \"external\":\n                    this._externalInteractors.push(interactor);\n                    break;\n                case \"particles\":\n                    this._particleInteractors.push(interactor);\n                    break;\n            }\n            interactor.init();\n        }\n    }\n    async particlesInteract(particle, delta) {\n        for (const interactor of this._externalInteractors) {\n            interactor.clear(particle, delta);\n        }\n        for (const interactor of this._particleInteractors) {\n            interactor.isEnabled(particle) && (await interactor.interact(particle, delta));\n        }\n    }\n    async reset(particle) {\n        for (const interactor of this._externalInteractors) {\n            interactor.isEnabled() && interactor.reset(particle);\n        }\n        for (const interactor of this._particleInteractors) {\n            interactor.isEnabled(particle) && interactor.reset(particle);\n        }\n    }\n}\n", "import { calcExactPositionOrRandomFromSize, clamp, getDistance, getParticleBaseVelocity, getParticleDirectionAngle, getRandom, getRangeValue, getValue, randomInRange, setRangeValue, } from \"../Utils/NumberUtils\";\nimport { deepExtend, getPosition, initParticleNumericAnimationValue, isInArray, itemFromSingleOrMultiple, } from \"../Utils/Utils\";\nimport { getHslFromAnimation, rangeColorToRgb } from \"../Utils/ColorUtils\";\nimport { Interactivity } from \"../Options/Classes/Interactivity/Interactivity\";\nimport { Vector } from \"./Utils/Vector\";\nimport { Vector3d } from \"./Utils/Vector3d\";\nimport { alterHsl } from \"../Utils/CanvasUtils\";\nimport { errorPrefix } from \"./Utils/Constants\";\nimport { loadParticlesOptions } from \"../Utils/OptionsUtils\";\nconst fixOutMode = (data) => {\n    if (!isInArray(data.outMode, data.checkModes)) {\n        return;\n    }\n    const diameter = data.radius * 2;\n    if (data.coord > data.maxCoord - diameter) {\n        data.setCb(-data.radius);\n    }\n    else if (data.coord < diameter) {\n        data.setCb(data.radius);\n    }\n};\nexport class Particle {\n    constructor(engine, id, container, position, overrideOptions, group) {\n        this.container = container;\n        this._calcPosition = (container, position, zIndex, tryCount = 0) => {\n            for (const [, plugin] of container.plugins) {\n                const pluginPos = plugin.particlePosition !== undefined ? plugin.particlePosition(position, this) : undefined;\n                if (pluginPos) {\n                    return Vector3d.create(pluginPos.x, pluginPos.y, zIndex);\n                }\n            }\n            const canvasSize = container.canvas.size, exactPosition = calcExactPositionOrRandomFromSize({\n                size: canvasSize,\n                position: position,\n            }), pos = Vector3d.create(exactPosition.x, exactPosition.y, zIndex), radius = this.getRadius(), outModes = this.options.move.outModes, fixHorizontal = (outMode) => {\n                fixOutMode({\n                    outMode,\n                    checkModes: [\"bounce\", \"bounce-horizontal\"],\n                    coord: pos.x,\n                    maxCoord: container.canvas.size.width,\n                    setCb: (value) => (pos.x += value),\n                    radius,\n                });\n            }, fixVertical = (outMode) => {\n                fixOutMode({\n                    outMode,\n                    checkModes: [\"bounce\", \"bounce-vertical\"],\n                    coord: pos.y,\n                    maxCoord: container.canvas.size.height,\n                    setCb: (value) => (pos.y += value),\n                    radius,\n                });\n            };\n            fixHorizontal(outModes.left ?? outModes.default);\n            fixHorizontal(outModes.right ?? outModes.default);\n            fixVertical(outModes.top ?? outModes.default);\n            fixVertical(outModes.bottom ?? outModes.default);\n            if (this._checkOverlap(pos, tryCount)) {\n                return this._calcPosition(container, undefined, zIndex, tryCount + 1);\n            }\n            return pos;\n        };\n        this._calculateVelocity = () => {\n            const baseVelocity = getParticleBaseVelocity(this.direction), res = baseVelocity.copy(), moveOptions = this.options.move;\n            if (moveOptions.direction === \"inside\" || moveOptions.direction === \"outside\") {\n                return res;\n            }\n            const rad = (Math.PI / 180) * getRangeValue(moveOptions.angle.value), radOffset = (Math.PI / 180) * getRangeValue(moveOptions.angle.offset), range = {\n                left: radOffset - rad / 2,\n                right: radOffset + rad / 2,\n            };\n            if (!moveOptions.straight) {\n                res.angle += randomInRange(setRangeValue(range.left, range.right));\n            }\n            if (moveOptions.random && typeof moveOptions.speed === \"number\") {\n                res.length *= getRandom();\n            }\n            return res;\n        };\n        this._checkOverlap = (pos, tryCount = 0) => {\n            const collisionsOptions = this.options.collisions, radius = this.getRadius();\n            if (!collisionsOptions.enable) {\n                return false;\n            }\n            const overlapOptions = collisionsOptions.overlap;\n            if (overlapOptions.enable) {\n                return false;\n            }\n            const retries = overlapOptions.retries;\n            if (retries >= 0 && tryCount > retries) {\n                throw new Error(`${errorPrefix} particle is overlapping and can't be placed`);\n            }\n            return !!this.container.particles.find((particle) => getDistance(pos, particle.position) < radius + particle.getRadius());\n        };\n        this._getRollColor = (color) => {\n            if (!color || !this.roll || (!this.backColor && !this.roll.alter)) {\n                return color;\n            }\n            const backFactor = this.roll.horizontal && this.roll.vertical ? 2 : 1, backSum = this.roll.horizontal ? Math.PI / 2 : 0, rolled = Math.floor(((this.roll.angle ?? 0) + backSum) / (Math.PI / backFactor)) % 2;\n            if (!rolled) {\n                return color;\n            }\n            if (this.backColor) {\n                return this.backColor;\n            }\n            if (this.roll.alter) {\n                return alterHsl(color, this.roll.alter.type, this.roll.alter.value);\n            }\n            return color;\n        };\n        this._initPosition = (position) => {\n            const container = this.container, zIndexValue = getRangeValue(this.options.zIndex.value);\n            this.position = this._calcPosition(container, position, clamp(zIndexValue, 0, container.zLayers));\n            this.initialPosition = this.position.copy();\n            const canvasSize = container.canvas.size;\n            this.moveCenter = {\n                ...getPosition(this.options.move.center, canvasSize),\n                radius: this.options.move.center.radius ?? 0,\n                mode: this.options.move.center.mode ?? \"percent\",\n            };\n            this.direction = getParticleDirectionAngle(this.options.move.direction, this.position, this.moveCenter);\n            switch (this.options.move.direction) {\n                case \"inside\":\n                    this.outType = \"inside\";\n                    break;\n                case \"outside\":\n                    this.outType = \"outside\";\n                    break;\n            }\n            this.offset = Vector.origin;\n        };\n        this._loadShapeData = (shapeOptions, reduceDuplicates) => {\n            const shapeData = shapeOptions.options[this.shape];\n            if (!shapeData) {\n                return;\n            }\n            return deepExtend({\n                close: shapeOptions.close,\n                fill: shapeOptions.fill,\n            }, itemFromSingleOrMultiple(shapeData, this.id, reduceDuplicates));\n        };\n        this._engine = engine;\n        this.init(id, position, overrideOptions, group);\n    }\n    destroy(override) {\n        if (this.unbreakable || this.destroyed) {\n            return;\n        }\n        this.destroyed = true;\n        this.bubble.inRange = false;\n        this.slow.inRange = false;\n        const container = this.container, pathGenerator = this.pathGenerator;\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particleDestroyed) {\n                plugin.particleDestroyed(this, override);\n            }\n        }\n        for (const updater of container.particles.updaters) {\n            if (updater.particleDestroyed) {\n                updater.particleDestroyed(this, override);\n            }\n        }\n        if (pathGenerator) {\n            pathGenerator.reset(this);\n        }\n    }\n    draw(delta) {\n        const container = this.container;\n        for (const [, plugin] of container.plugins) {\n            container.canvas.drawParticlePlugin(plugin, this, delta);\n        }\n        container.canvas.drawParticle(this, delta);\n    }\n    getFillColor() {\n        return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.color));\n    }\n    getMass() {\n        return (this.getRadius() ** 2 * Math.PI) / 2;\n    }\n    getPosition() {\n        return {\n            x: this.position.x + this.offset.x,\n            y: this.position.y + this.offset.y,\n            z: this.position.z,\n        };\n    }\n    getRadius() {\n        return this.bubble.radius ?? this.size.value;\n    }\n    getStrokeColor() {\n        return this._getRollColor(this.bubble.color ?? getHslFromAnimation(this.strokeColor));\n    }\n    init(id, position, overrideOptions, group) {\n        const container = this.container, engine = this._engine;\n        this.id = id;\n        this.group = group;\n        this.fill = true;\n        this.pathRotation = false;\n        this.close = true;\n        this.lastPathTime = 0;\n        this.destroyed = false;\n        this.unbreakable = false;\n        this.rotation = 0;\n        this.misplaced = false;\n        this.retina = {\n            maxDistance: {},\n        };\n        this.outType = \"normal\";\n        this.ignoresResizeRatio = true;\n        const pxRatio = container.retina.pixelRatio, mainOptions = container.actualOptions, particlesOptions = loadParticlesOptions(this._engine, container, mainOptions.particles), shapeType = particlesOptions.shape.type, { reduceDuplicates } = particlesOptions;\n        this.shape = itemFromSingleOrMultiple(shapeType, this.id, reduceDuplicates);\n        const shapeOptions = particlesOptions.shape;\n        if (overrideOptions && overrideOptions.shape && overrideOptions.shape.type) {\n            const overrideShapeType = overrideOptions.shape.type, shape = itemFromSingleOrMultiple(overrideShapeType, this.id, reduceDuplicates);\n            if (shape) {\n                this.shape = shape;\n                shapeOptions.load(overrideOptions.shape);\n            }\n        }\n        this.shapeData = this._loadShapeData(shapeOptions, reduceDuplicates);\n        particlesOptions.load(overrideOptions);\n        const shapeData = this.shapeData;\n        if (shapeData) {\n            particlesOptions.load(shapeData.particles);\n        }\n        const interactivity = new Interactivity(engine, container);\n        interactivity.load(container.actualOptions.interactivity);\n        interactivity.load(particlesOptions.interactivity);\n        this.interactivity = interactivity;\n        this.fill = shapeData?.fill ?? particlesOptions.shape.fill;\n        this.close = shapeData?.close ?? particlesOptions.shape.close;\n        this.options = particlesOptions;\n        const pathOptions = this.options.move.path;\n        this.pathDelay = getValue(pathOptions.delay) * 1000;\n        if (pathOptions.generator) {\n            this.pathGenerator = this._engine.plugins.getPathGenerator(pathOptions.generator);\n            if (this.pathGenerator && container.addPath(pathOptions.generator, this.pathGenerator)) {\n                this.pathGenerator.init(container);\n            }\n        }\n        container.retina.initParticle(this);\n        this.size = initParticleNumericAnimationValue(this.options.size, pxRatio);\n        this.bubble = {\n            inRange: false,\n        };\n        this.slow = {\n            inRange: false,\n            factor: 1,\n        };\n        this._initPosition(position);\n        this.initialVelocity = this._calculateVelocity();\n        this.velocity = this.initialVelocity.copy();\n        this.moveDecay = 1 - getRangeValue(this.options.move.decay);\n        const particles = container.particles;\n        particles.needsSort = particles.needsSort || particles.lastZIndex < this.position.z;\n        particles.lastZIndex = this.position.z;\n        this.zIndexFactor = this.position.z / container.zLayers;\n        this.sides = 24;\n        let drawer = container.drawers.get(this.shape);\n        if (!drawer) {\n            drawer = this._engine.plugins.getShapeDrawer(this.shape);\n            if (drawer) {\n                container.drawers.set(this.shape, drawer);\n            }\n        }\n        if (drawer && drawer.loadShape) {\n            drawer.loadShape(this);\n        }\n        const sideCountFunc = drawer?.getSidesCount;\n        if (sideCountFunc) {\n            this.sides = sideCountFunc(this);\n        }\n        this.spawning = false;\n        this.shadowColor = rangeColorToRgb(this.options.shadow.color);\n        for (const updater of container.particles.updaters) {\n            updater.init(this);\n        }\n        for (const mover of container.particles.movers) {\n            mover.init && mover.init(this);\n        }\n        if (drawer && drawer.particleInit) {\n            drawer.particleInit(container, this);\n        }\n        for (const [, plugin] of container.plugins) {\n            plugin.particleCreated && plugin.particleCreated(this);\n        }\n    }\n    isInsideCanvas() {\n        const radius = this.getRadius(), canvasSize = this.container.canvas.size, position = this.position;\n        return (position.x >= -radius &&\n            position.y >= -radius &&\n            position.y <= canvasSize.height + radius &&\n            position.x <= canvasSize.width + radius);\n    }\n    isVisible() {\n        return !this.destroyed && !this.spawning && this.isInsideCanvas();\n    }\n    reset() {\n        for (const updater of this.container.particles.updaters) {\n            updater.reset && updater.reset(this);\n        }\n    }\n}\n", "export class Point {\n    constructor(position, particle) {\n        this.position = position;\n        this.particle = particle;\n    }\n}\n", "export class Range {\n    constructor(x, y) {\n        this.position = {\n            x: x,\n            y: y,\n        };\n    }\n}\n", "import { Circle } from \"./Circle\";\nimport { Range } from \"./Range\";\nexport class Rectangle extends Range {\n    constructor(x, y, width, height) {\n        super(x, y);\n        this.size = {\n            height: height,\n            width: width,\n        };\n    }\n    contains(point) {\n        const w = this.size.width, h = this.size.height, pos = this.position;\n        return point.x >= pos.x && point.x <= pos.x + w && point.y >= pos.y && point.y <= pos.y + h;\n    }\n    intersects(range) {\n        if (range instanceof Circle) {\n            range.intersects(this);\n        }\n        const w = this.size.width, h = this.size.height, pos1 = this.position, pos2 = range.position, size2 = range instanceof Rectangle ? range.size : { width: 0, height: 0 }, w2 = size2.width, h2 = size2.height;\n        return pos2.x < pos1.x + w && pos2.x + w2 > pos1.x && pos2.y < pos1.y + h && pos2.y + h2 > pos1.y;\n    }\n}\n", "import { Range } from \"./Range\";\nimport { Rectangle } from \"./Rectangle\";\nimport { getDistance } from \"../../Utils/NumberUtils\";\nexport class Circle extends Range {\n    constructor(x, y, radius) {\n        super(x, y);\n        this.radius = radius;\n    }\n    contains(point) {\n        return getDistance(point, this.position) <= this.radius;\n    }\n    intersects(range) {\n        const pos1 = this.position, pos2 = range.position, distPos = { x: Math.abs(pos2.x - pos1.x), y: Math.abs(pos2.y - pos1.y) }, r = this.radius;\n        if (range instanceof Circle) {\n            const rSum = r + range.radius, dist = Math.sqrt(distPos.x ** 2 + distPos.y ** 2);\n            return rSum > dist;\n        }\n        else if (range instanceof Rectangle) {\n            const { width, height } = range.size, edges = Math.pow(distPos.x - width, 2) + Math.pow(distPos.y - height, 2);\n            return (edges <= r ** 2 ||\n                (distPos.x <= r + width && distPos.y <= r + height) ||\n                distPos.x <= width ||\n                distPos.y <= height);\n        }\n        return false;\n    }\n}\n", "import { Circle } from \"./Circle\";\nimport { Rectangle } from \"./Rectangle\";\nimport { getDistance } from \"../../Utils/NumberUtils\";\nexport class QuadTree {\n    constructor(rectangle, capacity) {\n        this.rectangle = rectangle;\n        this.capacity = capacity;\n        this._subdivide = () => {\n            const { x, y } = this.rectangle.position, { width, height } = this.rectangle.size, { capacity } = this;\n            for (let i = 0; i < 4; i++) {\n                this._subs.push(new QuadTree(new Rectangle(x + (width / 2) * (i % 2), y + (height / 2) * (Math.round(i / 2) - (i % 2)), width / 2, height / 2), capacity));\n            }\n            this._divided = true;\n        };\n        this._points = [];\n        this._divided = false;\n        this._subs = [];\n    }\n    insert(point) {\n        if (!this.rectangle.contains(point.position)) {\n            return false;\n        }\n        if (this._points.length < this.capacity) {\n            this._points.push(point);\n            return true;\n        }\n        if (!this._divided) {\n            this._subdivide();\n        }\n        return this._subs.some((sub) => sub.insert(point));\n    }\n    query(range, check, found) {\n        const res = found || [];\n        if (!range.intersects(this.rectangle)) {\n            return [];\n        }\n        for (const p of this._points) {\n            if (!range.contains(p.position) &&\n                getDistance(range.position, p.position) > p.particle.getRadius() &&\n                (!check || check(p.particle))) {\n                continue;\n            }\n            res.push(p.particle);\n        }\n        if (this._divided) {\n            for (const sub of this._subs) {\n                sub.query(range, check, res);\n            }\n        }\n        return res;\n    }\n    queryCircle(position, radius, check) {\n        return this.query(new Circle(position.x, position.y, radius), check);\n    }\n    queryRectangle(position, size, check) {\n        return this.query(new Rectangle(position.x, position.y, size.width, size.height), check);\n    }\n}\n", "import { getLogger, getPosition } from \"../Utils/Utils\";\nimport { InteractionManager } from \"./Utils/InteractionManager\";\nimport { Particle } from \"./Particle\";\nimport { Point } from \"./Utils/Point\";\nimport { QuadTree } from \"./Utils/QuadTree\";\nimport { Rectangle } from \"./Utils/Rectangle\";\nimport { errorPrefix } from \"./Utils/Constants\";\nconst qTreeCapacity = 4;\nconst qTreeRectangle = (canvasSize) => {\n    return new Rectangle(-canvasSize.width / 4, -canvasSize.height / 4, (canvasSize.width * 3) / 2, (canvasSize.height * 3) / 2);\n};\nexport class Particles {\n    constructor(engine, container) {\n        this._applyDensity = (options, manualCount, group) => {\n            if (!options.number.density?.enable) {\n                return;\n            }\n            const numberOptions = options.number, densityFactor = this._initDensityFactor(numberOptions.density), optParticlesNumber = numberOptions.value, optParticlesLimit = numberOptions.limit > 0 ? numberOptions.limit : optParticlesNumber, particlesNumber = Math.min(optParticlesNumber, optParticlesLimit) * densityFactor + manualCount, particlesCount = Math.min(this.count, this.filter((t) => t.group === group).length);\n            this.limit = numberOptions.limit * densityFactor;\n            if (particlesCount < particlesNumber) {\n                this.push(Math.abs(particlesNumber - particlesCount), undefined, options, group);\n            }\n            else if (particlesCount > particlesNumber) {\n                this.removeQuantity(particlesCount - particlesNumber, group);\n            }\n        };\n        this._initDensityFactor = (densityOptions) => {\n            const container = this._container;\n            if (!container.canvas.element || !densityOptions.enable) {\n                return 1;\n            }\n            const canvas = container.canvas.element, pxRatio = container.retina.pixelRatio;\n            return (canvas.width * canvas.height) / (densityOptions.factor * pxRatio ** 2 * densityOptions.area);\n        };\n        this._pushParticle = (position, overrideOptions, group, initializer) => {\n            try {\n                let particle = this.pool.pop();\n                if (particle) {\n                    particle.init(this._nextId, position, overrideOptions, group);\n                }\n                else {\n                    particle = new Particle(this._engine, this._nextId, this._container, position, overrideOptions, group);\n                }\n                let canAdd = true;\n                if (initializer) {\n                    canAdd = initializer(particle);\n                }\n                if (!canAdd) {\n                    return;\n                }\n                this._array.push(particle);\n                this._zArray.push(particle);\n                this._nextId++;\n                this._engine.dispatchEvent(\"particleAdded\", {\n                    container: this._container,\n                    data: {\n                        particle,\n                    },\n                });\n                return particle;\n            }\n            catch (e) {\n                getLogger().warning(`${errorPrefix} adding particle: ${e}`);\n                return;\n            }\n        };\n        this._removeParticle = (index, group, override) => {\n            const particle = this._array[index];\n            if (!particle || particle.group !== group) {\n                return false;\n            }\n            particle.destroy(override);\n            const zIdx = this._zArray.indexOf(particle);\n            this._array.splice(index, 1);\n            this._zArray.splice(zIdx, 1);\n            this.pool.push(particle);\n            this._engine.dispatchEvent(\"particleRemoved\", {\n                container: this._container,\n                data: {\n                    particle,\n                },\n            });\n            return true;\n        };\n        this._engine = engine;\n        this._container = container;\n        this._nextId = 0;\n        this._array = [];\n        this._zArray = [];\n        this.pool = [];\n        this.limit = 0;\n        this.needsSort = false;\n        this.lastZIndex = 0;\n        this._interactionManager = new InteractionManager(engine, container);\n        const canvasSize = container.canvas.size;\n        this.quadTree = new QuadTree(qTreeRectangle(canvasSize), qTreeCapacity);\n        this.movers = this._engine.plugins.getMovers(container, true);\n        this.updaters = this._engine.plugins.getUpdaters(container, true);\n    }\n    get count() {\n        return this._array.length;\n    }\n    addManualParticles() {\n        const container = this._container, options = container.actualOptions;\n        for (const particle of options.manualParticles) {\n            this.addParticle(particle.position ? getPosition(particle.position, container.canvas.size) : undefined, particle.options);\n        }\n    }\n    addParticle(position, overrideOptions, group, initializer) {\n        const container = this._container, options = container.actualOptions, limit = options.particles.number.limit;\n        if (limit > 0) {\n            const countToRemove = this.count + 1 - limit;\n            if (countToRemove > 0) {\n                this.removeQuantity(countToRemove);\n            }\n        }\n        return this._pushParticle(position, overrideOptions, group, initializer);\n    }\n    clear() {\n        this._array = [];\n        this._zArray = [];\n    }\n    destroy() {\n        this._array = [];\n        this._zArray = [];\n        this.movers = [];\n        this.updaters = [];\n    }\n    async draw(delta) {\n        const container = this._container;\n        container.canvas.clear();\n        await this.update(delta);\n        for (const [, plugin] of container.plugins) {\n            container.canvas.drawPlugin(plugin, delta);\n        }\n        for (const p of this._zArray) {\n            p.draw(delta);\n        }\n    }\n    filter(condition) {\n        return this._array.filter(condition);\n    }\n    find(condition) {\n        return this._array.find(condition);\n    }\n    handleClickMode(mode) {\n        this._interactionManager.handleClickMode(mode);\n    }\n    init() {\n        const container = this._container, options = container.actualOptions;\n        this.lastZIndex = 0;\n        this.needsSort = false;\n        let handled = false;\n        this.updaters = this._engine.plugins.getUpdaters(container, true);\n        this._interactionManager.init();\n        for (const [, plugin] of container.plugins) {\n            if (plugin.particlesInitialization !== undefined) {\n                handled = plugin.particlesInitialization();\n            }\n            if (handled) {\n                break;\n            }\n        }\n        this._interactionManager.init();\n        for (const [, pathGenerator] of container.pathGenerators) {\n            pathGenerator.init(container);\n        }\n        this.addManualParticles();\n        if (!handled) {\n            for (const group in options.particles.groups) {\n                const groupOptions = options.particles.groups[group];\n                for (let i = this.count, j = 0; j < groupOptions.number?.value && i < options.particles.number.value; i++, j++) {\n                    this.addParticle(undefined, groupOptions, group);\n                }\n            }\n            for (let i = this.count; i < options.particles.number.value; i++) {\n                this.addParticle();\n            }\n        }\n    }\n    push(nb, mouse, overrideOptions, group) {\n        this.pushing = true;\n        for (let i = 0; i < nb; i++) {\n            this.addParticle(mouse?.position, overrideOptions, group);\n        }\n        this.pushing = false;\n    }\n    async redraw() {\n        this.clear();\n        this.init();\n        await this.draw({ value: 0, factor: 0 });\n    }\n    remove(particle, group, override) {\n        this.removeAt(this._array.indexOf(particle), undefined, group, override);\n    }\n    removeAt(index, quantity = 1, group, override) {\n        if (index < 0 || index > this.count) {\n            return;\n        }\n        let deleted = 0;\n        for (let i = index; deleted < quantity && i < this.count; i++) {\n            this._removeParticle(i--, group, override) && deleted++;\n        }\n    }\n    removeQuantity(quantity, group) {\n        this.removeAt(0, quantity, group);\n    }\n    setDensity() {\n        const options = this._container.actualOptions, groups = options.particles.groups;\n        for (const group in groups) {\n            this._applyDensity(groups[group], 0, group);\n        }\n        this._applyDensity(options.particles, options.manualParticles.length);\n    }\n    async update(delta) {\n        const container = this._container, particlesToDelete = new Set();\n        this.quadTree = new QuadTree(qTreeRectangle(container.canvas.size), qTreeCapacity);\n        for (const [, pathGenerator] of container.pathGenerators) {\n            pathGenerator.update();\n        }\n        for (const [, plugin] of container.plugins) {\n            plugin.update && plugin.update(delta);\n        }\n        for (const particle of this._array) {\n            const resizeFactor = container.canvas.resizeFactor;\n            if (resizeFactor && !particle.ignoresResizeRatio) {\n                particle.position.x *= resizeFactor.width;\n                particle.position.y *= resizeFactor.height;\n                particle.initialPosition.x *= resizeFactor.width;\n                particle.initialPosition.y *= resizeFactor.height;\n            }\n            particle.ignoresResizeRatio = false;\n            await this._interactionManager.reset(particle);\n            for (const [, plugin] of this._container.plugins) {\n                if (particle.destroyed) {\n                    break;\n                }\n                if (plugin.particleUpdate) {\n                    plugin.particleUpdate(particle, delta);\n                }\n            }\n            for (const mover of this.movers) {\n                mover.isEnabled(particle) && mover.move(particle, delta);\n            }\n            if (particle.destroyed) {\n                particlesToDelete.add(particle);\n                continue;\n            }\n            this.quadTree.insert(new Point(particle.getPosition(), particle));\n        }\n        if (particlesToDelete.size) {\n            const checkDelete = (p) => !particlesToDelete.has(p);\n            this._array = this.filter(checkDelete);\n            this._zArray = this._zArray.filter(checkDelete);\n            this.pool.push(...particlesToDelete);\n        }\n        await this._interactionManager.externalInteract(delta);\n        for (const particle of this._array) {\n            for (const updater of this.updaters) {\n                updater.update(particle, delta);\n            }\n            if (!particle.destroyed && !particle.spawning) {\n                await this._interactionManager.particlesInteract(particle, delta);\n            }\n        }\n        delete container.canvas.resizeFactor;\n        if (this.needsSort) {\n            const zArray = this._zArray;\n            zArray.sort((a, b) => b.position.z - a.position.z || a.id - b.id);\n            this.lastZIndex = zArray[zArray.length - 1].position.z;\n            this.needsSort = false;\n        }\n    }\n}\n", "import { getRangeValue } from \"../Utils/NumberUtils\";\nimport { isSsr } from \"../Utils/Utils\";\nexport class Retina {\n    constructor(container) {\n        this.container = container;\n        this.pixelRatio = 1;\n        this.reduceFactor = 1;\n    }\n    init() {\n        const container = this.container, options = container.actualOptions;\n        this.pixelRatio = !options.detectRetina || isSsr() ? 1 : window.devicePixelRatio;\n        this.reduceFactor = 1;\n        const ratio = this.pixelRatio;\n        if (container.canvas.element) {\n            const element = container.canvas.element;\n            container.canvas.size.width = element.offsetWidth * ratio;\n            container.canvas.size.height = element.offsetHeight * ratio;\n        }\n        const particles = options.particles, moveOptions = particles.move;\n        this.attractDistance = getRangeValue(moveOptions.attract.distance) * ratio;\n        this.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n        this.sizeAnimationSpeed = getRangeValue(particles.size.animation.speed) * ratio;\n    }\n    initParticle(particle) {\n        const options = particle.options, ratio = this.pixelRatio, moveOptions = options.move, moveDistance = moveOptions.distance, props = particle.retina;\n        props.attractDistance = getRangeValue(moveOptions.attract.distance) * ratio;\n        props.moveDrift = getRangeValue(moveOptions.drift) * ratio;\n        props.moveSpeed = getRangeValue(moveOptions.speed) * ratio;\n        props.sizeAnimationSpeed = getRangeValue(options.size.animation.speed) * ratio;\n        const maxDistance = props.maxDistance;\n        maxDistance.horizontal = moveDistance.horizontal !== undefined ? moveDistance.horizontal * ratio : undefined;\n        maxDistance.vertical = moveDistance.vertical !== undefined ? moveDistance.vertical * ratio : undefined;\n        props.maxSpeed = getRangeValue(moveOptions.gravity.maxSpeed) * ratio;\n    }\n}\n", "import { getLogger, isFunction } from \"../Utils/Utils\";\nimport { Canvas } from \"./Canvas\";\nimport { EventListeners } from \"./Utils/EventListeners\";\nimport { Options } from \"../Options/Classes/Options\";\nimport { Particles } from \"./Particles\";\nimport { Retina } from \"./Retina\";\nimport { errorPrefix } from \"./Utils/Constants\";\nimport { getRangeValue } from \"../Utils/NumberUtils\";\nimport { loadOptions } from \"../Utils/OptionsUtils\";\nfunction guardCheck(container) {\n    return container && !container.destroyed;\n}\nfunction initDelta(value, fpsLimit = 60, smooth = false) {\n    return {\n        value,\n        factor: smooth ? 60 / fpsLimit : (60 * value) / 1000,\n    };\n}\nfunction loadContainerOptions(engine, container, ...sourceOptionsArr) {\n    const options = new Options(engine, container);\n    loadOptions(options, ...sourceOptionsArr);\n    return options;\n}\nconst defaultPathGeneratorKey = \"default\", defaultPathGenerator = {\n    generate: (p) => p.velocity,\n    init: () => {\n    },\n    update: () => {\n    },\n    reset: () => {\n    },\n};\nexport class Container {\n    constructor(engine, id, sourceOptions) {\n        this.id = id;\n        this._intersectionManager = (entries) => {\n            if (!guardCheck(this) || !this.actualOptions.pauseOnOutsideViewport) {\n                return;\n            }\n            for (const entry of entries) {\n                if (entry.target !== this.interactivity.element) {\n                    continue;\n                }\n                (entry.isIntersecting ? this.play : this.pause)();\n            }\n        };\n        this._nextFrame = async (timestamp) => {\n            try {\n                if (!this.smooth &&\n                    this.lastFrameTime !== undefined &&\n                    timestamp < this.lastFrameTime + 1000 / this.fpsLimit) {\n                    this.draw(false);\n                    return;\n                }\n                this.lastFrameTime ??= timestamp;\n                const delta = initDelta(timestamp - this.lastFrameTime, this.fpsLimit, this.smooth);\n                this.addLifeTime(delta.value);\n                this.lastFrameTime = timestamp;\n                if (delta.value > 1000) {\n                    this.draw(false);\n                    return;\n                }\n                await this.particles.draw(delta);\n                if (!this.alive()) {\n                    this.destroy();\n                    return;\n                }\n                if (this.getAnimationStatus()) {\n                    this.draw(false);\n                }\n            }\n            catch (e) {\n                getLogger().error(`${errorPrefix} in animation loop`, e);\n            }\n        };\n        this._engine = engine;\n        this.fpsLimit = 120;\n        this.smooth = false;\n        this._delay = 0;\n        this._duration = 0;\n        this._lifeTime = 0;\n        this._firstStart = true;\n        this.started = false;\n        this.destroyed = false;\n        this._paused = true;\n        this.lastFrameTime = 0;\n        this.zLayers = 100;\n        this.pageHidden = false;\n        this._sourceOptions = sourceOptions;\n        this._initialSourceOptions = sourceOptions;\n        this.retina = new Retina(this);\n        this.canvas = new Canvas(this);\n        this.particles = new Particles(this._engine, this);\n        this.pathGenerators = new Map();\n        this.interactivity = {\n            mouse: {\n                clicking: false,\n                inside: false,\n            },\n        };\n        this.plugins = new Map();\n        this.drawers = new Map();\n        this._options = loadContainerOptions(this._engine, this);\n        this.actualOptions = loadContainerOptions(this._engine, this);\n        this._eventListeners = new EventListeners(this);\n        if (typeof IntersectionObserver !== \"undefined\" && IntersectionObserver) {\n            this._intersectionObserver = new IntersectionObserver((entries) => this._intersectionManager(entries));\n        }\n        this._engine.dispatchEvent(\"containerBuilt\", { container: this });\n    }\n    get options() {\n        return this._options;\n    }\n    get sourceOptions() {\n        return this._sourceOptions;\n    }\n    addClickHandler(callback) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const el = this.interactivity.element;\n        if (!el) {\n            return;\n        }\n        const clickOrTouchHandler = (e, pos, radius) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            const pxRatio = this.retina.pixelRatio, posRetina = {\n                x: pos.x * pxRatio,\n                y: pos.y * pxRatio,\n            }, particles = this.particles.quadTree.queryCircle(posRetina, radius * pxRatio);\n            callback(e, particles);\n        };\n        const clickHandler = (e) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            const mouseEvent = e, pos = {\n                x: mouseEvent.offsetX || mouseEvent.clientX,\n                y: mouseEvent.offsetY || mouseEvent.clientY,\n            };\n            clickOrTouchHandler(e, pos, 1);\n        };\n        const touchStartHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touched = true;\n            touchMoved = false;\n        };\n        const touchMoveHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touchMoved = true;\n        };\n        const touchEndHandler = (e) => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            if (touched && !touchMoved) {\n                const touchEvent = e;\n                let lastTouch = touchEvent.touches[touchEvent.touches.length - 1];\n                if (!lastTouch) {\n                    lastTouch = touchEvent.changedTouches[touchEvent.changedTouches.length - 1];\n                    if (!lastTouch) {\n                        return;\n                    }\n                }\n                const element = this.canvas.element, canvasRect = element ? element.getBoundingClientRect() : undefined, pos = {\n                    x: lastTouch.clientX - (canvasRect ? canvasRect.left : 0),\n                    y: lastTouch.clientY - (canvasRect ? canvasRect.top : 0),\n                };\n                clickOrTouchHandler(e, pos, Math.max(lastTouch.radiusX, lastTouch.radiusY));\n            }\n            touched = false;\n            touchMoved = false;\n        };\n        const touchCancelHandler = () => {\n            if (!guardCheck(this)) {\n                return;\n            }\n            touched = false;\n            touchMoved = false;\n        };\n        let touched = false, touchMoved = false;\n        el.addEventListener(\"click\", clickHandler);\n        el.addEventListener(\"touchstart\", touchStartHandler);\n        el.addEventListener(\"touchmove\", touchMoveHandler);\n        el.addEventListener(\"touchend\", touchEndHandler);\n        el.addEventListener(\"touchcancel\", touchCancelHandler);\n    }\n    addLifeTime(value) {\n        this._lifeTime += value;\n    }\n    addPath(key, generator, override = false) {\n        if (!guardCheck(this) || (!override && this.pathGenerators.has(key))) {\n            return false;\n        }\n        this.pathGenerators.set(key, generator ?? defaultPathGenerator);\n        return true;\n    }\n    alive() {\n        return !this._duration || this._lifeTime <= this._duration;\n    }\n    destroy() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.stop();\n        this.particles.destroy();\n        this.canvas.destroy();\n        for (const [, drawer] of this.drawers) {\n            drawer.destroy && drawer.destroy(this);\n        }\n        for (const key of this.drawers.keys()) {\n            this.drawers.delete(key);\n        }\n        this._engine.plugins.destroy(this);\n        this.destroyed = true;\n        const mainArr = this._engine.dom(), idx = mainArr.findIndex((t) => t === this);\n        if (idx >= 0) {\n            mainArr.splice(idx, 1);\n        }\n        this._engine.dispatchEvent(\"containerDestroyed\", { container: this });\n    }\n    draw(force) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        let refreshTime = force;\n        this._drawAnimationFrame = requestAnimationFrame(async (timestamp) => {\n            if (refreshTime) {\n                this.lastFrameTime = undefined;\n                refreshTime = false;\n            }\n            await this._nextFrame(timestamp);\n        });\n    }\n    async export(type, options = {}) {\n        for (const [, plugin] of this.plugins) {\n            if (!plugin.export) {\n                continue;\n            }\n            const res = await plugin.export(type, options);\n            if (!res.supported) {\n                continue;\n            }\n            return res.blob;\n        }\n        getLogger().error(`${errorPrefix} - Export plugin with type ${type} not found`);\n    }\n    getAnimationStatus() {\n        return !this._paused && !this.pageHidden && guardCheck(this);\n    }\n    handleClickMode(mode) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.particles.handleClickMode(mode);\n        for (const [, plugin] of this.plugins) {\n            plugin.handleClickMode && plugin.handleClickMode(mode);\n        }\n    }\n    async init() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const shapes = this._engine.plugins.getSupportedShapes();\n        for (const type of shapes) {\n            const drawer = this._engine.plugins.getShapeDrawer(type);\n            if (drawer) {\n                this.drawers.set(type, drawer);\n            }\n        }\n        this._options = loadContainerOptions(this._engine, this, this._initialSourceOptions, this.sourceOptions);\n        this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n        const availablePlugins = this._engine.plugins.getAvailablePlugins(this);\n        for (const [id, plugin] of availablePlugins) {\n            this.plugins.set(id, plugin);\n        }\n        this.retina.init();\n        await this.canvas.init();\n        this.updateActualOptions();\n        this.canvas.initBackground();\n        this.canvas.resize();\n        this.zLayers = this.actualOptions.zLayers;\n        this._duration = getRangeValue(this.actualOptions.duration) * 1000;\n        this._delay = getRangeValue(this.actualOptions.delay) * 1000;\n        this._lifeTime = 0;\n        this.fpsLimit = this.actualOptions.fpsLimit > 0 ? this.actualOptions.fpsLimit : 120;\n        this.smooth = this.actualOptions.smooth;\n        for (const [, drawer] of this.drawers) {\n            drawer.init && (await drawer.init(this));\n        }\n        for (const [, plugin] of this.plugins) {\n            plugin.init && (await plugin.init());\n        }\n        this._engine.dispatchEvent(\"containerInit\", { container: this });\n        this.particles.init();\n        this.particles.setDensity();\n        for (const [, plugin] of this.plugins) {\n            plugin.particlesSetup && plugin.particlesSetup();\n        }\n        this._engine.dispatchEvent(\"particlesSetup\", { container: this });\n    }\n    async loadTheme(name) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this._currentTheme = name;\n        await this.refresh();\n    }\n    pause() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        if (this._drawAnimationFrame !== undefined) {\n            cancelAnimationFrame(this._drawAnimationFrame);\n            delete this._drawAnimationFrame;\n        }\n        if (this._paused) {\n            return;\n        }\n        for (const [, plugin] of this.plugins) {\n            plugin.pause && plugin.pause();\n        }\n        if (!this.pageHidden) {\n            this._paused = true;\n        }\n        this._engine.dispatchEvent(\"containerPaused\", { container: this });\n    }\n    play(force) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        const needsUpdate = this._paused || force;\n        if (this._firstStart && !this.actualOptions.autoPlay) {\n            this._firstStart = false;\n            return;\n        }\n        if (this._paused) {\n            this._paused = false;\n        }\n        if (needsUpdate) {\n            for (const [, plugin] of this.plugins) {\n                if (plugin.play) {\n                    plugin.play();\n                }\n            }\n        }\n        this._engine.dispatchEvent(\"containerPlay\", { container: this });\n        this.draw(needsUpdate || false);\n    }\n    async refresh() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.stop();\n        return this.start();\n    }\n    async reset() {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this._initialSourceOptions = undefined;\n        this._options = loadContainerOptions(this._engine, this);\n        this.actualOptions = loadContainerOptions(this._engine, this, this._options);\n        return this.refresh();\n    }\n    setNoise(noiseOrGenerator, init, update) {\n        if (!guardCheck(this)) {\n            return;\n        }\n        this.setPath(noiseOrGenerator, init, update);\n    }\n    setPath(pathOrGenerator, init, update) {\n        if (!pathOrGenerator || !guardCheck(this)) {\n            return;\n        }\n        const pathGenerator = { ...defaultPathGenerator };\n        if (isFunction(pathOrGenerator)) {\n            pathGenerator.generate = pathOrGenerator;\n            if (init) {\n                pathGenerator.init = init;\n            }\n            if (update) {\n                pathGenerator.update = update;\n            }\n        }\n        else {\n            const oldGenerator = pathGenerator;\n            pathGenerator.generate = pathOrGenerator.generate || oldGenerator.generate;\n            pathGenerator.init = pathOrGenerator.init || oldGenerator.init;\n            pathGenerator.update = pathOrGenerator.update || oldGenerator.update;\n        }\n        this.addPath(defaultPathGeneratorKey, pathGenerator, true);\n    }\n    async start() {\n        if (!guardCheck(this) || this.started) {\n            return;\n        }\n        await this.init();\n        this.started = true;\n        await new Promise((resolve) => {\n            this._delayTimeout = setTimeout(async () => {\n                this._eventListeners.addListeners();\n                if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n                    this._intersectionObserver.observe(this.interactivity.element);\n                }\n                for (const [, plugin] of this.plugins) {\n                    plugin.start && (await plugin.start());\n                }\n                this._engine.dispatchEvent(\"containerStarted\", { container: this });\n                this.play();\n                resolve();\n            }, this._delay);\n        });\n    }\n    stop() {\n        if (!guardCheck(this) || !this.started) {\n            return;\n        }\n        if (this._delayTimeout) {\n            clearTimeout(this._delayTimeout);\n            delete this._delayTimeout;\n        }\n        this._firstStart = true;\n        this.started = false;\n        this._eventListeners.removeListeners();\n        this.pause();\n        this.particles.clear();\n        this.canvas.stop();\n        if (this.interactivity.element instanceof HTMLElement && this._intersectionObserver) {\n            this._intersectionObserver.unobserve(this.interactivity.element);\n        }\n        for (const [, plugin] of this.plugins) {\n            plugin.stop && plugin.stop();\n        }\n        for (const key of this.plugins.keys()) {\n            this.plugins.delete(key);\n        }\n        this._sourceOptions = this._options;\n        this._engine.dispatchEvent(\"containerStopped\", { container: this });\n    }\n    updateActualOptions() {\n        this.actualOptions.responsive = [];\n        const newMaxWidth = this.actualOptions.setResponsive(this.canvas.size.width, this.retina.pixelRatio, this._options);\n        this.actualOptions.setTheme(this._currentTheme);\n        if (this.responsiveMaxWidth === newMaxWidth) {\n            return false;\n        }\n        this.responsiveMaxWidth = newMaxWidth;\n        return true;\n    }\n}\n", "export class EventDispatcher {\n    constructor() {\n        this._listeners = new Map();\n    }\n    addEventListener(type, listener) {\n        this.removeEventListener(type, listener);\n        let arr = this._listeners.get(type);\n        if (!arr) {\n            arr = [];\n            this._listeners.set(type, arr);\n        }\n        arr.push(listener);\n    }\n    dispatchEvent(type, args) {\n        const listeners = this._listeners.get(type);\n        listeners && listeners.forEach((handler) => handler(args));\n    }\n    hasEventListener(type) {\n        return !!this._listeners.get(type);\n    }\n    removeAllEventListeners(type) {\n        if (!type) {\n            this._listeners = new Map();\n        }\n        else {\n            this._listeners.delete(type);\n        }\n    }\n    removeEventListener(type, listener) {\n        const arr = this._listeners.get(type);\n        if (!arr) {\n            return;\n        }\n        const length = arr.length, idx = arr.indexOf(listener);\n        if (idx < 0) {\n            return;\n        }\n        if (length === 1) {\n            this._listeners.delete(type);\n        }\n        else {\n            arr.splice(idx, 1);\n        }\n    }\n}\n", "import { executeOnSingleOrMultiple } from \"../../Utils/Utils\";\nfunction getItemsFromInitializer(container, map, initializers, force = false) {\n    let res = map.get(container);\n    if (!res || force) {\n        res = [...initializers.values()].map((t) => t(container));\n        map.set(container, res);\n    }\n    return res;\n}\nexport class Plugins {\n    constructor(engine) {\n        this._engine = engine;\n        this.plugins = [];\n        this._initializers = {\n            interactors: new Map(),\n            movers: new Map(),\n            updaters: new Map(),\n        };\n        this.interactors = new Map();\n        this.movers = new Map();\n        this.updaters = new Map();\n        this.presets = new Map();\n        this.drawers = new Map();\n        this.pathGenerators = new Map();\n    }\n    addInteractor(name, initInteractor) {\n        this._initializers.interactors.set(name, initInteractor);\n    }\n    addParticleMover(name, initMover) {\n        this._initializers.movers.set(name, initMover);\n    }\n    addParticleUpdater(name, initUpdater) {\n        this._initializers.updaters.set(name, initUpdater);\n    }\n    addPathGenerator(type, pathGenerator) {\n        !this.getPathGenerator(type) && this.pathGenerators.set(type, pathGenerator);\n    }\n    addPlugin(plugin) {\n        !this.getPlugin(plugin.id) && this.plugins.push(plugin);\n    }\n    addPreset(presetKey, options, override = false) {\n        (override || !this.getPreset(presetKey)) && this.presets.set(presetKey, options);\n    }\n    addShapeDrawer(types, drawer) {\n        executeOnSingleOrMultiple(types, (type) => {\n            !this.getShapeDrawer(type) && this.drawers.set(type, drawer);\n        });\n    }\n    destroy(container) {\n        this.updaters.delete(container);\n        this.movers.delete(container);\n        this.interactors.delete(container);\n    }\n    getAvailablePlugins(container) {\n        const res = new Map();\n        for (const plugin of this.plugins) {\n            plugin.needsPlugin(container.actualOptions) && res.set(plugin.id, plugin.getPlugin(container));\n        }\n        return res;\n    }\n    getInteractors(container, force = false) {\n        return getItemsFromInitializer(container, this.interactors, this._initializers.interactors, force);\n    }\n    getMovers(container, force = false) {\n        return getItemsFromInitializer(container, this.movers, this._initializers.movers, force);\n    }\n    getPathGenerator(type) {\n        return this.pathGenerators.get(type);\n    }\n    getPlugin(plugin) {\n        return this.plugins.find((t) => t.id === plugin);\n    }\n    getPreset(preset) {\n        return this.presets.get(preset);\n    }\n    getShapeDrawer(type) {\n        return this.drawers.get(type);\n    }\n    getSupportedShapes() {\n        return this.drawers.keys();\n    }\n    getUpdaters(container, force = false) {\n        return getItemsFromInitializer(container, this.updaters, this._initializers.updaters, force);\n    }\n    loadOptions(options, sourceOptions) {\n        for (const plugin of this.plugins) {\n            plugin.loadOptions(options, sourceOptions);\n        }\n    }\n    loadParticlesOptions(container, options, ...sourceOptions) {\n        const updaters = this.updaters.get(container);\n        if (!updaters) {\n            return;\n        }\n        for (const updater of updaters) {\n            updater.loadOptions && updater.loadOptions(options, ...sourceOptions);\n        }\n    }\n}\n", "import { errorPrefix, generatedAttribute } from \"./Utils/Constants\";\nimport { getLogger, isBoolean, isFunction, isNumber, isString, itemFromSingleOrMultiple } from \"../Utils/Utils\";\nimport { Container } from \"./Container\";\nimport { EventDispatcher } from \"../Utils/EventDispatcher\";\nimport { Plugins } from \"./Utils/Plugins\";\nimport { getRandom } from \"../Utils/NumberUtils\";\nasync function getDataFromUrl(data) {\n    const url = itemFromSingleOrMultiple(data.url, data.index);\n    if (!url) {\n        return data.fallback;\n    }\n    const response = await fetch(url);\n    if (response.ok) {\n        return response.json();\n    }\n    getLogger().error(`${errorPrefix} ${response.status} while retrieving config file`);\n    return data.fallback;\n}\nfunction isParamsEmpty(params) {\n    return !params.id && !params.element && !params.url && !params.options;\n}\nfunction isParams(obj) {\n    return !isParamsEmpty(obj);\n}\nexport class Engine {\n    constructor() {\n        this._configs = new Map();\n        this._domArray = [];\n        this._eventDispatcher = new EventDispatcher();\n        this._initialized = false;\n        this.plugins = new Plugins(this);\n    }\n    get configs() {\n        const res = {};\n        for (const [name, config] of this._configs) {\n            res[name] = config;\n        }\n        return res;\n    }\n    get version() {\n        return \"2.12.0\";\n    }\n    addConfig(nameOrConfig, config) {\n        if (isString(nameOrConfig)) {\n            if (config) {\n                config.name = nameOrConfig;\n                this._configs.set(nameOrConfig, config);\n            }\n        }\n        else {\n            this._configs.set(nameOrConfig.name ?? \"default\", nameOrConfig);\n        }\n    }\n    addEventListener(type, listener) {\n        this._eventDispatcher.addEventListener(type, listener);\n    }\n    async addInteractor(name, interactorInitializer, refresh = true) {\n        this.plugins.addInteractor(name, interactorInitializer);\n        await this.refresh(refresh);\n    }\n    async addMover(name, moverInitializer, refresh = true) {\n        this.plugins.addParticleMover(name, moverInitializer);\n        await this.refresh(refresh);\n    }\n    async addParticleUpdater(name, updaterInitializer, refresh = true) {\n        this.plugins.addParticleUpdater(name, updaterInitializer);\n        await this.refresh(refresh);\n    }\n    async addPathGenerator(name, generator, refresh = true) {\n        this.plugins.addPathGenerator(name, generator);\n        await this.refresh(refresh);\n    }\n    async addPlugin(plugin, refresh = true) {\n        this.plugins.addPlugin(plugin);\n        await this.refresh(refresh);\n    }\n    async addPreset(preset, options, override = false, refresh = true) {\n        this.plugins.addPreset(preset, options, override);\n        await this.refresh(refresh);\n    }\n    async addShape(shape, drawer, initOrRefresh, afterEffectOrRefresh, destroyOrRefresh, refresh = true) {\n        let customDrawer;\n        let realRefresh = refresh, realInit, realAfterEffect, realDestroy;\n        if (isBoolean(initOrRefresh)) {\n            realRefresh = initOrRefresh;\n            realInit = undefined;\n        }\n        else {\n            realInit = initOrRefresh;\n        }\n        if (isBoolean(afterEffectOrRefresh)) {\n            realRefresh = afterEffectOrRefresh;\n            realAfterEffect = undefined;\n        }\n        else {\n            realAfterEffect = afterEffectOrRefresh;\n        }\n        if (isBoolean(destroyOrRefresh)) {\n            realRefresh = destroyOrRefresh;\n            realDestroy = undefined;\n        }\n        else {\n            realDestroy = destroyOrRefresh;\n        }\n        if (isFunction(drawer)) {\n            customDrawer = {\n                afterEffect: realAfterEffect,\n                destroy: realDestroy,\n                draw: drawer,\n                init: realInit,\n            };\n        }\n        else {\n            customDrawer = drawer;\n        }\n        this.plugins.addShapeDrawer(shape, customDrawer);\n        await this.refresh(realRefresh);\n    }\n    dispatchEvent(type, args) {\n        this._eventDispatcher.dispatchEvent(type, args);\n    }\n    dom() {\n        return this._domArray;\n    }\n    domItem(index) {\n        const dom = this.dom(), item = dom[index];\n        if (!item || item.destroyed) {\n            dom.splice(index, 1);\n            return;\n        }\n        return item;\n    }\n    init() {\n        if (this._initialized) {\n            return;\n        }\n        this._initialized = true;\n    }\n    async load(tagIdOrOptionsOrParams, options) {\n        return this.loadFromArray(tagIdOrOptionsOrParams, options);\n    }\n    async loadFromArray(tagIdOrOptionsOrParams, optionsOrIndex, index) {\n        let params;\n        if (!isParams(tagIdOrOptionsOrParams)) {\n            params = {};\n            if (isString(tagIdOrOptionsOrParams)) {\n                params.id = tagIdOrOptionsOrParams;\n            }\n            else {\n                params.options = tagIdOrOptionsOrParams;\n            }\n            if (isNumber(optionsOrIndex)) {\n                params.index = optionsOrIndex;\n            }\n            else {\n                params.options = optionsOrIndex ?? params.options;\n            }\n            params.index = index ?? params.index;\n        }\n        else {\n            params = tagIdOrOptionsOrParams;\n        }\n        return this._loadParams(params);\n    }\n    async loadJSON(tagId, pathConfigJson, index) {\n        let url, id;\n        if (isNumber(pathConfigJson) || pathConfigJson === undefined) {\n            url = tagId;\n        }\n        else {\n            id = tagId;\n            url = pathConfigJson;\n        }\n        return this._loadParams({ id: id, url, index });\n    }\n    async refresh(refresh = true) {\n        if (!refresh) {\n            return;\n        }\n        this.dom().forEach((t) => t.refresh());\n    }\n    removeEventListener(type, listener) {\n        this._eventDispatcher.removeEventListener(type, listener);\n    }\n    async set(id, element, options, index) {\n        const params = { index };\n        if (isString(id)) {\n            params.id = id;\n        }\n        else {\n            params.element = id;\n        }\n        if (element instanceof HTMLElement) {\n            params.element = element;\n        }\n        else {\n            params.options = element;\n        }\n        if (isNumber(options)) {\n            params.index = options;\n        }\n        else {\n            params.options = options ?? params.options;\n        }\n        return this._loadParams(params);\n    }\n    async setJSON(id, element, pathConfigJson, index) {\n        const params = {};\n        if (id instanceof HTMLElement) {\n            params.element = id;\n            params.url = element;\n            params.index = pathConfigJson;\n        }\n        else {\n            params.id = id;\n            params.element = element;\n            params.url = pathConfigJson;\n            params.index = index;\n        }\n        return this._loadParams(params);\n    }\n    setOnClickHandler(callback) {\n        const dom = this.dom();\n        if (!dom.length) {\n            throw new Error(`${errorPrefix} can only set click handlers after calling tsParticles.load()`);\n        }\n        for (const domItem of dom) {\n            domItem.addClickHandler(callback);\n        }\n    }\n    async _loadParams(params) {\n        const id = params.id ?? `tsparticles${Math.floor(getRandom() * 10000)}`, { index, url } = params, options = url ? await getDataFromUrl({ fallback: params.options, url, index }) : params.options;\n        let domContainer = params.element ?? document.getElementById(id);\n        if (!domContainer) {\n            domContainer = document.createElement(\"div\");\n            domContainer.id = id;\n            document.body.append(domContainer);\n        }\n        const currentOptions = itemFromSingleOrMultiple(options, index), dom = this.dom(), oldIndex = dom.findIndex((v) => v.id === id);\n        if (oldIndex >= 0) {\n            const old = this.domItem(oldIndex);\n            if (old && !old.destroyed) {\n                old.destroy();\n                dom.splice(oldIndex, 1);\n            }\n        }\n        let canvasEl;\n        if (domContainer.tagName.toLowerCase() === \"canvas\") {\n            canvasEl = domContainer;\n            canvasEl.dataset[generatedAttribute] = \"false\";\n        }\n        else {\n            const existingCanvases = domContainer.getElementsByTagName(\"canvas\");\n            if (existingCanvases.length) {\n                canvasEl = existingCanvases[0];\n                canvasEl.dataset[generatedAttribute] = \"false\";\n            }\n            else {\n                canvasEl = document.createElement(\"canvas\");\n                canvasEl.dataset[generatedAttribute] = \"true\";\n                domContainer.appendChild(canvasEl);\n            }\n        }\n        if (!canvasEl.style.width) {\n            canvasEl.style.width = \"100%\";\n        }\n        if (!canvasEl.style.height) {\n            canvasEl.style.height = \"100%\";\n        }\n        const newItem = new Container(this, id, currentOptions);\n        if (oldIndex >= 0) {\n            dom.splice(oldIndex, 0, newItem);\n        }\n        else {\n            dom.push(newItem);\n        }\n        newItem.canvas.loadCanvas(canvasEl);\n        await newItem.start();\n        return newItem;\n    }\n}\n", "import { getRangeValue, parseAlpha } from \"./NumberUtils\";\nimport { hslToRgb, hslaToRgba } from \"./ColorUtils\";\nexport class HslColorManager {\n    constructor() {\n        this.key = \"hsl\";\n        this.stringPrefix = \"hsl\";\n    }\n    handleColor(color) {\n        const colorValue = color.value, hslColor = colorValue.hsl ?? color.value;\n        if (hslColor.h !== undefined && hslColor.s !== undefined && hslColor.l !== undefined) {\n            return hslToRgb(hslColor);\n        }\n    }\n    handleRangeColor(color) {\n        const colorValue = color.value, hslColor = colorValue.hsl ?? color.value;\n        if (hslColor.h !== undefined && hslColor.l !== undefined) {\n            return hslToRgb({\n                h: getRangeValue(hslColor.h),\n                l: getRangeValue(hslColor.l),\n                s: getRangeValue(hslColor.s),\n            });\n        }\n    }\n    parseString(input) {\n        if (!input.startsWith(\"hsl\")) {\n            return;\n        }\n        const regex = /hsla?\\(\\s*(\\d+)\\s*,\\s*(\\d+)%\\s*,\\s*(\\d+)%\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i, result = regex.exec(input);\n        return result\n            ? hslaToRgba({\n                a: result.length > 4 ? parseAlpha(result[5]) : 1,\n                h: parseInt(result[1], 10),\n                l: parseInt(result[3], 10),\n                s: parseInt(result[2], 10),\n            })\n            : undefined;\n    }\n}\n", "import { getRangeValue, parseAlpha } from \"./NumberUtils\";\nexport class RgbColorManager {\n    constructor() {\n        this.key = \"rgb\";\n        this.stringPrefix = \"rgb\";\n    }\n    handleColor(color) {\n        const colorValue = color.value, rgbColor = colorValue.rgb ?? color.value;\n        if (rgbColor.r !== undefined) {\n            return rgbColor;\n        }\n    }\n    handleRangeColor(color) {\n        const colorValue = color.value, rgbColor = colorValue.rgb ?? color.value;\n        if (rgbColor.r !== undefined) {\n            return {\n                r: getRangeValue(rgbColor.r),\n                g: getRangeValue(rgbColor.g),\n                b: getRangeValue(rgbColor.b),\n            };\n        }\n    }\n    parseString(input) {\n        if (!input.startsWith(this.stringPrefix)) {\n            return;\n        }\n        const regex = /rgba?\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*(,\\s*([\\d.%]+)\\s*)?\\)/i, result = regex.exec(input);\n        return result\n            ? {\n                a: result.length > 4 ? parseAlpha(result[5]) : 1,\n                b: parseInt(result[3], 10),\n                g: parseInt(result[2], 10),\n                r: parseInt(result[1], 10),\n            }\n            : undefined;\n    }\n}\n", "import { Engine } from \"./Core/Engine\";\nimport { HslColorManager } from \"./Utils/HslColorManager\";\nimport { RgbColorManager } from \"./Utils/RgbColorManager\";\nimport { addColorManager } from \"./Utils/ColorUtils\";\nexport function init() {\n    const rgbColorManager = new RgbColorManager(), hslColorManager = new HslColorManager();\n    addColorManager(rgbColorManager);\n    addColorManager(hslColorManager);\n    const engine = new Engine();\n    engine.init();\n    return engine;\n}\n", "import { init } from \"./init\";\nimport { isSsr } from \"./Utils/Utils\";\nconst tsParticles = init();\nif (!isSsr()) {\n    window.tsParticles = tsParticles;\n}\nexport * from \"./exports\";\nexport * from \"./export-types\";\nexport { tsParticles };\n", "const isObject = (val) => typeof val === \"object\" && val !== null;\nexport function deepCompare(obj1, obj2, excludeKeyFn = () => false) {\n    if (!isObject(obj1) || !isObject(obj2)) {\n        return obj1 === obj2;\n    }\n    const keys1 = Object.keys(obj1).filter(key => !excludeKeyFn(key)), keys2 = Object.keys(obj2).filter(key => !excludeKeyFn(key));\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const value1 = obj1[key], value2 = obj2[key];\n        if (isObject(value1) && isObject(value2)) {\n            if (value1 === obj2 && value2 === obj1) {\n                continue;\n            }\n            if (!deepCompare(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (Array.isArray(value1) && Array.isArray(value2)) {\n            if (!deepCompareArrays(value1, value2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (value1 !== value2) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction deepCompareArrays(arr1, arr2, excludeKeyFn) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (let i = 0; i < arr1.length; i++) {\n        const val1 = arr1[i], val2 = arr2[i];\n        if (Array.isArray(val1) && Array.isArray(val2)) {\n            if (!deepCompareArrays(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (isObject(val1) && isObject(val2)) {\n            if (!deepCompare(val1, val2, excludeKeyFn)) {\n                return false;\n            }\n        }\n        else if (val1 !== val2) {\n            return false;\n        }\n    }\n    return true;\n}\n", "import Particles from \"./Particles\";\nexport default Particles;\nexport { Particles };\n"], "mappings": ";;;;;;;;AAAA,mBAAiC;;;ACA1B,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,cAAc;AACpB,IAAM,wBAAwB;AAC9B,IAAM,cAAc;;;ACVpB,IAAM,WAAN,MAAM,UAAS;AAAA,EAClB,YAAY,WAAW,GAAG,GAAG;AACzB,SAAK,mBAAmB,CAAC,OAAO,WAAW;AACvC,WAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAC3B,WAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AAAA,IAC/B;AACA,QAAI,CAAC,SAAS,SAAS,KAAK,WAAW;AACnC,WAAK,IAAI,UAAU;AACnB,WAAK,IAAI,UAAU;AACnB,YAAM,WAAW;AACjB,WAAK,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,IACvC,WACS,cAAc,UAAa,MAAM,QAAW;AACjD,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI,KAAK;AAAA,IAClB,OACK;AACD,YAAM,IAAI,MAAM,GAAG,WAAW,qCAAqC;AAAA,IACvE;AAAA,EACJ;AAAA,EACA,WAAW,SAAS;AAChB,WAAO,UAAS,OAAO,GAAG,GAAG,CAAC;AAAA,EAClC;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC;AAAA,EACpC;AAAA,EACA,IAAI,MAAM,OAAO;AACb,SAAK,iBAAiB,OAAO,KAAK,MAAM;AAAA,EAC5C;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK,KAAK,KAAK,YAAY,CAAC;AAAA,EACvC;AAAA,EACA,IAAI,OAAO,QAAQ;AACf,SAAK,iBAAiB,KAAK,OAAO,MAAM;AAAA,EAC5C;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,WAAO,UAAS,OAAO,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,EACvD;AAAA,EACA,OAAO,OAAO,GAAG,GAAG,GAAG;AACnB,WAAO,IAAI,UAAS,GAAG,GAAG,CAAC;AAAA,EAC/B;AAAA,EACA,IAAI,GAAG;AACH,WAAO,UAAS,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAAA,EACnE;AAAA,EACA,MAAM,GAAG;AACL,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AAAA,EAChB;AAAA,EACA,OAAO;AACH,WAAO,UAAS,MAAM,IAAI;AAAA,EAC9B;AAAA,EACA,WAAW,GAAG;AACV,WAAO,KAAK,IAAI,CAAC,EAAE;AAAA,EACvB;AAAA,EACA,aAAa,GAAG;AACZ,WAAO,KAAK,IAAI,CAAC,EAAE,YAAY;AAAA,EACnC;AAAA,EACA,IAAI,GAAG;AACH,WAAO,UAAS,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAC7D;AAAA,EACA,MAAM,GAAG;AACL,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACV,WAAO,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EACnC;AAAA,EACA,KAAK,GAAG;AACJ,WAAO,UAAS,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAC7D;AAAA,EACA,OAAO,GAAG;AACN,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACd;AAAA,EACA,YAAY;AACR,UAAM,SAAS,KAAK;AACpB,QAAI,UAAU,GAAG;AACb,WAAK,OAAO,IAAM,MAAM;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,OAAO,OAAO;AACV,WAAO,UAAS,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,EACtI;AAAA,EACA,MAAM,GAAG;AACL,SAAK,IAAI,EAAE;AACX,SAAK,IAAI,EAAE;AACX,UAAM,MAAM;AACZ,SAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7B;AAAA,EACA,IAAI,GAAG;AACH,WAAO,UAAS,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AAAA,EACnE;AAAA,EACA,QAAQ,GAAG;AACP,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AAAA,EAChB;AACJ;;;ACtGO,IAAM,SAAN,MAAM,gBAAe,SAAS;AAAA,EACjC,YAAY,WAAW,GAAG;AACtB,UAAM,WAAW,GAAG,CAAC;AAAA,EACzB;AAAA,EACA,WAAW,SAAS;AAChB,WAAO,QAAO,OAAO,GAAG,CAAC;AAAA,EAC7B;AAAA,EACA,OAAO,MAAM,QAAQ;AACjB,WAAO,QAAO,OAAO,OAAO,GAAG,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,OAAO,OAAO,GAAG,GAAG;AAChB,WAAO,IAAI,QAAO,GAAG,CAAC;AAAA,EAC1B;AACJ;;;ACZA,IAAI,UAAU,KAAK;AAcZ,SAAS,YAAY;AACxB,SAAO,MAAM,QAAQ,GAAG,GAAG,IAAI,KAAK;AACxC;AACO,SAAS,MAAM,KAAK,KAAK,KAAK;AACjC,SAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AAC3C;AAIO,SAAS,cAAc,GAAG;AAC7B,QAAM,MAAM,YAAY,CAAC;AACzB,MAAI,MAAM,YAAY,CAAC;AACvB,MAAI,QAAQ,KAAK;AACb,UAAM;AAAA,EACV;AACA,SAAO,UAAU,KAAK,MAAM,OAAO;AACvC;AACO,SAAS,cAAc,OAAO;AACjC,SAAO,SAAS,KAAK,IAAI,QAAQ,cAAc,KAAK;AACxD;AACO,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,KAAK,IAAI,QAAQ,MAAM;AAC3C;AACO,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,KAAK,IAAI,QAAQ,MAAM;AAC3C;AACO,SAAS,cAAc,QAAQ,OAAO;AACzC,MAAI,WAAW,SAAU,UAAU,UAAa,SAAS,MAAM,GAAI;AAC/D,WAAO;AAAA,EACX;AACA,QAAM,MAAM,YAAY,MAAM,GAAG,MAAM,YAAY,MAAM;AACzD,SAAO,UAAU,SACX;AAAA,IACE,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IACxB,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC5B,IACE,cAAc,KAAK,GAAG;AAChC;AACO,SAAS,SAAS,SAAS;AAC9B,QAAM,SAAS,QAAQ,QAAQ,EAAE,QAAQ,aAAa,IAAI,UAAU,MAAM,IACpE;AAAA,IACE,QAAQ;AAAA,IACR,cAAc;AAAA,EAClB,IACE;AACN,SAAO,SAAS,cAAc,cAAc,QAAQ,OAAO,YAAY,CAAC,IAAI,cAAc,QAAQ,KAAK;AAC3G;AACO,SAAS,aAAa,QAAQ,QAAQ;AACzC,QAAM,KAAK,OAAO,IAAI,OAAO,GAAG,KAAK,OAAO,IAAI,OAAO;AACvD,SAAO,EAAE,IAAQ,IAAQ,UAAU,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,EAAE;AACpE;AACO,SAAS,YAAY,QAAQ,QAAQ;AACxC,SAAO,aAAa,QAAQ,MAAM,EAAE;AACxC;AACO,SAAS,0BAA0B,WAAW,UAAU,QAAQ;AACnE,MAAI,SAAS,SAAS,GAAG;AACrB,WAAQ,YAAY,KAAK,KAAM;AAAA,EACnC;AACA,UAAQ,WAAW;AAAA,IACf,KAAK;AACD,aAAO,CAAC,KAAK,KAAK;AAAA,IACtB,KAAK;AACD,aAAO,CAAC,KAAK,KAAK;AAAA,IACtB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,KAAK,KAAK;AAAA,IACrB,KAAK;AACD,aAAO,KAAK,KAAK;AAAA,IACrB,KAAK;AACD,aAAQ,IAAI,KAAK,KAAM;AAAA,IAC3B,KAAK;AACD,aAAO,KAAK;AAAA,IAChB,KAAK;AACD,aAAQ,KAAK,KAAK,KAAM;AAAA,IAC5B,KAAK;AACD,aAAO,KAAK,MAAM,OAAO,IAAI,SAAS,GAAG,OAAO,IAAI,SAAS,CAAC;AAAA,IAClE,KAAK;AACD,aAAO,KAAK,MAAM,SAAS,IAAI,OAAO,GAAG,SAAS,IAAI,OAAO,CAAC;AAAA,IAClE;AACI,aAAO,UAAU,IAAI,KAAK,KAAK;AAAA,EACvC;AACJ;AACO,SAAS,wBAAwB,WAAW;AAC/C,QAAM,eAAe,OAAO;AAC5B,eAAa,SAAS;AACtB,eAAa,QAAQ;AACrB,SAAO;AACX;AAyBO,SAAS,kCAAkC,MAAM;AAjIxD;AAkII,SAAO;AAAA,IACH,KAAG,UAAK,aAAL,mBAAe,MAAK,UAAU,IAAI,KAAK,KAAK;AAAA,IAC/C,KAAG,UAAK,aAAL,mBAAe,MAAK,UAAU,IAAI,KAAK,KAAK;AAAA,EACnD;AACJ;AAQO,SAAS,WAAW,OAAO;AAC9B,SAAO,QAAS,MAAM,SAAS,GAAG,IAAI,WAAW,KAAK,IAAI,MAAM,WAAW,KAAK,IAAK;AACzF;;;AC9IA,IAAM,UAAU;AAAA,EACZ,OAAO,QAAQ;AAAA,EACf,OAAO,QAAQ;AAAA,EACf,MAAM,QAAQ;AAAA,EACd,KAAK,QAAQ;AAAA,EACb,SAAS,QAAQ;AAAA,EACjB,SAAS,QAAQ;AACrB;AASO,SAAS,YAAY;AACxB,SAAO;AACX;AAsBO,SAAS,QAAQ;AACpB,SAAO,OAAO,WAAW,eAAe,CAAC,UAAU,OAAO,OAAO,aAAa,eAAe,CAAC,OAAO;AACzG;AACO,SAAS,gBAAgB;AAC5B,SAAO,CAAC,MAAM,KAAK,OAAO,eAAe;AAC7C;AACO,SAAS,eAAe,OAAO;AAClC,MAAI,CAAC,cAAc,GAAG;AAClB;AAAA,EACJ;AACA,SAAO,WAAW,KAAK;AAC3B;AACO,SAAS,qBAAqB,UAAU;AAC3C,MAAI,MAAM,KAAK,OAAO,qBAAqB,aAAa;AACpD;AAAA,EACJ;AACA,SAAO,IAAI,iBAAiB,QAAQ;AACxC;AACO,SAAS,UAAU,OAAO,OAAO;AACpC,SAAO,UAAU,SAAU,QAAQ,KAAK,KAAK,MAAM,QAAQ,KAAK,IAAI;AACxE;AAQO,SAAS,iBAAiB,OAAO;AACpC,SAAO,KAAK,MAAM,UAAU,IAAI,MAAM,MAAM;AAChD;AACO,SAAS,cAAc,OAAO,OAAO,WAAW,MAAM;AACzD,SAAO,MAAM,UAAU,UAAa,WAAW,QAAQ,MAAM,SAAS,iBAAiB,KAAK,CAAC;AACjG;AA4BO,SAAS,WAAW,gBAAgB,SAAS;AAChD,aAAW,UAAU,SAAS;AAC1B,QAAI,WAAW,UAAa,WAAW,MAAM;AACzC;AAAA,IACJ;AACA,QAAI,CAAC,SAAS,MAAM,GAAG;AACnB,oBAAc;AACd;AAAA,IACJ;AACA,UAAM,gBAAgB,MAAM,QAAQ,MAAM;AAC1C,QAAI,kBAAkB,SAAS,WAAW,KAAK,CAAC,eAAe,CAAC,MAAM,QAAQ,WAAW,IAAI;AACzF,oBAAc,CAAC;AAAA,IACnB,WACS,CAAC,kBAAkB,SAAS,WAAW,KAAK,CAAC,eAAe,MAAM,QAAQ,WAAW,IAAI;AAC9F,oBAAc,CAAC;AAAA,IACnB;AACA,eAAW,OAAO,QAAQ;AACtB,UAAI,QAAQ,aAAa;AACrB;AAAA,MACJ;AACA,YAAM,aAAa,QAAQ,QAAQ,WAAW,GAAG,GAAG,WAAW;AAC/D,eAAS,GAAG,IACR,SAAS,KAAK,KAAK,MAAM,QAAQ,KAAK,IAChC,MAAM,IAAI,CAAC,MAAM,WAAW,SAAS,GAAG,GAAG,CAAC,CAAC,IAC7C,WAAW,SAAS,GAAG,GAAG,KAAK;AAAA,IAC7C;AAAA,EACJ;AACA,SAAO;AACX;AAwGO,SAAS,0BAA0B,KAAK,UAAU;AACrD,SAAO,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,UAAU,SAAS,MAAM,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC;AAC3F;AACO,SAAS,yBAAyB,KAAK,OAAO,UAAU;AAC3D,SAAO,QAAQ,GAAG,IAAI,cAAc,KAAK,OAAO,QAAQ,IAAI;AAChE;AAIO,SAAS,kCAAkC,SAAS,SAAS;AAChE,QAAM,aAAa,QAAQ,OAAO,mBAAmB,QAAQ,WAAW,MAAM;AAAA,IAC1E,WAAW,cAAc,iBAAiB,KAAK,IAAI;AAAA,IACnD,QAAQ,iBAAiB;AAAA,IACzB,OAAO,cAAc,QAAQ,KAAK,IAAI;AAAA,IACtC,KAAK,YAAY,UAAU,IAAI;AAAA,IAC/B,KAAK,YAAY,UAAU,IAAI;AAAA,IAC/B,OAAO;AAAA,IACP,UAAU,cAAc,iBAAiB,KAAK;AAAA,IAC9C,MAAM;AAAA,EACV;AACA,MAAI,iBAAiB,QAAQ;AACzB,QAAI,QAAQ,IAAI,cAAc,iBAAiB,KAAK;AACpD,YAAQ,iBAAiB,MAAM;AAAA,MAC3B,KAAK;AACD,YAAI,SAAS;AACb;AAAA,MACJ,KAAK;AACD,YAAI,SAAS;AACb;AAAA,MACJ,KAAK;AACD,YAAI,SAAS,UAAU,KAAK,MAAM,eAAe;AACjD;AAAA,IACR;AACA,UAAM,aAAa,iBAAiB,SAAS;AAC7C,YAAQ,iBAAiB,YAAY;AAAA,MACjC,KAAK;AACD,YAAI,QAAQ,IAAI;AAChB,YAAI,YAAY;AACZ,cAAI,SAAS;AAAA,QACjB;AACA;AAAA,MACJ,KAAK;AACD,YAAI,QAAQ,IAAI;AAChB,YAAI,YAAY;AACZ,cAAI,SAAS;AAAA,QACjB;AACA;AAAA,MACJ,KAAK;AAAA,MACL;AACI,YAAI,QAAQ,cAAc,GAAG;AAC7B,YAAI,YAAY;AACZ,cAAI,SAAS,UAAU,KAAK,MAAM,eAAe;AAAA,QACrD;AACA;AAAA,IACR;AAAA,EACJ;AACA,MAAI,eAAe,IAAI;AACvB,SAAO;AACX;AACA,SAAS,kBAAkB,gBAAgB,YAAY;AACnD,QAAM,YAAY,eAAe,SAAS;AAC1C,MAAI,CAAC,WAAW;AACZ,UAAM,EAAE,MAAM,GAAG,GAAG,KAAK,IAAI;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,aAAa,OAAO;AAC1B,MAAI,YAAY;AACZ,WAAO;AAAA,MACH,GAAI,eAAe,IAAI,MAAO,WAAW;AAAA,MACzC,GAAI,eAAe,IAAI,MAAO,WAAW;AAAA,IAC7C;AAAA,EACJ,OACK;AACD,WAAO;AAAA,MACH,OAAQ,eAAe,QAAQ,MAAO,WAAW;AAAA,MACjD,QAAS,eAAe,SAAS,MAAO,WAAW;AAAA,IACvD;AAAA,EACJ;AACJ;AACO,SAAS,YAAY,UAAU,YAAY;AAC9C,SAAO,kBAAkB,UAAU,UAAU;AACjD;AAIO,SAAS,UAAU,KAAK;AAC3B,SAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,KAAK;AAC1B,SAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,KAAK;AAC1B,SAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,WAAW,KAAK;AAC5B,SAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,KAAK;AAC1B,SAAO,OAAO,QAAQ,YAAY,QAAQ;AAC9C;AACO,SAAS,QAAQ,KAAK;AACzB,SAAO,MAAM,QAAQ,GAAG;AAC5B;;;AC/UA,IAAM,mBAAmB;AAAzB,IAA0D,gBAAgB,oBAAI,IAAI;AAC3E,SAAS,gBAAgB,SAAS;AACrC,gBAAc,IAAI,QAAQ,KAAK,OAAO;AAC1C;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,GAAG;AACP,SAAK;AAAA,EACT;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC7B;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO;AAAA,EACX;AACA,MAAI,IAAI,IAAI,GAAG;AACX,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AACA,SAAS,aAAa,OAAO;AACzB,aAAW,CAAC,EAAE,OAAO,KAAK,eAAe;AACrC,QAAI,MAAM,WAAW,QAAQ,YAAY,GAAG;AACxC,aAAO,QAAQ,YAAY,KAAK;AAAA,IACpC;AAAA,EACJ;AACA,QAAM,iBAAiB,8CAA8C,WAAW,MAAM,QAAQ,gBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM;AAC7H,WAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,MAAM,SAAY,IAAI,IAAI;AAAA,EAC9D,CAAC,GAAG,QAAQ,0DAA0D,SAAS,MAAM,KAAK,QAAQ;AAClG,SAAO,SACD;AAAA,IACE,GAAG,OAAO,CAAC,MAAM,SAAY,SAAS,OAAO,CAAC,GAAG,EAAE,IAAI,MAAO;AAAA,IAC9D,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,IACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,IACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,EAC7B,IACE;AACV;AACO,SAAS,gBAAgB,OAAO,OAAO,WAAW,MAAM;AAC3D,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,QAAM,QAAQ,SAAS,KAAK,IAAI,EAAE,OAAO,MAAM,IAAI;AACnD,MAAI,SAAS,MAAM,KAAK,GAAG;AACvB,WAAO,WAAW,MAAM,OAAO,OAAO,QAAQ;AAAA,EAClD;AACA,MAAI,QAAQ,MAAM,KAAK,GAAG;AACtB,WAAO,gBAAgB;AAAA,MACnB,OAAO,cAAc,MAAM,OAAO,OAAO,QAAQ;AAAA,IACrD,CAAC;AAAA,EACL;AACA,aAAW,CAAC,EAAE,OAAO,KAAK,eAAe;AACrC,UAAM,MAAM,QAAQ,iBAAiB,KAAK;AAC1C,QAAI,KAAK;AACL,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACO,SAAS,WAAW,OAAO,OAAO,WAAW,MAAM;AACtD,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,QAAM,QAAQ,SAAS,KAAK,IAAI,EAAE,OAAO,MAAM,IAAI;AACnD,MAAI,SAAS,MAAM,KAAK,GAAG;AACvB,WAAO,MAAM,UAAU,mBAAmB,kBAAkB,IAAI,YAAY,MAAM,KAAK;AAAA,EAC3F;AACA,MAAI,QAAQ,MAAM,KAAK,GAAG;AACtB,WAAO,WAAW;AAAA,MACd,OAAO,cAAc,MAAM,OAAO,OAAO,QAAQ;AAAA,IACrD,CAAC;AAAA,EACL;AACA,aAAW,CAAC,EAAE,OAAO,KAAK,eAAe;AACrC,UAAM,MAAM,QAAQ,YAAY,KAAK;AACrC,QAAI,KAAK;AACL,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAKO,SAAS,gBAAgB,OAAO,OAAO,WAAW,MAAM;AAC3D,QAAM,MAAM,gBAAgB,OAAO,OAAO,QAAQ;AAClD,SAAO,MAAM,SAAS,GAAG,IAAI;AACjC;AACO,SAAS,SAAS,OAAO;AAC5B,QAAM,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM,KAAK,IAAI,IAAI,IAAI,EAAE,GAAG,MAAM;AAAA,IAC5H,GAAG;AAAA,IACH,IAAI,MAAM,OAAO;AAAA,IACjB,GAAG;AAAA,EACP;AACA,MAAI,QAAQ,KAAK;AACb,QAAI,IAAI,IAAI,IAAI,OAAO,MAAM,QAAQ,MAAM,QAAQ,MAAM,QAAQ,IAAM,MAAM;AAC7E,QAAI,IACA,OAAO,OACA,KAAK,OAAO,MAAM,OAClB,IAAI,IAAI,OAAO,MAAM,KAAO,KAAK,OAAO,MAAM,OAAO,KAAO,KAAK,OAAO,MAAM;AAAA,EAC7F;AACA,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,IAAI,IAAI,GAAG;AACX,QAAI,KAAK;AAAA,EACb;AACA,MAAI,IAAI,KAAK,KAAK;AACd,QAAI,KAAK;AAAA,EACb;AACA,SAAO;AACX;AAIO,SAAS,YAAY,OAAO;AAC/B,SAAO,aAAa,KAAK;AAC7B;AACO,SAAS,SAAS,KAAK;AAC1B,QAAM,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,aAAa;AAAA,IAC9C,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI,IAAI;AAAA,EACf;AACA,MAAI,CAAC,WAAW,GAAG;AACf,WAAO,IAAI,OAAO,IAAI,OAAO,IAAI,WAAW;AAAA,EAChD,OACK;AACD,UAAM,IAAI,WAAW,IAAI,MACnB,WAAW,KAAK,IAAI,WAAW,KAC/B,WAAW,IAAI,WAAW,IAAI,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,WAAW,IAAI;AACxF,WAAO,IAAI,QAAQ,GAAG,GAAG,WAAW,IAAI,IAAI,CAAC;AAC7C,WAAO,IAAI,QAAQ,GAAG,GAAG,WAAW,CAAC;AACrC,WAAO,IAAI,QAAQ,GAAG,GAAG,WAAW,IAAI,IAAI,CAAC;AAAA,EACjD;AACA,SAAO,IAAI,KAAK,MAAM,OAAO,IAAI,GAAG;AACpC,SAAO,IAAI,KAAK,MAAM,OAAO,IAAI,GAAG;AACpC,SAAO,IAAI,KAAK,MAAM,OAAO,IAAI,GAAG;AACpC,SAAO;AACX;AACO,SAAS,WAAW,MAAM;AAC7B,QAAM,YAAY,SAAS,IAAI;AAC/B,SAAO;AAAA,IACH,GAAG,KAAK;AAAA,IACR,GAAG,UAAU;AAAA,IACb,GAAG,UAAU;AAAA,IACb,GAAG,UAAU;AAAA,EACjB;AACJ;AACO,SAAS,kBAAkB,KAAK;AACnC,QAAM,WAAW,OAAO;AACxB,SAAO;AAAA,IACH,GAAG,KAAK,MAAM,cAAc,cAAc,UAAU,GAAG,CAAC,CAAC;AAAA,IACzD,GAAG,KAAK,MAAM,cAAc,cAAc,UAAU,GAAG,CAAC,CAAC;AAAA,IACzD,GAAG,KAAK,MAAM,cAAc,cAAc,UAAU,GAAG,CAAC,CAAC;AAAA,EAC7D;AACJ;AACO,SAAS,gBAAgB,OAAO,SAAS;AAC5C,SAAO,QAAQ,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,WAAW,CAAC;AACnE;AACO,SAAS,gBAAgB,OAAO,SAAS;AAC5C,SAAO,QAAQ,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC,MAAM,WAAW,CAAC;AACrE;AAyDO,SAAS,oBAAoB,WAAW;AAC3C,SAAO,cAAc,SACf;AAAA,IACE,GAAG,UAAU,EAAE;AAAA,IACf,GAAG,UAAU,EAAE;AAAA,IACf,GAAG,UAAU,EAAE;AAAA,EACnB,IACE;AACV;;;ACvNO,SAAS,UAAU,SAAS,WAAW,WAAW;AACrD,UAAQ,YAAY,aAAa;AACjC,UAAQ,SAAS,GAAG,GAAG,UAAU,OAAO,UAAU,MAAM;AAC5D;AACO,SAAS,WAAW,SAAS,WAAW,OAAO,SAAS;AAC3D,MAAI,CAAC,OAAO;AACR;AAAA,EACJ;AACA,UAAQ,cAAc;AACtB,UAAQ,UAAU,OAAO,GAAG,GAAG,UAAU,OAAO,UAAU,MAAM;AAChE,UAAQ,cAAc;AAC1B;AACO,SAAS,MAAM,SAAS,WAAW;AACtC,UAAQ,UAAU,GAAG,GAAG,UAAU,OAAO,UAAU,MAAM;AAC7D;AACO,SAAS,aAAa,MAAM;AAC/B,QAAM,EAAE,WAAW,SAAS,UAAU,OAAO,aAAa,gBAAgB,WAAW,QAAQ,SAAS,QAAQ,UAAW,IAAI;AAC7H,QAAM,MAAM,SAAS,YAAY,GAAG,QAAQ,SAAS,YAAY,SAAS,eAAe,SAAS,SAAS,QAAQ,IAAI,aAAa;AAAA,IAChI,KAAK,KAAK,IAAI,KAAK;AAAA,IACnB,KAAK,KAAK,IAAI,KAAK;AAAA,EACvB,GAAG,gBAAgB;AAAA,IACf,GAAG,WAAW,OAAO,UAAU,KAAK;AAAA,IACpC,GAAG,WAAW,OAAO,UAAU,KAAK;AAAA,IACpC,GAAG,CAAC,WAAW,OAAO,UAAU,KAAK;AAAA,IACrC,GAAG,WAAW,OAAO,UAAU,KAAK;AAAA,EACxC;AACA,UAAQ,aAAa,cAAc,GAAG,cAAc,GAAG,cAAc,GAAG,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;AACrG,UAAQ,UAAU;AAClB,MAAI,gBAAgB;AAChB,YAAQ,2BAA2B;AAAA,EACvC;AACA,QAAM,cAAc,SAAS;AAC7B,MAAI,OAAO,UAAU,aAAa;AAC9B,YAAQ,aAAa,OAAO;AAC5B,YAAQ,cAAc,gBAAgB,WAAW;AACjD,YAAQ,gBAAgB,OAAO,OAAO;AACtC,YAAQ,gBAAgB,OAAO,OAAO;AAAA,EAC1C;AACA,MAAI,YAAY,MAAM;AAClB,YAAQ,YAAY,YAAY;AAAA,EACpC;AACA,QAAM,cAAc,SAAS,eAAe;AAC5C,UAAQ,YAAY;AACpB,MAAI,YAAY,QAAQ;AACpB,YAAQ,cAAc,YAAY;AAAA,EACtC;AACA,YAAU,WAAW,SAAS,UAAU,QAAQ,SAAS,KAAK;AAC9D,MAAI,cAAc,GAAG;AACjB,YAAQ,OAAO;AAAA,EACnB;AACA,MAAI,SAAS,OAAO;AAChB,YAAQ,UAAU;AAAA,EACtB;AACA,MAAI,SAAS,MAAM;AACf,YAAQ,KAAK;AAAA,EACjB;AACA,uBAAqB,WAAW,SAAS,UAAU,QAAQ,SAAS,KAAK;AACzE,UAAQ,2BAA2B;AACnC,UAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACzC;AACO,SAAS,UAAU,WAAW,SAAS,UAAU,QAAQ,SAAS,OAAO;AAC5E,MAAI,CAAC,SAAS,OAAO;AACjB;AAAA,EACJ;AACA,QAAM,SAAS,UAAU,QAAQ,IAAI,SAAS,KAAK;AACnD,MAAI,CAAC,QAAQ;AACT;AAAA,EACJ;AACA,SAAO,KAAK,SAAS,UAAU,QAAQ,SAAS,OAAO,UAAU,OAAO,UAAU;AACtF;AACO,SAAS,qBAAqB,WAAW,SAAS,UAAU,QAAQ,SAAS,OAAO;AACvF,MAAI,CAAC,SAAS,OAAO;AACjB;AAAA,EACJ;AACA,QAAM,SAAS,UAAU,QAAQ,IAAI,SAAS,KAAK;AACnD,MAAI,CAAC,UAAU,CAAC,OAAO,aAAa;AAChC;AAAA,EACJ;AACA,SAAO,YAAY,SAAS,UAAU,QAAQ,SAAS,OAAO,UAAU,OAAO,UAAU;AAC7F;AACO,SAAS,WAAW,SAAS,QAAQ,OAAO;AAC/C,MAAI,CAAC,OAAO,MAAM;AACd;AAAA,EACJ;AACA,SAAO,KAAK,SAAS,KAAK;AAC9B;AACO,SAAS,mBAAmB,SAAS,QAAQ,UAAU,OAAO;AACjE,MAAI,CAAC,OAAO,cAAc;AACtB;AAAA,EACJ;AACA,SAAO,aAAa,SAAS,UAAU,KAAK;AAChD;AACO,SAAS,SAAS,OAAO,MAAM,OAAO;AACzC,SAAO;AAAA,IACH,GAAG,MAAM;AAAA,IACT,GAAG,MAAM;AAAA,IACT,GAAG,MAAM,KAAK,SAAS,WAAW,KAAK,KAAK;AAAA,EAChD;AACJ;;;AC5GA,SAAS,kBAAkB,QAAQ,WAAW,KAAK;AAC/C,QAAM,WAAW,UAAU,GAAG;AAC9B,MAAI,aAAa,QAAW;AACxB,WAAO,GAAG,KAAK,OAAO,GAAG,KAAK,KAAK;AAAA,EACvC;AACJ;AACO,IAAM,SAAN,MAAa;AAAA,EAChB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,yBAAyB,CAAC,aAAa;AACxC,iBAAW,WAAW,KAAK,mBAAmB;AAC1C,gBAAQ,aAAa,QAAQ,UAAU,QAAQ;AAAA,MACnD;AAAA,IACJ;AACA,SAAK,wBAAwB,CAAC,KAAK,UAAU,QAAQ,UAAU,aAAa,cAAc;AACtF,iBAAW,WAAW,KAAK,kBAAkB;AACzC,YAAI,QAAQ,gBAAgB;AACxB,gBAAM,EAAE,MAAM,OAAO,IAAI,QAAQ,eAAe,UAAU,KAAK,QAAQ,QAAQ;AAC/E,cAAI,MAAM;AACN,wBAAY,OAAO;AAAA,UACvB;AACA,cAAI,QAAQ;AACR,wBAAY,SAAS;AAAA,UACzB;AAAA,QACJ;AACA,YAAI,QAAQ,oBAAoB;AAC5B,gBAAM,mBAAmB,QAAQ,mBAAmB,QAAQ;AAC5D,qBAAW,OAAO,kBAAkB;AAChC,8BAAkB,WAAW,kBAAkB,GAAG;AAAA,UACtD;AAAA,QACJ;AACA,gBAAQ,cAAc,QAAQ,WAAW,QAAQ;AAAA,MACrD;AAAA,IACJ;AACA,SAAK,sBAAsB,MAAM;AAC7B,iBAAW,UAAU,KAAK,gBAAgB;AACtC,eAAO,UAAU,OAAO,OAAO;AAAA,MACnC;AAAA,IACJ;AACA,SAAK,2BAA2B,CAAC,aAAa;AAC1C,UAAI,QAAQ;AACZ,iBAAW,UAAU,KAAK,eAAe;AACrC,YAAI,CAAC,UAAU,OAAO,mBAAmB;AACrC,mBAAS,gBAAgB,OAAO,kBAAkB,QAAQ,CAAC;AAAA,QAC/D;AACA,YAAI,CAAC,UAAU,OAAO,qBAAqB;AACvC,mBAAS,gBAAgB,OAAO,oBAAoB,QAAQ,CAAC;AAAA,QACjE;AACA,YAAI,UAAU,QAAQ;AAClB;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,CAAC,QAAQ,MAAM;AAAA,IAC1B;AACA,SAAK,aAAa,MAAM;AACpB,YAAM,UAAU,KAAK,UAAU,eAAe,QAAQ,QAAQ,eAAe,OAAO,QAAQ,MAAM,OAAO,WAAW,gBAAgB,KAAK;AACzI,UAAI,UAAU;AACV,cAAM,aAAa;AAAA,UACf,GAAG;AAAA,UACH,GAAG,MAAM;AAAA,QACb;AACA,aAAK,mBAAmB,gBAAgB,YAAY,WAAW,CAAC;AAAA,MACpE;AAAA,IACJ;AACA,SAAK,aAAa,MAAM;AACpB,YAAM,UAAU,KAAK,SAAS,UAAU,KAAK,UAAU;AACvD,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,UAAI,KAAK,aAAa;AAClB,aAAK,iBAAiB,WAAW,CAAC,GAAG,QAAQ,KAAK;AAClD,aAAK,oBAAoB;AAAA,MAC7B,OACK;AACD,aAAK,oBAAoB;AAAA,MAC7B;AACA,iBAAW,OAAO,QAAQ,OAAO;AAC7B,YAAI,CAAC,OAAO,CAAC,QAAQ,OAAO;AACxB;AAAA,QACJ;AACA,cAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,YAAI,CAAC,OAAO;AACR;AAAA,QACJ;AACA,gBAAQ,MAAM,YAAY,KAAK,OAAO,WAAW;AAAA,MACrD;AAAA,IACJ;AACA,SAAK,aAAa,YAAY;AAC1B,YAAM,UAAU,KAAK,UAAU,eAAe,QAAQ,QAAQ,UAAU,KAAK,OAAO,YAAY,MAAM;AACtG,UAAI,CAAC,MAAM,QAAQ;AACf;AAAA,MACJ;AACA,UAAI,UAAU,OAAO;AACjB,cAAM,YAAY,gBAAgB,UAAU,KAAK;AACjD,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,cAAMA,SAAQ,QAAQ,UAAU,KAAK;AACrC,aAAK,aAAa;AAAA,UACd,OAAO;AAAA,YACH,GAAG;AAAA,UACP;AAAA,UACA,SAAS,IAAIA,OAAM;AAAA,QACvB;AAAA,MACJ,OACK;AACD,cAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACnC,cAAI,CAAC,UAAU,OAAO;AAClB;AAAA,UACJ;AACA,gBAAM,MAAM,SAAS,cAAc,KAAK;AACxC,cAAI,iBAAiB,QAAQ,MAAM;AAC/B,iBAAK,aAAa;AAAA,cACd,OAAO;AAAA,cACP,SAAS,IAAI,MAAM;AAAA,YACvB;AACA,oBAAQ;AAAA,UACZ,CAAC;AACD,cAAI,iBAAiB,SAAS,CAAC,QAAQ;AACnC,mBAAO,IAAI,KAAK;AAAA,UACpB,CAAC;AACD,cAAI,MAAM,UAAU;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,SAAK,aAAa,CAAC,cAAc;AAC7B,WAAK,KAAK,CAAC,QAAQ,UAAU,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,IAC3D;AACA,SAAK,cAAc,CAAC,OAAO,YAAY;AACnC,WAAK,KAAK,CAAC,QAAQ,WAAW,KAAK,KAAK,MAAM,OAAO,OAAO,CAAC;AAAA,IACjE;AACA,SAAK,eAAe,MAAM;AACtB,YAAM,UAAU,KAAK;AACrB,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,WAAK,sBAAsB,CAAC,aAAa,SAAS,WAAW,CAAC;AAC9D,WAAK,WAAW;AAChB,WAAK,eAAe;AACpB,WAAK,sBAAsB,CAAC,aAAa,SAAS,QAAQ,SAAS,EAAE,YAAY,KAAK,CAAC,CAAC;AAAA,IAC5F;AACA,SAAK,sBAAsB,MAAM;AAC7B,YAAM,UAAU,KAAK,SAAS,gBAAgB,KAAK;AACnD,UAAI,EAAE,WAAW,gBAAgB;AAC7B;AAAA,MACJ;AACA,YAAM,QAAQ,QAAQ;AACtB,YAAM,WAAW,cAAc;AAC/B,YAAM,SAAS,cAAc;AAC7B,YAAM,MAAM,cAAc;AAC1B,YAAM,OAAO,cAAc;AAC3B,YAAM,QAAQ,cAAc;AAC5B,YAAM,SAAS,cAAc;AAAA,IACjC;AACA,SAAK,wBAAwB,CAAC,aAAa;AACvC,UAAI,CAAC,KAAK,mBAAmB;AACzB;AAAA,MACJ;AACA,eAAS,KAAK,iBAAiB;AAAA,IACnC;AACA,SAAK,sBAAsB,MAAM;AAC7B,YAAM,UAAU,KAAK;AACrB,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,YAAM,WAAW,aAAa,QAAQ,QAAQ;AAC9C,YAAM,YAAY,YAAY,SAAS,QAAQ;AAC/C,YAAM,YAAY,WAAW,KAAK,UAAU,cAAc,WAAW,OAAO,SAAS,EAAE,GAAG,QAAQ;AAClG,YAAM,YAAY,OAAO,KAAK,QAAQ;AACtC,YAAM,YAAY,QAAQ,KAAK,QAAQ;AACvC,YAAM,YAAY,SAAS,QAAQ,QAAQ;AAC3C,YAAM,YAAY,UAAU,QAAQ,QAAQ;AAAA,IAChD;AACA,SAAK,OAAO;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,IACX;AACA,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,iBAAiB,CAAC;AACvB,SAAK,gBAAgB,CAAC;AAAA,EAC1B;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK,UAAU,cAAc,WAAW;AAAA,EACnD;AAAA,EACA,QAAQ;AACJ,UAAM,UAAU,KAAK,UAAU,eAAe,QAAQ,QAAQ,UAAU,KAAK,OAAO,YAAY,KAAK;AACrG,QAAI,QAAQ,eAAe,QAAQ;AAC/B,WAAK,MAAM;AAAA,IACf,WACS,MAAM,UAAU,MAAM,SAAS,KAAK,WAAW;AACpD,UAAI,UAAU,OAAO;AACjB,aAAK,WAAW,gBAAgB,UAAU,OAAO,UAAU,OAAO,CAAC;AAAA,MACvE,WACS,UAAU,OAAO;AACtB,aAAK,YAAY,UAAU,OAAO,UAAU,OAAO;AAAA,MACvD;AAAA,IACJ,OACK;AACD,WAAK,KAAK,CAAC,QAAQ;AACf,cAAM,KAAK,KAAK,IAAI;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,KAAK;AACV,QAAI,KAAK,YAAY;AACjB,YAAM,UAAU,KAAK;AACrB,iBAAW,QAAQ,OAAO;AAAA,IAC9B,OACK;AACD,WAAK,oBAAoB;AAAA,IAC7B;AACA,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,iBAAiB,CAAC;AACvB,SAAK,gBAAgB,CAAC;AAAA,EAC1B;AAAA,EACA,KAAK,IAAI;AACL,UAAM,MAAM,KAAK;AACjB,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,WAAO,GAAG,GAAG;AAAA,EACjB;AAAA,EACA,aAAa,UAAU,OAAO;AAC1B,QAAI,SAAS,YAAY,SAAS,WAAW;AACzC;AAAA,IACJ;AACA,UAAM,SAAS,SAAS,UAAU;AAClC,QAAI,UAAU,GAAG;AACb;AAAA,IACJ;AACA,UAAM,UAAU,SAAS,aAAa,GAAG,UAAU,SAAS,eAAe,KAAK;AAChF,QAAI,CAAC,QAAQ,MAAM,IAAI,KAAK,yBAAyB,QAAQ;AAC7D,QAAI,CAAC,QAAQ;AACT,eAAS;AAAA,IACb;AACA,QAAI,CAAC,QAAQ;AACT,eAAS;AAAA,IACb;AACA,QAAI,CAAC,UAAU,CAAC,QAAQ;AACpB;AAAA,IACJ;AACA,SAAK,KAAK,CAAC,QAAQ;AA1P3B;AA2PY,YAAM,YAAY,KAAK,WAAW,UAAU,UAAU,eAAe,gBAAgB,SAAS,QAAQ,QAAQ,kBAAkB,IAAI,SAAS,iBAAiB,cAAc,aAAa,UAAU,SAAS,OAAO,aAAW,cAAS,YAAT,mBAAkB,UAAS,GAAG,gBAAgB,SAAS,iBAAiB,SAAS,WAAW,UAAU,gBAAgB,iBAAiB,gBAAgB,gBAAgB,YAAY,CAAC,GAAG,cAAc;AAAA,QAC/Z,MAAM,SAAS,gBAAgB,QAAQ,QAAQ,IAAI;AAAA,MACvD;AACA,kBAAY,SAAS,SAAS,gBAAgB,QAAQ,cAAc,IAAI,YAAY;AACpF,WAAK,sBAAsB,KAAK,UAAU,QAAQ,UAAU,aAAa,SAAS;AAClF,mBAAa;AAAA,QACT;AAAA,QACA,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA,gBAAgB,QAAQ,eAAe;AAAA,QACvC,WAAW,QAAQ,eAAe;AAAA,QAClC,QAAQ,UAAU,IAAI,SAAS,iBAAiB,cAAc;AAAA,QAC9D,SAAS;AAAA,QACT,QAAQ,SAAS,QAAQ;AAAA,QACzB;AAAA,MACJ,CAAC;AACD,WAAK,uBAAuB,QAAQ;AAAA,IACxC,CAAC;AAAA,EACL;AAAA,EACA,mBAAmB,QAAQ,UAAU,OAAO;AACxC,SAAK,KAAK,CAAC,QAAQ,mBAAmB,KAAK,QAAQ,UAAU,KAAK,CAAC;AAAA,EACvE;AAAA,EACA,WAAW,QAAQ,OAAO;AACtB,SAAK,KAAK,CAAC,QAAQ,WAAW,KAAK,QAAQ,KAAK,CAAC;AAAA,EACrD;AAAA,EACA,MAAM,OAAO;AACT,SAAK,sBAAsB,CAAC,QAAQ,IAAI,WAAW,CAAC;AACpD,SAAK,oBAAoB,qBAAqB,CAAC,YAAY;AACvD,iBAAW,UAAU,SAAS;AAC1B,YAAI,OAAO,SAAS,gBAAgB,OAAO,kBAAkB,SAAS;AAClE,eAAK,aAAa;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,QAAI;AACA,YAAM,KAAK,WAAW;AAAA,IAC1B,SACO,GAAG;AACN,gBAAU,EAAE,MAAM,CAAC;AAAA,IACvB;AACA,SAAK,eAAe;AACpB,SAAK,sBAAsB,CAAC,QAAQ;AAChC,UAAI,CAAC,KAAK,SAAS;AACf;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK,SAAS,EAAE,YAAY,KAAK,CAAC;AAAA,IAClD,CAAC;AACD,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,MAAM;AAAA,EACf;AAAA,EACA,iBAAiB;AACb,UAAM,UAAU,KAAK,UAAU,eAAe,aAAa,QAAQ,YAAY,UAAU,KAAK;AAC9F,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,UAAM,eAAe,QAAQ;AAC7B,QAAI,CAAC,cAAc;AACf;AAAA,IACJ;AACA,QAAI,WAAW,OAAO;AAClB,YAAM,QAAQ,gBAAgB,WAAW,KAAK;AAC9C,mBAAa,kBAAkB,QAAQ,gBAAgB,OAAO,WAAW,OAAO,IAAI;AAAA,IACxF,OACK;AACD,mBAAa,kBAAkB;AAAA,IACnC;AACA,iBAAa,kBAAkB,WAAW,SAAS;AACnD,iBAAa,qBAAqB,WAAW,YAAY;AACzD,iBAAa,mBAAmB,WAAW,UAAU;AACrD,iBAAa,iBAAiB,WAAW,QAAQ;AAAA,EACrD;AAAA,EACA,cAAc;AACV,SAAK,iBAAiB,CAAC;AACvB,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,UAAU,SAAS;AAC7C,UAAI,OAAO,QAAQ;AACf,aAAK,eAAe,KAAK,MAAM;AAAA,MACnC;AACA,UAAI,OAAO,qBAAqB,OAAO,qBAAqB;AACxD,aAAK,cAAc,KAAK,MAAM;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,SAAK,mBAAmB,CAAC;AACzB,SAAK,oBAAoB,CAAC;AAC1B,eAAW,WAAW,KAAK,UAAU,UAAU,UAAU;AACrD,UAAI,QAAQ,WAAW;AACnB,aAAK,kBAAkB,KAAK,OAAO;AAAA,MACvC;AACA,UAAI,QAAQ,kBAAkB,QAAQ,sBAAsB,QAAQ,YAAY;AAC5E,aAAK,iBAAiB,KAAK,OAAO;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW,QAAQ;AACf,QAAI,KAAK,cAAc,KAAK,SAAS;AACjC,WAAK,QAAQ,OAAO;AAAA,IACxB;AACA,SAAK,aACD,OAAO,WAAW,sBAAsB,OAAO,UACzC,OAAO,QAAQ,kBAAkB,MAAM,SACvC,KAAK;AACf,SAAK,UAAU;AACf,SAAK,QAAQ,aAAa;AAC1B,SAAK,iBAAiB,WAAW,CAAC,GAAG,KAAK,QAAQ,KAAK;AACvD,SAAK,KAAK,SAAS,OAAO;AAC1B,SAAK,KAAK,QAAQ,OAAO;AACzB,SAAK,WAAW,KAAK,QAAQ,WAAW,IAAI;AAC5C,SAAK,sBAAsB,CAAC,QAAQ;AAChC,UAAI,CAAC,KAAK,SAAS;AACf;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK,SAAS,EAAE,YAAY,KAAK,CAAC;AAAA,IAClD,CAAC;AACD,SAAK,UAAU,OAAO,KAAK;AAC3B,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,QAAQ;AACJ,UAAM,UAAU,KAAK,UAAU;AAC/B,SAAK,KAAK,CAAC,QAAQ;AACf,UAAI,QAAQ,eAAe,UAAU,QAAQ,eAAe,OAAO;AAC/D,cAAM,KAAK,KAAK,IAAI;AACpB,aAAK,WAAW,KAAK,gBAAgB;AAAA,MACzC,OACK;AACD,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,SAAS;AACL,QAAI,CAAC,KAAK,SAAS;AACf,aAAO;AAAA,IACX;AACA,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU,OAAO,YAAY,OAAO,UAAU,OAAO,MAAM,UAAU;AAAA,MAC7G,OAAO,KAAK,QAAQ,cAAc;AAAA,MAClC,QAAQ,KAAK,QAAQ,eAAe;AAAA,IACxC;AACA,QAAI,QAAQ,WAAW,KAAK,UACxB,QAAQ,UAAU,KAAK,SACvB,QAAQ,WAAW,KAAK,QAAQ,UAChC,QAAQ,UAAU,KAAK,QAAQ,OAAO;AACtC,aAAO;AAAA,IACX;AACA,UAAM,UAAU,EAAE,GAAG,KAAK;AAC1B,SAAK,QAAQ,QAAQ,KAAK,QAAQ,KAAK,QAAQ,cAAc;AAC7D,SAAK,QAAQ,SAAS,KAAK,SAAS,KAAK,QAAQ,eAAe;AAChE,QAAI,KAAK,UAAU,SAAS;AACxB,WAAK,eAAe;AAAA,QAChB,OAAO,KAAK,QAAQ,QAAQ;AAAA,QAC5B,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAClC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO;AACH,SAAK,sBAAsB,CAAC,QAAQ,IAAI,WAAW,CAAC;AACpD,SAAK,oBAAoB;AACzB,SAAK,KAAK,CAAC,QAAQ,MAAM,KAAK,KAAK,IAAI,CAAC;AAAA,EAC5C;AAAA,EACA,MAAM,eAAe;AACjB,QAAI,CAAC,KAAK,WAAW,CAAC,KAAK,OAAO,GAAG;AACjC;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,WAAW,eAAe,UAAU,oBAAoB;AAC/E,cAAU,UAAU,WAAW;AAC/B,SAAK,oBAAoB;AACzB,QAAI,cAAc;AACd,YAAM,UAAU,QAAQ;AAAA,IAC5B;AAAA,EACJ;AACJ;;;ACzaA,SAAS,eAAe,SAAS,OAAO,SAAS,KAAK,SAAS;AAC3D,MAAI,KAAK;AACL,QAAI,aAAa,EAAE,SAAS,KAAK;AACjC,QAAI,UAAU,OAAO,GAAG;AACpB,iBAAW,UAAU;AAAA,IACzB,WACS,YAAY,QAAW;AAC5B,mBAAa;AAAA,IACjB;AACA,YAAQ,iBAAiB,OAAO,SAAS,UAAU;AAAA,EACvD,OACK;AACD,UAAM,gBAAgB;AACtB,YAAQ,oBAAoB,OAAO,SAAS,aAAa;AAAA,EAC7D;AACJ;AACO,IAAM,iBAAN,MAAqB;AAAA,EACxB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,qBAAqB,CAAC,MAAM;AAC7B,YAAMC,aAAY,KAAK,WAAW,UAAUA,WAAU;AACtD,UAAI,KAAK,UAAU;AACf,cAAM,qBAAqBA,WAAU,cAAc,OAAO,WAAW,mBAAmB;AACxF,YAAI,CAAC,UAAU;AACX;AAAA,QACJ;AACA,2BAAmB,gBAAgB,EAAE,GAAG,SAAS;AACjD,2BAAmB,aAAY,oBAAI,KAAK,GAAE,QAAQ;AAClD,cAAM,UAAU,QAAQ,cAAc,OAAO;AAC7C,kCAA0B,QAAQ,MAAM,CAAC,SAAS,KAAK,UAAU,gBAAgB,IAAI,CAAC;AAAA,MAC1F;AACA,UAAI,EAAE,SAAS,YAAY;AACvB,mBAAW,MAAM,KAAK,kBAAkB,GAAG,GAAG;AAAA,MAClD;AAAA,IACJ;AACA,SAAK,qBAAqB,CAAC,MAAM;AAC7B,YAAM,aAAa,GAAGA,aAAY,KAAK,WAAW,UAAUA,WAAU,SAAS,gBAAgB,QAAQ,eAAe,YAAY,WAAW,UAAU,cAAc,OAAO,cAAc,OAAO,QAAQ,QAAQ,OAAO,KAAK,CAACC,WAAUA,OAAM,SAAS,SAAS;AAChQ,UAAI,SAAS,MAAM,QAAQ,MAAM;AAC7B,QAAAD,WAAU,UAAU,SAAS;AAAA,MACjC;AAAA,IACJ;AACA,SAAK,0BAA0B,MAAM;AACjC,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU;AACtD,WAAK,kBAAkB;AACvB,UAAI,CAAC,QAAQ,aAAa;AACtB;AAAA,MACJ;AACA,UAAI,YAAY,SAAS,QAAQ;AAC7B,QAAAA,WAAU,aAAa;AACvB,QAAAA,WAAU,MAAM;AAAA,MACpB,OACK;AACD,QAAAA,WAAU,aAAa;AACvB,YAAIA,WAAU,mBAAmB,GAAG;AAChC,UAAAA,WAAU,KAAK,IAAI;AAAA,QACvB,OACK;AACD,UAAAA,WAAU,KAAK,IAAI;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,sBAAsB,YAAY;AACnC,UAAI,KAAK,gBAAgB;AACrB,qBAAa,KAAK,cAAc;AAChC,eAAO,KAAK;AAAA,MAChB;AACA,WAAK,iBAAiB,WAAW,YAAY;AACzC,cAAM,SAAS,KAAK,UAAU;AAC9B,kBAAW,MAAM,OAAO,aAAa;AAAA,MACzC,GAAG,KAAK,UAAU,cAAc,cAAc,OAAO,OAAO,QAAQ,GAAI;AAAA,IAC5E;AACA,SAAK,gCAAgC,CAAC,oBAAoB,QAAQ;AAC9D,YAAM,WAAW,KAAK,WAAWA,aAAY,KAAK,WAAW,UAAUA,WAAU;AACjF,YAAM,kBAAkBA,WAAU,cAAc;AAChD,UAAI,CAAC,iBAAiB;AAClB;AAAA,MACJ;AACA,YAAM,OAAO,iBAAiB,WAAWA,WAAU,OAAO;AAC1D,UAAI,UAAU;AACV,iBAAS,MAAM,gBAAgB,SAAS,WAAW,YAAY;AAAA,MACnE;AACA,UAAI,EAAE,QAAQ,cAAc,OAAO,QAAQ,UAAU,QAAQ,cAAc,OAAO,QAAQ,SAAS;AAC/F;AAAA,MACJ;AACA,qBAAe,iBAAiB,gBAAgB,SAAS,WAAW,GAAG;AACvE,qBAAe,iBAAiB,iBAAiB,SAAS,YAAY,GAAG;AACzE,qBAAe,iBAAiB,gBAAgB,SAAS,WAAW,GAAG;AACvE,UAAI,CAAC,QAAQ,cAAc,OAAO,QAAQ,QAAQ;AAC9C,uBAAe,iBAAiB,eAAe,SAAS,UAAU,GAAG;AAAA,MACzE,OACK;AACD,uBAAe,iBAAiB,eAAe,SAAS,eAAe,GAAG;AAC1E,uBAAe,iBAAiB,cAAc,SAAS,SAAS,GAAG;AACnE,uBAAe,iBAAiB,gBAAgB,SAAS,WAAW,GAAG;AAAA,MAC3E;AACA,qBAAe,iBAAiB,oBAAoB,SAAS,YAAY,GAAG;AAC5E,qBAAe,iBAAiB,kBAAkB,SAAS,aAAa,GAAG;AAAA,IAC/E;AACA,SAAK,mBAAmB,CAAC,QAAQ;AAC7B,YAAM,WAAW,KAAK,WAAWA,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,aAAa,QAAQ,cAAc,WAAW,WAAWA,WAAU,OAAO;AAC1K,UAAI,qBAAqB;AACzB,UAAI,eAAe,UAAU;AACzB,QAAAA,WAAU,cAAc,UAAU;AAClC,6BAAqB;AAAA,MACzB,WACS,eAAe,YAAY,UAAU;AAC1C,QAAAA,WAAU,cAAc,UAAU,SAAS,iBAAiB,SAAS;AAAA,MACzE,OACK;AACD,QAAAA,WAAU,cAAc,UAAU;AAAA,MACtC;AACA,WAAK,kBAAkB,GAAG;AAC1B,WAAK,cAAc,GAAG;AACtB,WAAK,8BAA8B,oBAAoB,GAAG;AAC1D,UAAI,UAAU;AACV,uBAAe,UAAU,uBAAuB,SAAS,kBAAkB,KAAK,KAAK;AAAA,MACzF;AAAA,IACJ;AACA,SAAK,oBAAoB,CAAC,QAAQ;AAC9B,YAAM,WAAW,KAAK,WAAW,aAAa,eAAe,8BAA8B;AAC3F,UAAI,CAAC,YAAY;AACb;AAAA,MACJ;AACA,UAAI,WAAW,qBAAqB,QAAW;AAC3C,uBAAe,YAAY,UAAU,SAAS,aAAa,GAAG;AAC9D;AAAA,MACJ;AACA,UAAI,WAAW,gBAAgB,QAAW;AACtC;AAAA,MACJ;AACA,UAAI,KAAK;AACL,mBAAW,YAAY,SAAS,cAAc;AAAA,MAClD,OACK;AACD,mBAAW,eAAe,SAAS,cAAc;AAAA,MACrD;AAAA,IACJ;AACA,SAAK,gBAAgB,CAAC,QAAQ;AAC1B,YAAM,WAAW,KAAK,WAAWA,aAAY,KAAK,WAAW,UAAUA,WAAU;AACjF,UAAI,CAAC,QAAQ,cAAc,OAAO,QAAQ;AACtC;AAAA,MACJ;AACA,UAAI,OAAO,mBAAmB,aAAa;AACvC,uBAAe,QAAQ,aAAa,SAAS,QAAQ,GAAG;AACxD;AAAA,MACJ;AACA,YAAM,WAAWA,WAAU,OAAO;AAClC,UAAI,KAAK,mBAAmB,CAAC,KAAK;AAC9B,YAAI,UAAU;AACV,eAAK,gBAAgB,UAAU,QAAQ;AAAA,QAC3C;AACA,aAAK,gBAAgB,WAAW;AAChC,eAAO,KAAK;AAAA,MAChB,WACS,CAAC,KAAK,mBAAmB,OAAO,UAAU;AAC/C,aAAK,kBAAkB,IAAI,eAAe,OAAO,YAAY;AACzD,gBAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,QAAQ;AACvD,cAAI,CAAC,OAAO;AACR;AAAA,UACJ;AACA,gBAAM,KAAK,oBAAoB;AAAA,QACnC,CAAC;AACD,aAAK,gBAAgB,QAAQ,QAAQ;AAAA,MACzC;AAAA,IACJ;AACA,SAAK,aAAa,MAAM;AACpB,YAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AACA,YAAM,EAAE,MAAM,IAAI;AAClB,YAAM,WAAW;AACjB,YAAM,eAAe,MAAM;AAAA,IAC/B;AACA,SAAK,mBAAmB,CAAC,MAAM;AAC3B,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,EAAE,MAAM,IAAIA,WAAU;AAC3F,YAAM,SAAS;AACf,UAAI,UAAU;AACd,YAAM,gBAAgB,MAAM;AAC5B,UAAI,CAAC,iBAAiB,CAAC,QAAQ,cAAc,OAAO,QAAQ,QAAQ;AAChE;AAAA,MACJ;AACA,iBAAW,CAAC,EAAE,MAAM,KAAKA,WAAU,SAAS;AACxC,YAAI,CAAC,OAAO,oBAAoB;AAC5B;AAAA,QACJ;AACA,kBAAU,OAAO,mBAAmB,aAAa;AACjD,YAAI,SAAS;AACT;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,SAAS;AACV,aAAK,mBAAmB,CAAC;AAAA,MAC7B;AACA,YAAM,WAAW;AAAA,IACrB;AACA,SAAK,oBAAoB,MAAM;AAC3B,YAAM,gBAAgB,KAAK,UAAU;AACrC,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AACA,YAAM,QAAQ,cAAc;AAC5B,aAAO,MAAM;AACb,aAAO,MAAM;AACb,aAAO,MAAM;AACb,oBAAc,SAAS;AACvB,YAAM,SAAS;AACf,YAAM,WAAW;AAAA,IACrB;AACA,SAAK,kBAAkB,CAAC,MAAM;AAC1B,YAAMA,aAAY,KAAK,WAAW,UAAUA,WAAU,eAAe,gBAAgBA,WAAU,eAAe,WAAWA,WAAU,OAAO;AAC1I,UAAI,CAAC,iBAAiB,CAAC,cAAc,SAAS;AAC1C;AAAA,MACJ;AACA,oBAAc,MAAM,SAAS;AAC7B,UAAI;AACJ,UAAI,EAAE,KAAK,WAAW,SAAS,GAAG;AAC9B,aAAK,WAAW;AAChB,cAAM,aAAa;AACnB,YAAI,cAAc,YAAY,QAAQ;AAClC,cAAI,UAAU;AACV,kBAAM,aAAa,SAAS,sBAAsB;AAClD,kBAAM;AAAA,cACF,GAAG,WAAW,UAAU,WAAW;AAAA,cACnC,GAAG,WAAW,UAAU,WAAW;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ,WACS,QAAQ,cAAc,cAAc,UAAU;AACnD,gBAAM,SAAS,WAAW,QAAQ,SAAS,WAAW;AACtD,cAAI,UAAU,UAAU,UAAU;AAC9B,kBAAM,aAAa,OAAO,sBAAsB,GAAG,aAAa,OAAO,sBAAsB,GAAG,aAAa,SAAS,sBAAsB;AAC5I,kBAAM;AAAA,cACF,GAAG,WAAW,UAAU,IAAI,WAAW,QAAQ,WAAW,OAAO,WAAW;AAAA,cAC5E,GAAG,WAAW,UAAU,IAAI,WAAW,OAAO,WAAW,MAAM,WAAW;AAAA,YAC9E;AAAA,UACJ,OACK;AACD,kBAAM;AAAA,cACF,GAAG,WAAW,WAAW,WAAW;AAAA,cACpC,GAAG,WAAW,WAAW,WAAW;AAAA,YACxC;AAAA,UACJ;AAAA,QACJ,WACS,WAAW,WAAW,UAAU;AACrC,gBAAM;AAAA,YACF,GAAG,WAAW,WAAW,WAAW;AAAA,YACpC,GAAG,WAAW,WAAW,WAAW;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ,OACK;AACD,aAAK,WAAW,EAAE,SAAS;AAC3B,YAAI,UAAU;AACV,gBAAM,aAAa,GAAG,YAAY,WAAW,QAAQ,WAAW,QAAQ,SAAS,CAAC,GAAG,aAAa,SAAS,sBAAsB;AACjI,gBAAM;AAAA,YACF,GAAG,UAAU,WAAW,WAAW,QAAQ;AAAA,YAC3C,GAAG,UAAU,WAAW,WAAW,OAAO;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,UAAUA,WAAU,OAAO;AACjC,UAAI,KAAK;AACL,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,MACb;AACA,oBAAc,MAAM,WAAW;AAC/B,oBAAc,SAAS;AAAA,IAC3B;AACA,SAAK,YAAY,CAAC,MAAM;AACpB,YAAM,MAAM,GAAG,UAAU,MAAM,KAAK,IAAI,cAAc;AACtD,iBAAW,SAAS,SAAS;AACzB,aAAK,SAAS,OAAO,MAAM,UAAU;AAAA,MACzC;AACA,WAAK,kBAAkB;AAAA,IAC3B;AACA,SAAK,iBAAiB,CAAC,MAAM;AACzB,YAAM,MAAM,GAAG,UAAU,MAAM,KAAK,IAAI,cAAc;AACtD,iBAAW,SAAS,SAAS;AACzB,aAAK,SAAS,OAAO,MAAM,UAAU;AAAA,MACzC;AACA,WAAK,iBAAiB,CAAC;AAAA,IAC3B;AACA,SAAK,cAAc,CAAC,MAAM;AACtB,YAAM,MAAM,GAAG,UAAU,MAAM,KAAK,IAAI,cAAc;AACtD,iBAAW,SAAS,SAAS;AACzB,aAAK,SAAS,IAAI,MAAM,YAAY,YAAY,IAAI,CAAC;AAAA,MACzD;AACA,WAAK,gBAAgB,CAAC;AAAA,IAC1B;AACA,SAAK,WAAW;AAChB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,YAAY;AAAA,MACb,WAAW,MAAM,KAAK,WAAW;AAAA,MACjC,YAAY,MAAM,KAAK,kBAAkB;AAAA,MACzC,WAAW,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAAA,MACxC,SAAS,CAAC,MAAM,KAAK,iBAAiB,CAAC;AAAA,MACvC,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC;AAAA,MACrC,WAAW,CAAC,MAAM,KAAK,gBAAgB,CAAC;AAAA,MACxC,UAAU,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,MACjC,aAAa,CAAC,MAAM,KAAK,UAAU,CAAC;AAAA,MACpC,eAAe,CAAC,MAAM,KAAK,eAAe,CAAC;AAAA,MAC3C,kBAAkB,MAAM,KAAK,wBAAwB;AAAA,MACrD,aAAa,CAAC,MAAM,KAAK,mBAAmB,CAAC;AAAA,MAC7C,gBAAgB,CAAC,MAAM,KAAK,mBAAmB,CAAC;AAAA,MAChD,QAAQ,MAAM;AACV,aAAK,oBAAoB;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,SAAK,iBAAiB,IAAI;AAAA,EAC9B;AAAA,EACA,kBAAkB;AACd,SAAK,iBAAiB,KAAK;AAAA,EAC/B;AACJ;;;AC7TO,IAAM,eAAN,MAAM,cAAa;AAAA,EACtB,cAAc;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,OAAO,OAAO,QAAQ,MAAM;AACxB,UAAM,QAAQ,IAAI,cAAa;AAC/B,UAAM,KAAK,MAAM;AACjB,QAAI,SAAS,QAAW;AACpB,UAAI,SAAS,IAAI,KAAK,QAAQ,IAAI,GAAG;AACjC,cAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,MAC9B,OACK;AACD,cAAM,KAAK,IAAI;AAAA,MACnB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM;AACP,SAAI,6BAAM,WAAU,QAAW;AAC3B;AAAA,IACJ;AACA,SAAK,QAAQ,KAAK;AAAA,EACtB;AACJ;;;ACvBO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,MAAM,QAAQ;AACnB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC3D;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACjCO,IAAM,sBAAN,MAA0B;AAAA,EAC7B,cAAc;AACV,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,MAAM,QAAQ;AACnB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC3D;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;AChBO,IAAM,iBAAN,MAAqB;AAAA,EACxB,cAAc;AACV,SAAK,YAAY;AACjB,SAAK,QAAQ,IAAI,oBAAoB;AACrC,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAS,SAAS,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK,MAAM,IAAI,KAAK;AACnE,WAAK,MAAM,KAAK,MAAM,UAAU,SAAY,QAAQ,EAAE,MAAa,CAAC;AAAA,IACxE;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACxBO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;AChBO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACfO,IAAM,WAAN,MAAe;AAAA,EAClB,cAAc;AACV,SAAK,YAAY,CAAC;AAClB,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,KAAK;AACL,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,GAAG,OAAO;AACV,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,MAAM;AACN,WAAO,0BAA0B,KAAK,WAAW,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC9E;AAAA,EACA,IAAI,IAAI,OAAO;AACX,SAAK,YAAY,0BAA0B,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;AAAA,EACpE;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,MAAM,KAAK,OAAO,KAAK,aAAa,KAAK;AAC/C,QAAI,QAAQ,QAAW;AACnB,WAAK,MAAM;AAAA,IACf;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;AC/CO,IAAM,WAAN,MAAe;AAAA,EAClB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACnBO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,OAAO,CAAC;AACb,SAAK,WAAW,IAAI,SAAS;AAAA,EACjC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,SAAS,KAAK,KAAK,QAAQ;AAAA,EACpC;AACJ;;;ACnBO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,SAAS,QAAW;AACpB;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACXO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,UAAU,IAAI,WAAW;AAC9B,SAAK,QAAQ,IAAI,SAAS;AAC1B,SAAK,UAAU,IAAI,WAAW;AAC9B,SAAK,SAAS,IAAI,YAAY;AAAA,EAClC;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,MAAM,OAAO;AACb,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,QAAQ,KAAK,KAAK,WAAW,KAAK,OAAO;AAC9C,UAAM,QAAQ,KAAK,SAAS,KAAK;AACjC,QAAI,UAAU,QAAW;AACrB,WAAK,QAAQ,0BAA0B,OAAO,CAAC,MAAM;AACjD,cAAM,MAAM,IAAI,SAAS;AACzB,YAAI,KAAK,CAAC;AACV,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,SAAK,QAAQ,KAAK,KAAK,WAAW,KAAK,OAAO;AAC9C,QAAI,UAAU,KAAK,MAAM,GAAG;AACxB,WAAK,OAAO,SAAS,KAAK;AAAA,IAC9B,OACK;AACD,WAAK,OAAO,KAAK,KAAK,MAAM;AAAA,IAChC;AAAA,EACJ;AACJ;;;ACnDO,IAAM,QAAN,MAAY;AAAA,EACf,YAAY,QAAQ,WAAW;AAC3B,SAAK,UAAU;AACf,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,YAAY;AAClB;AAAA,IACJ;AACA,UAAM,cAAc,KAAK,QAAQ,QAAQ,YAAY,IAAI,KAAK,UAAU;AACxE,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,eAAW,cAAc,aAAa;AAClC,UAAI,CAAC,WAAW,iBAAiB;AAC7B;AAAA,MACJ;AACA,iBAAW,gBAAgB,MAAM,IAAI;AAAA,IACzC;AAAA,EACJ;AACJ;;;ACrBO,IAAM,gBAAN,MAAoB;AAAA,EACvB,YAAY,QAAQ,WAAW;AAC3B,SAAK,YAAY;AACjB,SAAK,SAAS,IAAI,OAAO;AACzB,SAAK,QAAQ,IAAI,MAAM,QAAQ,SAAS;AAAA,EAC5C;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,aAAa,KAAK;AACzC,QAAI,cAAc,QAAW;AACzB,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAC9B;AACJ;;;ACxBO,IAAM,iBAAN,MAAqB;AAAA,EACxB,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU;AACf,WAAK,WAAW;AAAA,QACZ,GAAG,KAAK,SAAS,KAAK;AAAA,QACtB,GAAG,KAAK,SAAS,KAAK;AAAA,QACtB,MAAM,KAAK,SAAS,QAAQ;AAAA,MAChC;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,WAAK,UAAU,WAAW,CAAC,GAAG,KAAK,OAAO;AAAA,IAC9C;AAAA,EACJ;AACJ;;;AChBO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,UAAI,KAAK,SAAS,UAAU;AACxB,aAAK,OAAO;AAAA,MAChB,OACK;AACD,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,WAAW,CAAC,GAAG,KAAK,OAAO;AAAA,IAC9C;AAAA,EACJ;AACJ;;;AC1BO,IAAM,eAAN,MAAmB;AAAA,EACtB,cAAc;AACV,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;AClBO,IAAM,QAAN,MAAY;AAAA,EACf,cAAc;AACV,SAAK,OAAO;AACZ,SAAK,UAAU,IAAI,aAAa;AAAA,EACpC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,QAAQ,KAAK,KAAK,OAAO;AAC9B,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,WAAW,CAAC,GAAG,KAAK,OAAO;AAAA,IAC9C;AAAA,EACJ;AACJ;;;AClBO,IAAM,iBAAN,MAAqB;AAAA,EACxB,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,cAAc,KAAK,MAAM;AAAA,IAC3C;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACpCO,IAAM,eAAN,MAAmB;AAAA,EACtB,cAAc;AACV,SAAK,IAAI,IAAI,eAAe;AAC5B,SAAK,IAAI,IAAI,eAAe;AAC5B,SAAK,IAAI,IAAI,eAAe;AAAA,EAChC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,EAAE,KAAK,KAAK,CAAC;AAClB,SAAK,EAAE,KAAK,KAAK,CAAC;AAClB,SAAK,EAAE,KAAK,KAAK,CAAC;AAAA,EACtB;AACJ;;;ACZO,IAAM,kBAAN,MAAM,yBAAwB,aAAa;AAAA,EAC9C,cAAc;AACV,UAAM;AACN,SAAK,YAAY,IAAI,aAAa;AAAA,EACtC;AAAA,EACA,OAAO,OAAO,QAAQ,MAAM;AACxB,UAAM,QAAQ,IAAI,iBAAgB;AAClC,UAAM,KAAK,MAAM;AACjB,QAAI,SAAS,QAAW;AACpB,UAAI,SAAS,IAAI,KAAK,QAAQ,IAAI,GAAG;AACjC,cAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,MAC9B,OACK;AACD,cAAM,KAAK,IAAI;AAAA,MACnB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,iBAAiB,KAAK;AAC5B,QAAI,mBAAmB,QAAW;AAC9B,UAAI,eAAe,WAAW,QAAW;AACrC,aAAK,UAAU,EAAE,KAAK,cAAc;AAAA,MACxC,OACK;AACD,aAAK,UAAU,KAAK,KAAK,SAAS;AAAA,MACtC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACpCO,IAAM,mBAAN,MAAuB;AAAA,EAC1B,cAAc;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;ACZO,IAAM,oBAAN,MAAwB;AAAA,EAC3B,cAAc;AACV,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACfO,IAAM,mBAAN,MAAuB;AAAA,EAC1B,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;AACO,IAAM,yBAAN,cAAqC,iBAAiB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,KAAK;AAAA,IAC7B;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,eAAe,QAAW;AAC/B,WAAK,aAAa,KAAK;AAAA,IAC3B;AAAA,EACJ;AACJ;;;ACvDO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,KAAK;AAAA,IAC7B;AAAA,EACJ;AACJ;;;ACZO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,SAAS,IAAI,OAAO;AACzB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,UAAU,KAAK,MAAM,GAAG;AACxB,WAAK,OAAO,SAAS,KAAK;AAAA,IAC9B,OACK;AACD,WAAK,OAAO,KAAK,KAAK,MAAM;AAAA,IAChC;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,OAAO,eAAe,MAAS;AAAA,IACpG;AAAA,EACJ;AACJ;;;ACtBO,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EACvD,cAAc;AACV,UAAM;AACN,SAAK,OAAO,eAAe;AAC3B,SAAK,QAAQ;AAAA,EACjB;AACJ;;;ACNO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,aAAa,IAAI,sBAAsB;AAC5C,SAAK,WAAW,IAAI,sBAAsB;AAAA,EAC9C;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,WAAW,KAAK,KAAK,UAAU;AACpC,SAAK,SAAS,KAAK,KAAK,QAAQ;AAAA,EACpC;AACJ;;;ACTO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,SAAS,IAAI,iBAAiB;AACnC,SAAK,SAAS,IAAI,gBAAgB;AAClC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU,IAAI,kBAAkB;AAAA,EACzC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,cAAc,KAAK,QAAQ;AAAA,IAC/C;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,QAAQ,KAAK,KAAK,OAAO;AAAA,EAClC;AACJ;;;AC7BO,IAAM,YAAN,MAAgB;AAAA,EACnB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,cAAc,KAAK,MAAM;AAAA,IAC3C;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AAAA,EACJ;AACJ;;;AChBO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AAAA,EACJ;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,OAAO,IAAI;AAAA,EACpB;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,OAAO,IAAI;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AAtBf;AAuBQ,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,cAAc,KAAK,QAAQ;AAAA,IAC/C;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,UAAM,YAAU,UAAK,WAAL,mBAAa,MAAK,KAAK;AACvC,QAAI,YAAY,QAAW;AACvB,WAAK,OAAO,IAAI;AAAA,IACpB;AACA,UAAM,YAAU,UAAK,WAAL,mBAAa,MAAK,KAAK;AACvC,QAAI,YAAY,QAAW;AACvB,WAAK,OAAO,IAAI;AAAA,IACpB;AAAA,EACJ;AACJ;;;ACzCO,IAAM,aAAN,MAAiB;AAAA,EACpB,cAAc;AACV,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,MAAM,QAAW;AACtB,WAAK,IAAI,KAAK;AAAA,IAClB;AACA,QAAI,KAAK,MAAM,QAAW;AACtB,WAAK,IAAI,KAAK;AAAA,IAClB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACvBO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,eAAe;AACpB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,cAAc,KAAK,YAAY;AAAA,IACvD;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,cAAc,KAAK,QAAQ;AAAA,IAC/C;AAAA,EACJ;AACJ;;;ACvBO,IAAM,WAAN,MAAe;AAAA,EAClB,cAAc;AACV,SAAK,QAAQ;AACb,SAAK,QAAQ,IAAI,gBAAgB;AACjC,SAAK,SAAS;AACd,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,YAAY,KAAK;AACtB,QAAI,KAAK,SAAS;AACd,WAAK,UAAU,WAAW,KAAK,SAAS,KAAK,OAAO;AAAA,IACxD;AAAA,EACJ;AACJ;;;ACxBO,IAAM,gBAAN,MAAoB;AAAA,EACvB,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC3D;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;ACZO,IAAM,YAAN,MAAgB;AAAA,EACnB,cAAc;AACV,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,OAAO,IAAI,cAAc;AAAA,EAClC;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,EACnC;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,UAAa,KAAK,cAAc,QAAW;AACzD,WAAK,KAAK,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK,UAAU,CAAC;AAAA,IACzD;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AACJ;;;AC3BO,IAAM,WAAN,MAAe;AAAA,EAClB,cAAc;AACV,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,SAAK,SAAS,KAAK,UAAU,KAAK;AAClC,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,QAAQ,KAAK,SAAS,KAAK;AAChC,SAAK,MAAM,KAAK,OAAO,KAAK;AAAA,EAChC;AACJ;;;ACdO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,eAAe;AACpB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,cAAc,KAAK,YAAY;AAAA,IACvD;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,UAAU;AACf,WAAK,WAAW,WAAW,CAAC,GAAG,KAAK,QAAQ;AAAA,IAChD;AAAA,EACJ;AACJ;;;ACXO,IAAM,OAAN,MAAW;AAAA,EACd,cAAc;AACV,SAAK,QAAQ,IAAI,UAAU;AAC3B,SAAK,UAAU,IAAI,YAAY;AAC/B,SAAK,SAAS,IAAI,WAAW;AAC7B,SAAK,QAAQ;AACb,SAAK,WAAW,CAAC;AACjB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU,IAAI,YAAY;AAC/B,SAAK,OAAO,IAAI,SAAS;AACzB,SAAK,WAAW,IAAI,SAAS;AAC7B,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,UAAU;AAC3B,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,OAAO;AACd,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,IAAI,aAAa;AACb,WAAO;AAAA,EACX;AAAA,EACA,IAAI,WAAW,GAAG;AAAA,EAClB;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,MAAM,OAAO;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,SAAS,UAAU;AAAA,EAC5B;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,OAAO;AAChB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,MAAM,KAAK,SAAS,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK,MAAM,IAAI,KAAK,KAAK;AACzE,SAAK,QAAQ,KAAK,KAAK,OAAO;AAC9B,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,cAAc,QAAW;AAC9B,WAAK,YAAY,KAAK;AAAA,IAC1B;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,SAAS,KAAK,QAAQ,IAChC;AAAA,QACE,YAAY,KAAK;AAAA,QACjB,UAAU,KAAK;AAAA,MACnB,IACE,EAAE,GAAG,KAAK,SAAS;AAAA,IAC7B;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,QAAQ,KAAK,KAAK,OAAO;AAC9B,UAAM,WAAW,KAAK,YAAY,KAAK,WAAW,KAAK;AACvD,QAAI,aAAa,QAAW;AACxB,UAAI,SAAS,QAAQ,GAAG;AACpB,aAAK,SAAS,KAAK,QAAQ;AAAA,MAC/B,OACK;AACD,aAAK,SAAS,KAAK;AAAA,UACf,SAAS;AAAA,QACb,CAAC;AAAA,MACL;AAAA,IACJ;AACA,SAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK;AACtC,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,SAAK,KAAK,KAAK,KAAK,IAAI;AACxB,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,SAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACzHO,IAAM,mBAAN,cAA+B,uBAAuB;AAAA,EACzD,cAAc;AACV,UAAM;AACN,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,YAAY,OAAO;AACnB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,KAAK,MAAM;AACP,SAAI,6BAAM,iBAAgB,UAAa,KAAK,iBAAiB,QAAW;AACpE,WAAK,eAAe,KAAK;AAAA,IAC7B;AACA,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACtBO,IAAM,UAAN,cAAsB,gBAAgB;AAAA,EACzC,cAAc;AACV,UAAM;AACN,SAAK,YAAY,IAAI,iBAAiB;AACtC,SAAK,OAAO,eAAe;AAC3B,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf,UAAM,YAAY,KAAK,aAAa,KAAK;AACzC,QAAI,cAAc,QAAW;AACzB,WAAK,UAAU,KAAK,SAAS;AAC7B,WAAK,QAAQ,cAAc,KAAK,OAAO,KAAK,UAAU,SAAS,KAAK,UAAU,eAAe,MAAS;AAAA,IAC1G;AAAA,EACJ;AACJ;;;AC3BO,IAAM,mBAAN,MAAuB;AAAA,EAC1B,cAAc;AACV,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,OAAO;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,WAAW,OAAO;AAClB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,UAAM,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK;AAC9C,QAAI,UAAU,QAAW;AACrB,WAAK,QAAQ;AAAA,IACjB;AACA,UAAM,SAAS,KAAK,UAAU,KAAK;AACnC,QAAI,WAAW,QAAW;AACtB,WAAK,SAAS;AAAA,IAClB;AAAA,EACJ;AACJ;;;ACvCO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,UAAU,IAAI,iBAAiB;AACpC,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,QAAQ,KAAK,KAAK,OAAO;AAC9B,UAAM,QAAQ,KAAK,SAAS,KAAK;AACjC,QAAI,UAAU,QAAW;AACrB,WAAK,QAAQ;AAAA,IACjB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AACJ;;;ACzBO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI,aAAa;AAC9B,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AACA,SAAK,MAAM,QAAQ;AAAA,EACvB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,QAAQ,aAAa,OAAO,KAAK,OAAO,KAAK,KAAK;AACvD,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B;AAAA,IACJ;AACA,QAAI,KAAK,OAAO,MAAM,QAAW;AAC7B,WAAK,OAAO,IAAI,KAAK,OAAO;AAAA,IAChC;AACA,QAAI,KAAK,OAAO,MAAM,QAAW;AAC7B,WAAK,OAAO,IAAI,KAAK,OAAO;AAAA,IAChC;AAAA,EACJ;AACJ;;;AChCA,IAAM,UAAU;AAAhB,IAA6B,aAAa;AAA1C,IAAkD,WAAW;AAA7D,IAAsE,cAAc;AAApF,IAA8F,aAAa;AAA3G,IAAsH,gBAAgB;AAC/H,IAAM,QAAN,MAAY;AAAA,EACf,cAAc;AACV,SAAK,YAAY,CAAC,MAAM,SAAS,QAAQ,gBAAgB;AACrD,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AACA,YAAM,cAAc,QAAQ,IAAI,GAAG,aAAa,cAAc,CAAC,IAAI,CAAC,GAAG,sBAAsB,gBAAgB,QAAQ,KAAK,QAAQ,OAAO,CAAC,GAAG,qBAAqB,gBAAgB,QAAQ,KAAK,QAAQ,MAAM,CAAC;AAC9M,UAAI,qBAAqB;AACrB,aAAK,QAAQ,OAAO,IAAI;AAAA,MAC5B;AACA,UAAI,sBAAsB,aAAa;AACnC,aAAK,QAAQ,MAAM,IAAI;AAAA,MAC3B;AACA,WAAK,QAAQ,OAAO,IAAI,WAAW,KAAK,QAAQ,OAAO,KAAK,YAAY,IAAI;AAC5E,UAAI,CAAC,KAAK,QAAQ,MAAM,KAAK,aAAa;AACtC,aAAK,QAAQ,MAAM,IAAI,WAAW,KAAK,QAAQ,MAAM,KAAK,YAAY,IAAI;AAAA,MAC9E;AAAA,IACJ;AACA,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACZ,WAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK,QAAQ,UAAU;AAAA,EAC5D;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,QAAQ,UAAU,IAAI,KAAK,QAAQ,OAAO,IAAI;AAAA,EACvD;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,OAAO;AACd,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,QAAQ;AACR,WAAQ,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,WAAW;AAAA,EAC9D;AAAA,EACA,IAAI,MAAM,OAAO;AACb,SAAK,QAAQ,WAAW,IAAI,KAAK,QAAQ,QAAQ,IAAI;AAAA,EACzD;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,OAAO,OAAO;AACd,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,UAAU;AACV,WAAQ,KAAK,QAAQ,UAAU,KAAK,KAAK,QAAQ,aAAa;AAAA,EAClE;AAAA,EACA,IAAI,QAAQ,OAAO;AACf,SAAK,QAAQ,aAAa,IAAI,KAAK,QAAQ,UAAU,IAAI;AAAA,EAC7D;AAAA,EACA,IAAI,SAAS;AACT,WAAO,CAAC;AAAA,EACZ;AAAA,EACA,IAAI,OAAO,QAAQ;AAAA,EACnB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,WAAW,KAAK;AACrC,QAAI,YAAY,QAAW;AACvB,iBAAW,SAAS,SAAS;AACzB,cAAM,OAAO,QAAQ,KAAK;AAC1B,YAAI,MAAM;AACN,eAAK,QAAQ,KAAK,IAAI,WAAW,KAAK,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,UAAU,KAAK,WAAW,SAAS,YAAY,IAAI;AACxD,SAAK,UAAU,KAAK,SAAS,YAAY,eAAe,KAAK;AAC7D,SAAK,UAAU,KAAK,SAAS,KAAK,QAAQ,UAAU,aAAa,IAAI;AACrE,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,QAAI,KAAK,SAAS,QAAW;AACzB,WAAK,OAAO,KAAK;AAAA,IACrB;AAAA,EACJ;AACJ;;;ACrFO,IAAM,gBAAN,cAA4B,uBAAuB;AAAA,EACtD,cAAc;AACV,UAAM;AACN,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,SAAS,OAAO;AAChB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,KAAK,MAAM;AACP,SAAI,6BAAM,cAAa,UAAa,KAAK,iBAAiB,QAAW;AACjE,WAAK,eAAe,KAAK;AAAA,IAC7B;AACA,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;;;ACtBO,IAAM,OAAN,cAAmB,gBAAgB;AAAA,EACtC,cAAc;AACV,UAAM;AACN,SAAK,YAAY,IAAI,cAAc;AACnC,SAAK,OAAO,eAAe;AAC3B,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,OAAO;AACP,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,UAAM,YAAY,KAAK,aAAa,KAAK;AACzC,QAAI,cAAc,QAAW;AACzB,WAAK,UAAU,KAAK,SAAS;AAC7B,WAAK,QAAQ,cAAc,KAAK,OAAO,KAAK,UAAU,SAAS,KAAK,UAAU,eAAe,MAAS;AAAA,IAC1G;AAAA,EACJ;AACJ;;;ACzBO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,KAAK,MAAM;AACP,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,gBAAgB,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IAC9D;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,cAAc,KAAK,OAAO;AAAA,IAC7C;AAAA,EACJ;AACJ;;;ACnBO,IAAM,SAAN,cAAqB,gBAAgB;AAAA,EACxC,cAAc;AACV,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,KAAK,MAAM;AACP,UAAM,KAAK,IAAI;AACf,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,gBAAgB,QAAW;AAChC,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,iBAAiB,QAAW;AACjC,WAAK,eAAe,KAAK;AAAA,IAC7B;AAAA,EACJ;AACJ;;;ACXO,IAAM,mBAAN,MAAuB;AAAA,EAC1B,YAAY,QAAQ,WAAW;AAC3B,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,SAAS,IAAI,gBAAgB;AAClC,SAAK,aAAa,IAAI,WAAW;AACjC,SAAK,QAAQ,IAAI,gBAAgB;AACjC,SAAK,MAAM,QAAQ;AACnB,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,SAAS,IAAI,gBAAgB;AAClC,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,mBAAmB;AACxB,SAAK,SAAS,IAAI,OAAO;AACzB,SAAK,QAAQ,IAAI,MAAM;AACvB,SAAK,OAAO,IAAI,KAAK;AACrB,SAAK,SAAS,IAAI,OAAO;AACzB,SAAK,SAAS,IAAI,OAAO;AAAA,EAC7B;AAAA,EACA,KAAK,MAAM;AA/Bf;AAgCQ,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,MAAM,KAAK,gBAAgB,OAAO,KAAK,OAAO,KAAK,KAAK,CAAC;AAC9D,QAAI,KAAK,WAAW,QAAW;AAC3B,iBAAW,SAAS,KAAK,QAAQ;AAC7B,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,SAAS,QAAW;AACpB,eAAK,OAAO,KAAK,IAAI,WAAW,KAAK,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI;AAAA,QAClE;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,KAAK,KAAK,KAAK,IAAI;AACxB,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,QAAQ,KAAK,KAAK,OAAO;AAC9B,QAAI,KAAK,qBAAqB,QAAW;AACrC,WAAK,mBAAmB,KAAK;AAAA,IACjC;AACA,SAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,SAAK,KAAK,KAAK,KAAK,IAAI;AACxB,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,SAAK,OAAO,KAAK,KAAK,MAAM;AAC5B,UAAM,eAAa,UAAK,SAAL,mBAAW,iBAAc,UAAK,SAAL,mBAAW;AACvD,QAAI,eAAe,QAAW;AAC1B,WAAK,WAAW,SAAS;AAAA,IAC7B;AACA,SAAK,WAAW,KAAK,KAAK,UAAU;AACpC,QAAI,KAAK,kBAAkB,QAAW;AAClC,WAAK,gBAAgB,WAAW,CAAC,GAAG,KAAK,aAAa;AAAA,IAC1D;AACA,UAAM,eAAe,KAAK,YAAU,UAAK,UAAL,mBAAY;AAChD,QAAI,cAAc;AACd,WAAK,SAAS,0BAA0B,cAAc,CAAC,MAAM;AACzD,cAAM,MAAM,IAAI,OAAO;AACvB,YAAI,KAAK,CAAC;AACV,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,QAAI,KAAK,YAAY;AACjB,YAAM,WAAW,KAAK,QAAQ,QAAQ,SAAS,IAAI,KAAK,UAAU;AAClE,UAAI,UAAU;AACV,mBAAW,WAAW,UAAU;AAC5B,cAAI,QAAQ,aAAa;AACrB,oBAAQ,YAAY,MAAM,IAAI;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,cAAc,KAAK,QAAQ,QAAQ,YAAY,IAAI,KAAK,UAAU;AACxE,UAAI,aAAa;AACb,mBAAW,cAAc,aAAa;AAClC,cAAI,WAAW,sBAAsB;AACjC,uBAAW,qBAAqB,MAAM,IAAI;AAAA,UAC9C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACzFO,SAAS,YAAY,YAAY,kBAAkB;AACtD,aAAW,iBAAiB,kBAAkB;AAC1C,YAAQ,KAAK,aAAa;AAAA,EAC9B;AACJ;AACO,SAAS,qBAAqB,QAAQ,cAAc,kBAAkB;AACzE,QAAM,UAAU,IAAI,iBAAiB,QAAQ,SAAS;AACtD,cAAY,SAAS,GAAG,gBAAgB;AACxC,SAAO;AACX;;;ACAO,IAAM,UAAN,MAAc;AAAA,EACjB,YAAY,QAAQ,WAAW;AAC3B,SAAK,oBAAoB,CAAC,SAAS;AAC/B,aAAQ,KAAK,OAAO,KAAK,CAAC,UAAU,MAAM,QAAQ,SAAS,MAAM,QAAQ,SAAS,IAAI,KAClF,KAAK,OAAO,KAAK,CAAC,UAAU,MAAM,QAAQ,SAAS,MAAM,QAAQ,SAAS,KAAK;AAAA,IACvF;AACA,SAAK,gBAAgB,CAAC,WAAW;AAC7B,WAAK,KAAK,KAAK,QAAQ,QAAQ,UAAU,MAAM,CAAC;AAAA,IACpD;AACA,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,aAAa,IAAI,WAAW;AACjC,SAAK,iBAAiB,IAAI,eAAe;AACzC,SAAK,gBAAgB,CAAC;AACtB,SAAK,QAAQ;AACb,SAAK,aAAa,IAAI,WAAW;AACjC,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,gBAAgB,IAAI,cAAc,QAAQ,SAAS;AACxD,SAAK,kBAAkB,CAAC;AACxB,SAAK,YAAY,qBAAqB,KAAK,SAAS,KAAK,UAAU;AACnE,SAAK,cAAc;AACnB,SAAK,yBAAyB;AAC9B,SAAK,aAAa,CAAC;AACnB,SAAK,SAAS;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS,CAAC;AACf,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,iBAAiB;AACjB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,eAAe,OAAO;AACtB,SAAK,WAAW,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,UAAU,OAAO;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,IAAI,gBAAgB;AAChB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,cAAc,OAAO;AACrB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,KAAK,MAAM;AA3Df;AA4DQ,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,QAAW;AAC3B,gCAA0B,KAAK,QAAQ,CAAC,WAAW,KAAK,cAAc,MAAM,CAAC;AAAA,IACjF;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,cAAc,KAAK,KAAK;AAAA,IACzC;AACA,UAAM,eAAe,KAAK,gBAAgB,KAAK;AAC/C,QAAI,iBAAiB,QAAW;AAC5B,WAAK,eAAe;AAAA,IACxB;AACA,QAAI,KAAK,aAAa,QAAW;AAC7B,WAAK,WAAW,cAAc,KAAK,QAAQ;AAAA,IAC/C;AACA,UAAM,WAAW,KAAK,YAAY,KAAK;AACvC,QAAI,aAAa,QAAW;AACxB,WAAK,WAAW;AAAA,IACpB;AACA,QAAI,KAAK,gBAAgB,QAAW;AAChC,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,QAAI,KAAK,2BAA2B,QAAW;AAC3C,WAAK,yBAAyB,KAAK;AAAA,IACvC;AACA,QAAI,KAAK,YAAY,QAAW;AAC5B,WAAK,UAAU,KAAK;AAAA,IACxB;AACA,SAAK,WAAW,KAAK,KAAK,UAAU;AACpC,UAAM,aAAa,KAAK,cAAc,KAAK;AAC3C,QAAI,UAAU,UAAU,GAAG;AACvB,WAAK,WAAW,SAAS;AAAA,IAC7B,OACK;AACD,WAAK,WAAW,KAAK,UAAU;AAAA,IACnC;AACA,SAAK,eAAe,KAAK,KAAK,cAAc;AAC5C,SAAK,cAAc,KAAK,KAAK,aAAa;AAC1C,QAAI,KAAK,iBAAiB;AACtB,WAAK,kBAAkB,KAAK,gBAAgB,IAAI,CAAC,MAAM;AACnD,cAAM,MAAM,IAAI,eAAe;AAC/B,YAAI,KAAK,CAAC;AACV,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,SAAK,UAAU,KAAK,KAAK,SAAS;AAClC,SAAK,QAAQ,WAAW,KAAK,OAAO,KAAK,KAAK;AAC9C,SAAK,QAAQ,QAAQ,YAAY,MAAM,IAAI;AAC3C,QAAI,KAAK,WAAW,QAAW;AAC3B,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,UAAM,cAAc,KAAK,QAAQ,QAAQ,YAAY,IAAI,KAAK,UAAU;AACxE,QAAI,aAAa;AACb,iBAAW,cAAc,aAAa;AAClC,YAAI,WAAW,aAAa;AACxB,qBAAW,YAAY,MAAM,IAAI;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,KAAK,eAAe,QAAW;AAC/B,iBAAW,cAAc,KAAK,YAAY;AACtC,cAAM,gBAAgB,IAAI,WAAW;AACrC,sBAAc,KAAK,UAAU;AAC7B,aAAK,WAAW,KAAK,aAAa;AAAA,MACtC;AAAA,IACJ;AACA,SAAK,WAAW,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;AACtD,QAAI,KAAK,WAAW,QAAW;AAC3B,iBAAW,SAAS,KAAK,QAAQ;AAC7B,cAAM,gBAAgB,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,MAAM,IAAI;AACnE,YAAI,CAAC,eAAe;AAChB,gBAAM,WAAW,IAAI,MAAM;AAC3B,mBAAS,KAAK,KAAK;AACnB,eAAK,OAAO,KAAK,QAAQ;AAAA,QAC7B,OACK;AACD,wBAAc,KAAK,KAAK;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,cAAc,QAAO,UAAK,kBAAkB,MAAM,MAA7B,mBAAgC;AAC1D,SAAK,cAAc,SAAQ,UAAK,kBAAkB,OAAO,MAA9B,mBAAiC;AAAA,EAChE;AAAA,EACA,cAAc,OAAO,SAAS,gBAAgB;AAC1C,SAAK,KAAK,cAAc;AACxB,UAAM,oBAAoB,KAAK,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS,YAAY,SAAS,EAAE,WAAW,OAAO,aAAa,EAAE,WAAW,UAAU,KAAK;AACnJ,SAAK,KAAK,uDAAmB,OAAO;AACpC,WAAO,uDAAmB;AAAA,EAC9B;AAAA,EACA,SAAS,MAAM;AACX,QAAI,MAAM;AACN,YAAM,cAAc,KAAK,OAAO,KAAK,CAAC,UAAU,MAAM,SAAS,IAAI;AACnE,UAAI,aAAa;AACb,aAAK,KAAK,YAAY,OAAO;AAAA,MACjC;AAAA,IACJ,OACK;AACD,YAAM,aAAa,eAAe,8BAA8B,GAAG,iBAAiB,cAAc,WAAW,SAAS,eAAe,KAAK,kBAAkB,iBAAiB,SAAS,OAAO;AAC7L,UAAI,cAAc;AACd,aAAK,KAAK,aAAa,OAAO;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACvKO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,QAAQ,WAAW;AAC3B,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,eAAe,OAAO,QAAQ,eAAe,KAAK,WAAW,IAAI;AACtE,SAAK,uBAAuB,CAAC;AAC7B,SAAK,uBAAuB,CAAC;AAAA,EACjC;AAAA,EACA,MAAM,iBAAiB,OAAO;AAC1B,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,UAAU,KAAM,MAAM,WAAW,SAAS,KAAK;AAAA,IAC9D;AAAA,EACJ;AAAA,EACA,gBAAgB,MAAM;AAClB,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,mBAAmB,WAAW,gBAAgB,IAAI;AAAA,IACjE;AAAA,EACJ;AAAA,EACA,OAAO;AACH,SAAK,uBAAuB,CAAC;AAC7B,SAAK,uBAAuB,CAAC;AAC7B,eAAW,cAAc,KAAK,cAAc;AACxC,cAAQ,WAAW,MAAM;AAAA,QACrB,KAAK;AACD,eAAK,qBAAqB,KAAK,UAAU;AACzC;AAAA,QACJ,KAAK;AACD,eAAK,qBAAqB,KAAK,UAAU;AACzC;AAAA,MACR;AACA,iBAAW,KAAK;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,MAAM,kBAAkB,UAAU,OAAO;AACrC,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,MAAM,UAAU,KAAK;AAAA,IACpC;AACA,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,UAAU,QAAQ,KAAM,MAAM,WAAW,SAAS,UAAU,KAAK;AAAA,IAChF;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,UAAU;AAClB,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,UAAU,KAAK,WAAW,MAAM,QAAQ;AAAA,IACvD;AACA,eAAW,cAAc,KAAK,sBAAsB;AAChD,iBAAW,UAAU,QAAQ,KAAK,WAAW,MAAM,QAAQ;AAAA,IAC/D;AAAA,EACJ;AACJ;;;ACxCA,IAAM,aAAa,CAAC,SAAS;AACzB,MAAI,CAAC,UAAU,KAAK,SAAS,KAAK,UAAU,GAAG;AAC3C;AAAA,EACJ;AACA,QAAM,WAAW,KAAK,SAAS;AAC/B,MAAI,KAAK,QAAQ,KAAK,WAAW,UAAU;AACvC,SAAK,MAAM,CAAC,KAAK,MAAM;AAAA,EAC3B,WACS,KAAK,QAAQ,UAAU;AAC5B,SAAK,MAAM,KAAK,MAAM;AAAA,EAC1B;AACJ;AACO,IAAM,WAAN,MAAe;AAAA,EAClB,YAAY,QAAQ,IAAI,WAAW,UAAU,iBAAiB,OAAO;AACjE,SAAK,YAAY;AACjB,SAAK,gBAAgB,CAACE,YAAWC,WAAU,QAAQ,WAAW,MAAM;AAChE,iBAAW,CAAC,EAAE,MAAM,KAAKD,WAAU,SAAS;AACxC,cAAM,YAAY,OAAO,qBAAqB,SAAY,OAAO,iBAAiBC,WAAU,IAAI,IAAI;AACpG,YAAI,WAAW;AACX,iBAAO,SAAS,OAAO,UAAU,GAAG,UAAU,GAAG,MAAM;AAAA,QAC3D;AAAA,MACJ;AACA,YAAM,aAAaD,WAAU,OAAO,MAAM,gBAAgB,kCAAkC;AAAA,QACxF,MAAM;AAAA,QACN,UAAUC;AAAA,MACd,CAAC,GAAG,MAAM,SAAS,OAAO,cAAc,GAAG,cAAc,GAAG,MAAM,GAAG,SAAS,KAAK,UAAU,GAAG,WAAW,KAAK,QAAQ,KAAK,UAAU,gBAAgB,CAAC,YAAY;AAChK,mBAAW;AAAA,UACP;AAAA,UACA,YAAY,CAAC,UAAU,mBAAmB;AAAA,UAC1C,OAAO,IAAI;AAAA,UACX,UAAUD,WAAU,OAAO,KAAK;AAAA,UAChC,OAAO,CAAC,UAAW,IAAI,KAAK;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,GAAG,cAAc,CAAC,YAAY;AAC1B,mBAAW;AAAA,UACP;AAAA,UACA,YAAY,CAAC,UAAU,iBAAiB;AAAA,UACxC,OAAO,IAAI;AAAA,UACX,UAAUA,WAAU,OAAO,KAAK;AAAA,UAChC,OAAO,CAAC,UAAW,IAAI,KAAK;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL;AACA,oBAAc,SAAS,QAAQ,SAAS,OAAO;AAC/C,oBAAc,SAAS,SAAS,SAAS,OAAO;AAChD,kBAAY,SAAS,OAAO,SAAS,OAAO;AAC5C,kBAAY,SAAS,UAAU,SAAS,OAAO;AAC/C,UAAI,KAAK,cAAc,KAAK,QAAQ,GAAG;AACnC,eAAO,KAAK,cAAcA,YAAW,QAAW,QAAQ,WAAW,CAAC;AAAA,MACxE;AACA,aAAO;AAAA,IACX;AACA,SAAK,qBAAqB,MAAM;AAC5B,YAAM,eAAe,wBAAwB,KAAK,SAAS,GAAG,MAAM,aAAa,KAAK,GAAG,cAAc,KAAK,QAAQ;AACpH,UAAI,YAAY,cAAc,YAAY,YAAY,cAAc,WAAW;AAC3E,eAAO;AAAA,MACX;AACA,YAAM,MAAO,KAAK,KAAK,MAAO,cAAc,YAAY,MAAM,KAAK,GAAG,YAAa,KAAK,KAAK,MAAO,cAAc,YAAY,MAAM,MAAM,GAAG,QAAQ;AAAA,QACjJ,MAAM,YAAY,MAAM;AAAA,QACxB,OAAO,YAAY,MAAM;AAAA,MAC7B;AACA,UAAI,CAAC,YAAY,UAAU;AACvB,YAAI,SAAS,cAAc,cAAc,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,MACrE;AACA,UAAI,YAAY,UAAU,OAAO,YAAY,UAAU,UAAU;AAC7D,YAAI,UAAU,UAAU;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AACA,SAAK,gBAAgB,CAAC,KAAK,WAAW,MAAM;AACxC,YAAM,oBAAoB,KAAK,QAAQ,YAAY,SAAS,KAAK,UAAU;AAC3E,UAAI,CAAC,kBAAkB,QAAQ;AAC3B,eAAO;AAAA,MACX;AACA,YAAM,iBAAiB,kBAAkB;AACzC,UAAI,eAAe,QAAQ;AACvB,eAAO;AAAA,MACX;AACA,YAAM,UAAU,eAAe;AAC/B,UAAI,WAAW,KAAK,WAAW,SAAS;AACpC,cAAM,IAAI,MAAM,GAAG,WAAW,8CAA8C;AAAA,MAChF;AACA,aAAO,CAAC,CAAC,KAAK,UAAU,UAAU,KAAK,CAAC,aAAa,YAAY,KAAK,SAAS,QAAQ,IAAI,SAAS,SAAS,UAAU,CAAC;AAAA,IAC5H;AACA,SAAK,gBAAgB,CAAC,UAAU;AAC5B,UAAI,CAAC,SAAS,CAAC,KAAK,QAAS,CAAC,KAAK,aAAa,CAAC,KAAK,KAAK,OAAQ;AAC/D,eAAO;AAAA,MACX;AACA,YAAM,aAAa,KAAK,KAAK,cAAc,KAAK,KAAK,WAAW,IAAI,GAAG,UAAU,KAAK,KAAK,aAAa,KAAK,KAAK,IAAI,GAAG,SAAS,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,YAAY,KAAK,KAAK,WAAW,IAAI;AAC5M,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AACA,UAAI,KAAK,WAAW;AAChB,eAAO,KAAK;AAAA,MAChB;AACA,UAAI,KAAK,KAAK,OAAO;AACjB,eAAO,SAAS,OAAO,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK;AAAA,MACtE;AACA,aAAO;AAAA,IACX;AACA,SAAK,gBAAgB,CAACC,cAAa;AAC/B,YAAMD,aAAY,KAAK,WAAW,cAAc,cAAc,KAAK,QAAQ,OAAO,KAAK;AACvF,WAAK,WAAW,KAAK,cAAcA,YAAWC,WAAU,MAAM,aAAa,GAAGD,WAAU,OAAO,CAAC;AAChG,WAAK,kBAAkB,KAAK,SAAS,KAAK;AAC1C,YAAM,aAAaA,WAAU,OAAO;AACpC,WAAK,aAAa;AAAA,QACd,GAAG,YAAY,KAAK,QAAQ,KAAK,QAAQ,UAAU;AAAA,QACnD,QAAQ,KAAK,QAAQ,KAAK,OAAO,UAAU;AAAA,QAC3C,MAAM,KAAK,QAAQ,KAAK,OAAO,QAAQ;AAAA,MAC3C;AACA,WAAK,YAAY,0BAA0B,KAAK,QAAQ,KAAK,WAAW,KAAK,UAAU,KAAK,UAAU;AACtG,cAAQ,KAAK,QAAQ,KAAK,WAAW;AAAA,QACjC,KAAK;AACD,eAAK,UAAU;AACf;AAAA,QACJ,KAAK;AACD,eAAK,UAAU;AACf;AAAA,MACR;AACA,WAAK,SAAS,OAAO;AAAA,IACzB;AACA,SAAK,iBAAiB,CAAC,cAAc,qBAAqB;AACtD,YAAM,YAAY,aAAa,QAAQ,KAAK,KAAK;AACjD,UAAI,CAAC,WAAW;AACZ;AAAA,MACJ;AACA,aAAO,WAAW;AAAA,QACd,OAAO,aAAa;AAAA,QACpB,MAAM,aAAa;AAAA,MACvB,GAAG,yBAAyB,WAAW,KAAK,IAAI,gBAAgB,CAAC;AAAA,IACrE;AACA,SAAK,UAAU;AACf,SAAK,KAAK,IAAI,UAAU,iBAAiB,KAAK;AAAA,EAClD;AAAA,EACA,QAAQ,UAAU;AACd,QAAI,KAAK,eAAe,KAAK,WAAW;AACpC;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,SAAK,OAAO,UAAU;AACtB,SAAK,KAAK,UAAU;AACpB,UAAM,YAAY,KAAK,WAAW,gBAAgB,KAAK;AACvD,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,UAAI,OAAO,mBAAmB;AAC1B,eAAO,kBAAkB,MAAM,QAAQ;AAAA,MAC3C;AAAA,IACJ;AACA,eAAW,WAAW,UAAU,UAAU,UAAU;AAChD,UAAI,QAAQ,mBAAmB;AAC3B,gBAAQ,kBAAkB,MAAM,QAAQ;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,eAAe;AACf,oBAAc,MAAM,IAAI;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,KAAK,OAAO;AACR,UAAM,YAAY,KAAK;AACvB,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,gBAAU,OAAO,mBAAmB,QAAQ,MAAM,KAAK;AAAA,IAC3D;AACA,cAAU,OAAO,aAAa,MAAM,KAAK;AAAA,EAC7C;AAAA,EACA,eAAe;AACX,WAAO,KAAK,cAAc,KAAK,OAAO,SAAS,oBAAoB,KAAK,KAAK,CAAC;AAAA,EAClF;AAAA,EACA,UAAU;AACN,WAAQ,KAAK,UAAU,KAAK,IAAI,KAAK,KAAM;AAAA,EAC/C;AAAA,EACA,cAAc;AACV,WAAO;AAAA,MACH,GAAG,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,MACjC,GAAG,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,MACjC,GAAG,KAAK,SAAS;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,YAAY;AACR,WAAO,KAAK,OAAO,UAAU,KAAK,KAAK;AAAA,EAC3C;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK,cAAc,KAAK,OAAO,SAAS,oBAAoB,KAAK,WAAW,CAAC;AAAA,EACxF;AAAA,EACA,KAAK,IAAI,UAAU,iBAAiB,OAAO;AACvC,UAAM,YAAY,KAAK,WAAW,SAAS,KAAK;AAChD,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AAAA,MACV,aAAa,CAAC;AAAA,IAClB;AACA,SAAK,UAAU;AACf,SAAK,qBAAqB;AAC1B,UAAM,UAAU,UAAU,OAAO,YAAY,cAAc,UAAU,eAAe,mBAAmB,qBAAqB,KAAK,SAAS,WAAW,YAAY,SAAS,GAAG,YAAY,iBAAiB,MAAM,MAAM,EAAE,iBAAiB,IAAI;AAC7O,SAAK,QAAQ,yBAAyB,WAAW,KAAK,IAAI,gBAAgB;AAC1E,UAAM,eAAe,iBAAiB;AACtC,QAAI,mBAAmB,gBAAgB,SAAS,gBAAgB,MAAM,MAAM;AACxE,YAAM,oBAAoB,gBAAgB,MAAM,MAAM,QAAQ,yBAAyB,mBAAmB,KAAK,IAAI,gBAAgB;AACnI,UAAI,OAAO;AACP,aAAK,QAAQ;AACb,qBAAa,KAAK,gBAAgB,KAAK;AAAA,MAC3C;AAAA,IACJ;AACA,SAAK,YAAY,KAAK,eAAe,cAAc,gBAAgB;AACnE,qBAAiB,KAAK,eAAe;AACrC,UAAM,YAAY,KAAK;AACvB,QAAI,WAAW;AACX,uBAAiB,KAAK,UAAU,SAAS;AAAA,IAC7C;AACA,UAAM,gBAAgB,IAAI,cAAc,QAAQ,SAAS;AACzD,kBAAc,KAAK,UAAU,cAAc,aAAa;AACxD,kBAAc,KAAK,iBAAiB,aAAa;AACjD,SAAK,gBAAgB;AACrB,SAAK,QAAO,uCAAW,SAAQ,iBAAiB,MAAM;AACtD,SAAK,SAAQ,uCAAW,UAAS,iBAAiB,MAAM;AACxD,SAAK,UAAU;AACf,UAAM,cAAc,KAAK,QAAQ,KAAK;AACtC,SAAK,YAAY,SAAS,YAAY,KAAK,IAAI;AAC/C,QAAI,YAAY,WAAW;AACvB,WAAK,gBAAgB,KAAK,QAAQ,QAAQ,iBAAiB,YAAY,SAAS;AAChF,UAAI,KAAK,iBAAiB,UAAU,QAAQ,YAAY,WAAW,KAAK,aAAa,GAAG;AACpF,aAAK,cAAc,KAAK,SAAS;AAAA,MACrC;AAAA,IACJ;AACA,cAAU,OAAO,aAAa,IAAI;AAClC,SAAK,OAAO,kCAAkC,KAAK,QAAQ,MAAM,OAAO;AACxE,SAAK,SAAS;AAAA,MACV,SAAS;AAAA,IACb;AACA,SAAK,OAAO;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACZ;AACA,SAAK,cAAc,QAAQ;AAC3B,SAAK,kBAAkB,KAAK,mBAAmB;AAC/C,SAAK,WAAW,KAAK,gBAAgB,KAAK;AAC1C,SAAK,YAAY,IAAI,cAAc,KAAK,QAAQ,KAAK,KAAK;AAC1D,UAAM,YAAY,UAAU;AAC5B,cAAU,YAAY,UAAU,aAAa,UAAU,aAAa,KAAK,SAAS;AAClF,cAAU,aAAa,KAAK,SAAS;AACrC,SAAK,eAAe,KAAK,SAAS,IAAI,UAAU;AAChD,SAAK,QAAQ;AACb,QAAI,SAAS,UAAU,QAAQ,IAAI,KAAK,KAAK;AAC7C,QAAI,CAAC,QAAQ;AACT,eAAS,KAAK,QAAQ,QAAQ,eAAe,KAAK,KAAK;AACvD,UAAI,QAAQ;AACR,kBAAU,QAAQ,IAAI,KAAK,OAAO,MAAM;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,UAAU,OAAO,WAAW;AAC5B,aAAO,UAAU,IAAI;AAAA,IACzB;AACA,UAAM,gBAAgB,iCAAQ;AAC9B,QAAI,eAAe;AACf,WAAK,QAAQ,cAAc,IAAI;AAAA,IACnC;AACA,SAAK,WAAW;AAChB,SAAK,cAAc,gBAAgB,KAAK,QAAQ,OAAO,KAAK;AAC5D,eAAW,WAAW,UAAU,UAAU,UAAU;AAChD,cAAQ,KAAK,IAAI;AAAA,IACrB;AACA,eAAW,SAAS,UAAU,UAAU,QAAQ;AAC5C,YAAM,QAAQ,MAAM,KAAK,IAAI;AAAA,IACjC;AACA,QAAI,UAAU,OAAO,cAAc;AAC/B,aAAO,aAAa,WAAW,IAAI;AAAA,IACvC;AACA,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,aAAO,mBAAmB,OAAO,gBAAgB,IAAI;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,UAAM,SAAS,KAAK,UAAU,GAAG,aAAa,KAAK,UAAU,OAAO,MAAM,WAAW,KAAK;AAC1F,WAAQ,SAAS,KAAK,CAAC,UACnB,SAAS,KAAK,CAAC,UACf,SAAS,KAAK,WAAW,SAAS,UAClC,SAAS,KAAK,WAAW,QAAQ;AAAA,EACzC;AAAA,EACA,YAAY;AACR,WAAO,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,eAAe;AAAA,EACpE;AAAA,EACA,QAAQ;AACJ,eAAW,WAAW,KAAK,UAAU,UAAU,UAAU;AACrD,cAAQ,SAAS,QAAQ,MAAM,IAAI;AAAA,IACvC;AAAA,EACJ;AACJ;;;AC9SO,IAAM,QAAN,MAAY;AAAA,EACf,YAAY,UAAU,UAAU;AAC5B,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AACJ;;;ACLO,IAAM,QAAN,MAAY;AAAA,EACf,YAAY,GAAG,GAAG;AACd,SAAK,WAAW;AAAA,MACZ;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;;;ACLO,IAAM,YAAN,MAAM,mBAAkB,MAAM;AAAA,EACjC,YAAY,GAAG,GAAG,OAAO,QAAQ;AAC7B,UAAM,GAAG,CAAC;AACV,SAAK,OAAO;AAAA,MACR;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS,OAAO;AACZ,UAAM,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,QAAQ,MAAM,KAAK;AAC5D,WAAO,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI;AAAA,EAC9F;AAAA,EACA,WAAW,OAAO;AACd,QAAI,iBAAiB,QAAQ;AACzB,YAAM,WAAW,IAAI;AAAA,IACzB;AACA,UAAM,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,QAAQ,OAAO,KAAK,UAAU,OAAO,MAAM,UAAU,QAAQ,iBAAiB,aAAY,MAAM,OAAO,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,KAAK,MAAM,OAAO,KAAK,MAAM;AACtM,WAAO,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EACpG;AACJ;;;AClBO,IAAM,SAAN,MAAM,gBAAe,MAAM;AAAA,EAC9B,YAAY,GAAG,GAAG,QAAQ;AACtB,UAAM,GAAG,CAAC;AACV,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,SAAS,OAAO;AACZ,WAAO,YAAY,OAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,EACrD;AAAA,EACA,WAAW,OAAO;AACd,UAAM,OAAO,KAAK,UAAU,OAAO,MAAM,UAAU,UAAU,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE,GAAG,IAAI,KAAK;AACtI,QAAI,iBAAiB,SAAQ;AACzB,YAAM,OAAO,IAAI,MAAM,QAAQ,OAAO,KAAK,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC;AAC/E,aAAO,OAAO;AAAA,IAClB,WACS,iBAAiB,WAAW;AACjC,YAAM,EAAE,OAAO,OAAO,IAAI,MAAM,MAAM,QAAQ,KAAK,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,QAAQ,IAAI,QAAQ,CAAC;AAC7G,aAAQ,SAAS,KAAK,KACjB,QAAQ,KAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,UAC5C,QAAQ,KAAK,SACb,QAAQ,KAAK;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AACJ;;;ACvBO,IAAM,WAAN,MAAM,UAAS;AAAA,EAClB,YAAY,WAAW,UAAU;AAC7B,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,aAAa,MAAM;AACpB,YAAM,EAAE,GAAG,EAAE,IAAI,KAAK,UAAU,UAAU,EAAE,OAAO,OAAO,IAAI,KAAK,UAAU,MAAM,EAAE,UAAAE,UAAS,IAAI;AAClG,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAK,MAAM,KAAK,IAAI,UAAS,IAAI,UAAU,IAAK,QAAQ,KAAM,IAAI,IAAI,IAAK,SAAS,KAAM,KAAK,MAAM,IAAI,CAAC,IAAK,IAAI,IAAK,QAAQ,GAAG,SAAS,CAAC,GAAGA,SAAQ,CAAC;AAAA,MAC7J;AACA,WAAK,WAAW;AAAA,IACpB;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,WAAW;AAChB,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA,EACA,OAAO,OAAO;AACV,QAAI,CAAC,KAAK,UAAU,SAAS,MAAM,QAAQ,GAAG;AAC1C,aAAO;AAAA,IACX;AACA,QAAI,KAAK,QAAQ,SAAS,KAAK,UAAU;AACrC,WAAK,QAAQ,KAAK,KAAK;AACvB,aAAO;AAAA,IACX;AACA,QAAI,CAAC,KAAK,UAAU;AAChB,WAAK,WAAW;AAAA,IACpB;AACA,WAAO,KAAK,MAAM,KAAK,CAAC,QAAQ,IAAI,OAAO,KAAK,CAAC;AAAA,EACrD;AAAA,EACA,MAAM,OAAO,OAAO,OAAO;AACvB,UAAM,MAAM,SAAS,CAAC;AACtB,QAAI,CAAC,MAAM,WAAW,KAAK,SAAS,GAAG;AACnC,aAAO,CAAC;AAAA,IACZ;AACA,eAAW,KAAK,KAAK,SAAS;AAC1B,UAAI,CAAC,MAAM,SAAS,EAAE,QAAQ,KAC1B,YAAY,MAAM,UAAU,EAAE,QAAQ,IAAI,EAAE,SAAS,UAAU,MAC9D,CAAC,SAAS,MAAM,EAAE,QAAQ,IAAI;AAC/B;AAAA,MACJ;AACA,UAAI,KAAK,EAAE,QAAQ;AAAA,IACvB;AACA,QAAI,KAAK,UAAU;AACf,iBAAW,OAAO,KAAK,OAAO;AAC1B,YAAI,MAAM,OAAO,OAAO,GAAG;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU,QAAQ,OAAO;AACjC,WAAO,KAAK,MAAM,IAAI,OAAO,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,KAAK;AAAA,EACvE;AAAA,EACA,eAAe,UAAU,MAAM,OAAO;AAClC,WAAO,KAAK,MAAM,IAAI,UAAU,SAAS,GAAG,SAAS,GAAG,KAAK,OAAO,KAAK,MAAM,GAAG,KAAK;AAAA,EAC3F;AACJ;;;AClDA,IAAM,gBAAgB;AACtB,IAAM,iBAAiB,CAAC,eAAe;AACnC,SAAO,IAAI,UAAU,CAAC,WAAW,QAAQ,GAAG,CAAC,WAAW,SAAS,GAAI,WAAW,QAAQ,IAAK,GAAI,WAAW,SAAS,IAAK,CAAC;AAC/H;AACO,IAAM,YAAN,MAAgB;AAAA,EACnB,YAAY,QAAQ,WAAW;AAC3B,SAAK,gBAAgB,CAAC,SAAS,aAAa,UAAU;AAb9D;AAcY,UAAI,GAAC,aAAQ,OAAO,YAAf,mBAAwB,SAAQ;AACjC;AAAA,MACJ;AACA,YAAM,gBAAgB,QAAQ,QAAQ,gBAAgB,KAAK,mBAAmB,cAAc,OAAO,GAAG,qBAAqB,cAAc,OAAO,oBAAoB,cAAc,QAAQ,IAAI,cAAc,QAAQ,oBAAoB,kBAAkB,KAAK,IAAI,oBAAoB,iBAAiB,IAAI,gBAAgB,aAAa,iBAAiB,KAAK,IAAI,KAAK,OAAO,KAAK,OAAO,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,MAAM;AAC3Z,WAAK,QAAQ,cAAc,QAAQ;AACnC,UAAI,iBAAiB,iBAAiB;AAClC,aAAK,KAAK,KAAK,IAAI,kBAAkB,cAAc,GAAG,QAAW,SAAS,KAAK;AAAA,MACnF,WACS,iBAAiB,iBAAiB;AACvC,aAAK,eAAe,iBAAiB,iBAAiB,KAAK;AAAA,MAC/D;AAAA,IACJ;AACA,SAAK,qBAAqB,CAAC,mBAAmB;AAC1C,YAAMC,aAAY,KAAK;AACvB,UAAI,CAACA,WAAU,OAAO,WAAW,CAAC,eAAe,QAAQ;AACrD,eAAO;AAAA,MACX;AACA,YAAM,SAASA,WAAU,OAAO,SAAS,UAAUA,WAAU,OAAO;AACpE,aAAQ,OAAO,QAAQ,OAAO,UAAW,eAAe,SAAS,WAAW,IAAI,eAAe;AAAA,IACnG;AACA,SAAK,gBAAgB,CAAC,UAAU,iBAAiB,OAAO,gBAAgB;AACpE,UAAI;AACA,YAAI,WAAW,KAAK,KAAK,IAAI;AAC7B,YAAI,UAAU;AACV,mBAAS,KAAK,KAAK,SAAS,UAAU,iBAAiB,KAAK;AAAA,QAChE,OACK;AACD,qBAAW,IAAI,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,YAAY,UAAU,iBAAiB,KAAK;AAAA,QACzG;AACA,YAAI,SAAS;AACb,YAAI,aAAa;AACb,mBAAS,YAAY,QAAQ;AAAA,QACjC;AACA,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AACA,aAAK,OAAO,KAAK,QAAQ;AACzB,aAAK,QAAQ,KAAK,QAAQ;AAC1B,aAAK;AACL,aAAK,QAAQ,cAAc,iBAAiB;AAAA,UACxC,WAAW,KAAK;AAAA,UAChB,MAAM;AAAA,YACF;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX,SACO,GAAG;AACN,kBAAU,EAAE,QAAQ,GAAG,WAAW,qBAAqB,CAAC,EAAE;AAC1D;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,kBAAkB,CAAC,OAAO,OAAO,aAAa;AAC/C,YAAM,WAAW,KAAK,OAAO,KAAK;AAClC,UAAI,CAAC,YAAY,SAAS,UAAU,OAAO;AACvC,eAAO;AAAA,MACX;AACA,eAAS,QAAQ,QAAQ;AACzB,YAAM,OAAO,KAAK,QAAQ,QAAQ,QAAQ;AAC1C,WAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,WAAK,QAAQ,OAAO,MAAM,CAAC;AAC3B,WAAK,KAAK,KAAK,QAAQ;AACvB,WAAK,QAAQ,cAAc,mBAAmB;AAAA,QAC1C,WAAW,KAAK;AAAA,QAChB,MAAM;AAAA,UACF;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO,CAAC;AACb,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,sBAAsB,IAAI,mBAAmB,QAAQ,SAAS;AACnE,UAAM,aAAa,UAAU,OAAO;AACpC,SAAK,WAAW,IAAI,SAAS,eAAe,UAAU,GAAG,aAAa;AACtE,SAAK,SAAS,KAAK,QAAQ,QAAQ,UAAU,WAAW,IAAI;AAC5D,SAAK,WAAW,KAAK,QAAQ,QAAQ,YAAY,WAAW,IAAI;AAAA,EACpE;AAAA,EACA,IAAI,QAAQ;AACR,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,qBAAqB;AACjB,UAAM,YAAY,KAAK,YAAY,UAAU,UAAU;AACvD,eAAW,YAAY,QAAQ,iBAAiB;AAC5C,WAAK,YAAY,SAAS,WAAW,YAAY,SAAS,UAAU,UAAU,OAAO,IAAI,IAAI,QAAW,SAAS,OAAO;AAAA,IAC5H;AAAA,EACJ;AAAA,EACA,YAAY,UAAU,iBAAiB,OAAO,aAAa;AACvD,UAAM,YAAY,KAAK,YAAY,UAAU,UAAU,eAAe,QAAQ,QAAQ,UAAU,OAAO;AACvG,QAAI,QAAQ,GAAG;AACX,YAAM,gBAAgB,KAAK,QAAQ,IAAI;AACvC,UAAI,gBAAgB,GAAG;AACnB,aAAK,eAAe,aAAa;AAAA,MACrC;AAAA,IACJ;AACA,WAAO,KAAK,cAAc,UAAU,iBAAiB,OAAO,WAAW;AAAA,EAC3E;AAAA,EACA,QAAQ;AACJ,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,UAAU;AACN,SAAK,SAAS,CAAC;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AAAA,EACrB;AAAA,EACA,MAAM,KAAK,OAAO;AACd,UAAM,YAAY,KAAK;AACvB,cAAU,OAAO,MAAM;AACvB,UAAM,KAAK,OAAO,KAAK;AACvB,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,gBAAU,OAAO,WAAW,QAAQ,KAAK;AAAA,IAC7C;AACA,eAAW,KAAK,KAAK,SAAS;AAC1B,QAAE,KAAK,KAAK;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,OAAO,WAAW;AACd,WAAO,KAAK,OAAO,OAAO,SAAS;AAAA,EACvC;AAAA,EACA,KAAK,WAAW;AACZ,WAAO,KAAK,OAAO,KAAK,SAAS;AAAA,EACrC;AAAA,EACA,gBAAgB,MAAM;AAClB,SAAK,oBAAoB,gBAAgB,IAAI;AAAA,EACjD;AAAA,EACA,OAAO;AApJX;AAqJQ,UAAM,YAAY,KAAK,YAAY,UAAU,UAAU;AACvD,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,QAAI,UAAU;AACd,SAAK,WAAW,KAAK,QAAQ,QAAQ,YAAY,WAAW,IAAI;AAChE,SAAK,oBAAoB,KAAK;AAC9B,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,UAAI,OAAO,4BAA4B,QAAW;AAC9C,kBAAU,OAAO,wBAAwB;AAAA,MAC7C;AACA,UAAI,SAAS;AACT;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,oBAAoB,KAAK;AAC9B,eAAW,CAAC,EAAE,aAAa,KAAK,UAAU,gBAAgB;AACtD,oBAAc,KAAK,SAAS;AAAA,IAChC;AACA,SAAK,mBAAmB;AACxB,QAAI,CAAC,SAAS;AACV,iBAAW,SAAS,QAAQ,UAAU,QAAQ;AAC1C,cAAM,eAAe,QAAQ,UAAU,OAAO,KAAK;AACnD,iBAAS,IAAI,KAAK,OAAO,IAAI,GAAG,MAAI,kBAAa,WAAb,mBAAqB,UAAS,IAAI,QAAQ,UAAU,OAAO,OAAO,KAAK,KAAK;AAC5G,eAAK,YAAY,QAAW,cAAc,KAAK;AAAA,QACnD;AAAA,MACJ;AACA,eAAS,IAAI,KAAK,OAAO,IAAI,QAAQ,UAAU,OAAO,OAAO,KAAK;AAC9D,aAAK,YAAY;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,KAAK,IAAI,OAAO,iBAAiB,OAAO;AACpC,SAAK,UAAU;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,WAAK,YAAY,+BAAO,UAAU,iBAAiB,KAAK;AAAA,IAC5D;AACA,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,MAAM,SAAS;AACX,SAAK,MAAM;AACX,SAAK,KAAK;AACV,UAAM,KAAK,KAAK,EAAE,OAAO,GAAG,QAAQ,EAAE,CAAC;AAAA,EAC3C;AAAA,EACA,OAAO,UAAU,OAAO,UAAU;AAC9B,SAAK,SAAS,KAAK,OAAO,QAAQ,QAAQ,GAAG,QAAW,OAAO,QAAQ;AAAA,EAC3E;AAAA,EACA,SAAS,OAAO,WAAW,GAAG,OAAO,UAAU;AAC3C,QAAI,QAAQ,KAAK,QAAQ,KAAK,OAAO;AACjC;AAAA,IACJ;AACA,QAAI,UAAU;AACd,aAAS,IAAI,OAAO,UAAU,YAAY,IAAI,KAAK,OAAO,KAAK;AAC3D,WAAK,gBAAgB,KAAK,OAAO,QAAQ,KAAK;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,eAAe,UAAU,OAAO;AAC5B,SAAK,SAAS,GAAG,UAAU,KAAK;AAAA,EACpC;AAAA,EACA,aAAa;AACT,UAAM,UAAU,KAAK,WAAW,eAAe,SAAS,QAAQ,UAAU;AAC1E,eAAW,SAAS,QAAQ;AACxB,WAAK,cAAc,OAAO,KAAK,GAAG,GAAG,KAAK;AAAA,IAC9C;AACA,SAAK,cAAc,QAAQ,WAAW,QAAQ,gBAAgB,MAAM;AAAA,EACxE;AAAA,EACA,MAAM,OAAO,OAAO;AAChB,UAAM,YAAY,KAAK,YAAY,oBAAoB,oBAAI,IAAI;AAC/D,SAAK,WAAW,IAAI,SAAS,eAAe,UAAU,OAAO,IAAI,GAAG,aAAa;AACjF,eAAW,CAAC,EAAE,aAAa,KAAK,UAAU,gBAAgB;AACtD,oBAAc,OAAO;AAAA,IACzB;AACA,eAAW,CAAC,EAAE,MAAM,KAAK,UAAU,SAAS;AACxC,aAAO,UAAU,OAAO,OAAO,KAAK;AAAA,IACxC;AACA,eAAW,YAAY,KAAK,QAAQ;AAChC,YAAM,eAAe,UAAU,OAAO;AACtC,UAAI,gBAAgB,CAAC,SAAS,oBAAoB;AAC9C,iBAAS,SAAS,KAAK,aAAa;AACpC,iBAAS,SAAS,KAAK,aAAa;AACpC,iBAAS,gBAAgB,KAAK,aAAa;AAC3C,iBAAS,gBAAgB,KAAK,aAAa;AAAA,MAC/C;AACA,eAAS,qBAAqB;AAC9B,YAAM,KAAK,oBAAoB,MAAM,QAAQ;AAC7C,iBAAW,CAAC,EAAE,MAAM,KAAK,KAAK,WAAW,SAAS;AAC9C,YAAI,SAAS,WAAW;AACpB;AAAA,QACJ;AACA,YAAI,OAAO,gBAAgB;AACvB,iBAAO,eAAe,UAAU,KAAK;AAAA,QACzC;AAAA,MACJ;AACA,iBAAW,SAAS,KAAK,QAAQ;AAC7B,cAAM,UAAU,QAAQ,KAAK,MAAM,KAAK,UAAU,KAAK;AAAA,MAC3D;AACA,UAAI,SAAS,WAAW;AACpB,0BAAkB,IAAI,QAAQ;AAC9B;AAAA,MACJ;AACA,WAAK,SAAS,OAAO,IAAI,MAAM,SAAS,YAAY,GAAG,QAAQ,CAAC;AAAA,IACpE;AACA,QAAI,kBAAkB,MAAM;AACxB,YAAM,cAAc,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC;AACnD,WAAK,SAAS,KAAK,OAAO,WAAW;AACrC,WAAK,UAAU,KAAK,QAAQ,OAAO,WAAW;AAC9C,WAAK,KAAK,KAAK,GAAG,iBAAiB;AAAA,IACvC;AACA,UAAM,KAAK,oBAAoB,iBAAiB,KAAK;AACrD,eAAW,YAAY,KAAK,QAAQ;AAChC,iBAAW,WAAW,KAAK,UAAU;AACjC,gBAAQ,OAAO,UAAU,KAAK;AAAA,MAClC;AACA,UAAI,CAAC,SAAS,aAAa,CAAC,SAAS,UAAU;AAC3C,cAAM,KAAK,oBAAoB,kBAAkB,UAAU,KAAK;AAAA,MACpE;AAAA,IACJ;AACA,WAAO,UAAU,OAAO;AACxB,QAAI,KAAK,WAAW;AAChB,YAAM,SAAS,KAAK;AACpB,aAAO,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,KAAK,EAAE,EAAE;AAChE,WAAK,aAAa,OAAO,OAAO,SAAS,CAAC,EAAE,SAAS;AACrD,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AACJ;;;AC/QO,IAAM,SAAN,MAAa;AAAA,EAChB,YAAY,WAAW;AACnB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,OAAO;AACH,UAAM,YAAY,KAAK,WAAW,UAAU,UAAU;AACtD,SAAK,aAAa,CAAC,QAAQ,gBAAgB,MAAM,IAAI,IAAI,OAAO;AAChE,SAAK,eAAe;AACpB,UAAM,QAAQ,KAAK;AACnB,QAAI,UAAU,OAAO,SAAS;AAC1B,YAAM,UAAU,UAAU,OAAO;AACjC,gBAAU,OAAO,KAAK,QAAQ,QAAQ,cAAc;AACpD,gBAAU,OAAO,KAAK,SAAS,QAAQ,eAAe;AAAA,IAC1D;AACA,UAAM,YAAY,QAAQ,WAAW,cAAc,UAAU;AAC7D,SAAK,kBAAkB,cAAc,YAAY,QAAQ,QAAQ,IAAI;AACrE,SAAK,WAAW,cAAc,YAAY,QAAQ,QAAQ,IAAI;AAC9D,SAAK,qBAAqB,cAAc,UAAU,KAAK,UAAU,KAAK,IAAI;AAAA,EAC9E;AAAA,EACA,aAAa,UAAU;AACnB,UAAM,UAAU,SAAS,SAAS,QAAQ,KAAK,YAAY,cAAc,QAAQ,MAAM,eAAe,YAAY,UAAU,QAAQ,SAAS;AAC7I,UAAM,kBAAkB,cAAc,YAAY,QAAQ,QAAQ,IAAI;AACtE,UAAM,YAAY,cAAc,YAAY,KAAK,IAAI;AACrD,UAAM,YAAY,cAAc,YAAY,KAAK,IAAI;AACrD,UAAM,qBAAqB,cAAc,QAAQ,KAAK,UAAU,KAAK,IAAI;AACzE,UAAM,cAAc,MAAM;AAC1B,gBAAY,aAAa,aAAa,eAAe,SAAY,aAAa,aAAa,QAAQ;AACnG,gBAAY,WAAW,aAAa,aAAa,SAAY,aAAa,WAAW,QAAQ;AAC7F,UAAM,WAAW,cAAc,YAAY,QAAQ,QAAQ,IAAI;AAAA,EACnE;AACJ;;;ACzBA,SAAS,WAAW,WAAW;AAC3B,SAAO,aAAa,CAAC,UAAU;AACnC;AACA,SAAS,UAAU,OAAO,WAAW,IAAI,SAAS,OAAO;AACrD,SAAO;AAAA,IACH;AAAA,IACA,QAAQ,SAAS,KAAK,WAAY,KAAK,QAAS;AAAA,EACpD;AACJ;AACA,SAAS,qBAAqB,QAAQ,cAAc,kBAAkB;AAClE,QAAM,UAAU,IAAI,QAAQ,QAAQ,SAAS;AAC7C,cAAY,SAAS,GAAG,gBAAgB;AACxC,SAAO;AACX;AACA,IAAM,0BAA0B;AAAhC,IAA2C,uBAAuB;AAAA,EAC9D,UAAU,CAAC,MAAM,EAAE;AAAA,EACnB,MAAM,MAAM;AAAA,EACZ;AAAA,EACA,QAAQ,MAAM;AAAA,EACd;AAAA,EACA,OAAO,MAAM;AAAA,EACb;AACJ;AACO,IAAM,YAAN,MAAgB;AAAA,EACnB,YAAY,QAAQ,IAAI,eAAe;AACnC,SAAK,KAAK;AACV,SAAK,uBAAuB,CAAC,YAAY;AACrC,UAAI,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,cAAc,wBAAwB;AACjE;AAAA,MACJ;AACA,iBAAW,SAAS,SAAS;AACzB,YAAI,MAAM,WAAW,KAAK,cAAc,SAAS;AAC7C;AAAA,QACJ;AACA,SAAC,MAAM,iBAAiB,KAAK,OAAO,KAAK,OAAO;AAAA,MACpD;AAAA,IACJ;AACA,SAAK,aAAa,OAAO,cAAc;AACnC,UAAI;AACA,YAAI,CAAC,KAAK,UACN,KAAK,kBAAkB,UACvB,YAAY,KAAK,gBAAgB,MAAO,KAAK,UAAU;AACvD,eAAK,KAAK,KAAK;AACf;AAAA,QACJ;AACA,aAAK,kBAAL,KAAK,gBAAkB;AACvB,cAAM,QAAQ,UAAU,YAAY,KAAK,eAAe,KAAK,UAAU,KAAK,MAAM;AAClF,aAAK,YAAY,MAAM,KAAK;AAC5B,aAAK,gBAAgB;AACrB,YAAI,MAAM,QAAQ,KAAM;AACpB,eAAK,KAAK,KAAK;AACf;AAAA,QACJ;AACA,cAAM,KAAK,UAAU,KAAK,KAAK;AAC/B,YAAI,CAAC,KAAK,MAAM,GAAG;AACf,eAAK,QAAQ;AACb;AAAA,QACJ;AACA,YAAI,KAAK,mBAAmB,GAAG;AAC3B,eAAK,KAAK,KAAK;AAAA,QACnB;AAAA,MACJ,SACO,GAAG;AACN,kBAAU,EAAE,MAAM,GAAG,WAAW,sBAAsB,CAAC;AAAA,MAC3D;AAAA,IACJ;AACA,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,wBAAwB;AAC7B,SAAK,SAAS,IAAI,OAAO,IAAI;AAC7B,SAAK,SAAS,IAAI,OAAO,IAAI;AAC7B,SAAK,YAAY,IAAI,UAAU,KAAK,SAAS,IAAI;AACjD,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,gBAAgB;AAAA,MACjB,OAAO;AAAA,QACH,UAAU;AAAA,QACV,QAAQ;AAAA,MACZ;AAAA,IACJ;AACA,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,WAAW,qBAAqB,KAAK,SAAS,IAAI;AACvD,SAAK,gBAAgB,qBAAqB,KAAK,SAAS,IAAI;AAC5D,SAAK,kBAAkB,IAAI,eAAe,IAAI;AAC9C,QAAI,OAAO,yBAAyB,eAAe,sBAAsB;AACrE,WAAK,wBAAwB,IAAI,qBAAqB,CAAC,YAAY,KAAK,qBAAqB,OAAO,CAAC;AAAA,IACzG;AACA,SAAK,QAAQ,cAAc,kBAAkB,EAAE,WAAW,KAAK,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,gBAAgB;AAChB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,gBAAgB,UAAU;AACtB,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,UAAM,KAAK,KAAK,cAAc;AAC9B,QAAI,CAAC,IAAI;AACL;AAAA,IACJ;AACA,UAAM,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC5C,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,YAAM,UAAU,KAAK,OAAO,YAAY,YAAY;AAAA,QAChD,GAAG,IAAI,IAAI;AAAA,QACX,GAAG,IAAI,IAAI;AAAA,MACf,GAAG,YAAY,KAAK,UAAU,SAAS,YAAY,WAAW,SAAS,OAAO;AAC9E,eAAS,GAAG,SAAS;AAAA,IACzB;AACA,UAAM,eAAe,CAAC,MAAM;AACxB,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,YAAM,aAAa,GAAG,MAAM;AAAA,QACxB,GAAG,WAAW,WAAW,WAAW;AAAA,QACpC,GAAG,WAAW,WAAW,WAAW;AAAA,MACxC;AACA,0BAAoB,GAAG,KAAK,CAAC;AAAA,IACjC;AACA,UAAM,oBAAoB,MAAM;AAC5B,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,gBAAU;AACV,mBAAa;AAAA,IACjB;AACA,UAAM,mBAAmB,MAAM;AAC3B,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,mBAAa;AAAA,IACjB;AACA,UAAM,kBAAkB,CAAC,MAAM;AAC3B,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,UAAI,WAAW,CAAC,YAAY;AACxB,cAAM,aAAa;AACnB,YAAI,YAAY,WAAW,QAAQ,WAAW,QAAQ,SAAS,CAAC;AAChE,YAAI,CAAC,WAAW;AACZ,sBAAY,WAAW,eAAe,WAAW,eAAe,SAAS,CAAC;AAC1E,cAAI,CAAC,WAAW;AACZ;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,UAAU,KAAK,OAAO,SAAS,aAAa,UAAU,QAAQ,sBAAsB,IAAI,QAAW,MAAM;AAAA,UAC3G,GAAG,UAAU,WAAW,aAAa,WAAW,OAAO;AAAA,UACvD,GAAG,UAAU,WAAW,aAAa,WAAW,MAAM;AAAA,QAC1D;AACA,4BAAoB,GAAG,KAAK,KAAK,IAAI,UAAU,SAAS,UAAU,OAAO,CAAC;AAAA,MAC9E;AACA,gBAAU;AACV,mBAAa;AAAA,IACjB;AACA,UAAM,qBAAqB,MAAM;AAC7B,UAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,MACJ;AACA,gBAAU;AACV,mBAAa;AAAA,IACjB;AACA,QAAI,UAAU,OAAO,aAAa;AAClC,OAAG,iBAAiB,SAAS,YAAY;AACzC,OAAG,iBAAiB,cAAc,iBAAiB;AACnD,OAAG,iBAAiB,aAAa,gBAAgB;AACjD,OAAG,iBAAiB,YAAY,eAAe;AAC/C,OAAG,iBAAiB,eAAe,kBAAkB;AAAA,EACzD;AAAA,EACA,YAAY,OAAO;AACf,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,QAAQ,KAAK,WAAW,WAAW,OAAO;AACtC,QAAI,CAAC,WAAW,IAAI,KAAM,CAAC,YAAY,KAAK,eAAe,IAAI,GAAG,GAAI;AAClE,aAAO;AAAA,IACX;AACA,SAAK,eAAe,IAAI,KAAK,aAAa,oBAAoB;AAC9D,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,WAAO,CAAC,KAAK,aAAa,KAAK,aAAa,KAAK;AAAA,EACrD;AAAA,EACA,UAAU;AACN,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,KAAK;AACV,SAAK,UAAU,QAAQ;AACvB,SAAK,OAAO,QAAQ;AACpB,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,WAAW,OAAO,QAAQ,IAAI;AAAA,IACzC;AACA,eAAW,OAAO,KAAK,QAAQ,KAAK,GAAG;AACnC,WAAK,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,SAAK,QAAQ,QAAQ,QAAQ,IAAI;AACjC,SAAK,YAAY;AACjB,UAAM,UAAU,KAAK,QAAQ,IAAI,GAAG,MAAM,QAAQ,UAAU,CAAC,MAAM,MAAM,IAAI;AAC7E,QAAI,OAAO,GAAG;AACV,cAAQ,OAAO,KAAK,CAAC;AAAA,IACzB;AACA,SAAK,QAAQ,cAAc,sBAAsB,EAAE,WAAW,KAAK,CAAC;AAAA,EACxE;AAAA,EACA,KAAK,OAAO;AACR,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,QAAI,cAAc;AAClB,SAAK,sBAAsB,sBAAsB,OAAO,cAAc;AAClE,UAAI,aAAa;AACb,aAAK,gBAAgB;AACrB,sBAAc;AAAA,MAClB;AACA,YAAM,KAAK,WAAW,SAAS;AAAA,IACnC,CAAC;AAAA,EACL;AAAA,EACA,MAAM,OAAO,MAAM,UAAU,CAAC,GAAG;AAC7B,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,UAAI,CAAC,OAAO,QAAQ;AAChB;AAAA,MACJ;AACA,YAAM,MAAM,MAAM,OAAO,OAAO,MAAM,OAAO;AAC7C,UAAI,CAAC,IAAI,WAAW;AAChB;AAAA,MACJ;AACA,aAAO,IAAI;AAAA,IACf;AACA,cAAU,EAAE,MAAM,GAAG,WAAW,8BAA8B,IAAI,YAAY;AAAA,EAClF;AAAA,EACA,qBAAqB;AACjB,WAAO,CAAC,KAAK,WAAW,CAAC,KAAK,cAAc,WAAW,IAAI;AAAA,EAC/D;AAAA,EACA,gBAAgB,MAAM;AAClB,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,UAAU,gBAAgB,IAAI;AACnC,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,mBAAmB,OAAO,gBAAgB,IAAI;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AACT,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,UAAM,SAAS,KAAK,QAAQ,QAAQ,mBAAmB;AACvD,eAAW,QAAQ,QAAQ;AACvB,YAAM,SAAS,KAAK,QAAQ,QAAQ,eAAe,IAAI;AACvD,UAAI,QAAQ;AACR,aAAK,QAAQ,IAAI,MAAM,MAAM;AAAA,MACjC;AAAA,IACJ;AACA,SAAK,WAAW,qBAAqB,KAAK,SAAS,MAAM,KAAK,uBAAuB,KAAK,aAAa;AACvG,SAAK,gBAAgB,qBAAqB,KAAK,SAAS,MAAM,KAAK,QAAQ;AAC3E,UAAM,mBAAmB,KAAK,QAAQ,QAAQ,oBAAoB,IAAI;AACtE,eAAW,CAAC,IAAI,MAAM,KAAK,kBAAkB;AACzC,WAAK,QAAQ,IAAI,IAAI,MAAM;AAAA,IAC/B;AACA,SAAK,OAAO,KAAK;AACjB,UAAM,KAAK,OAAO,KAAK;AACvB,SAAK,oBAAoB;AACzB,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,OAAO;AACnB,SAAK,UAAU,KAAK,cAAc;AAClC,SAAK,YAAY,cAAc,KAAK,cAAc,QAAQ,IAAI;AAC9D,SAAK,SAAS,cAAc,KAAK,cAAc,KAAK,IAAI;AACxD,SAAK,YAAY;AACjB,SAAK,WAAW,KAAK,cAAc,WAAW,IAAI,KAAK,cAAc,WAAW;AAChF,SAAK,SAAS,KAAK,cAAc;AACjC,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,QAAS,MAAM,OAAO,KAAK,IAAI;AAAA,IAC1C;AACA,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,QAAS,MAAM,OAAO,KAAK;AAAA,IACtC;AACA,SAAK,QAAQ,cAAc,iBAAiB,EAAE,WAAW,KAAK,CAAC;AAC/D,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,WAAW;AAC1B,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,kBAAkB,OAAO,eAAe;AAAA,IACnD;AACA,SAAK,QAAQ,cAAc,kBAAkB,EAAE,WAAW,KAAK,CAAC;AAAA,EACpE;AAAA,EACA,MAAM,UAAU,MAAM;AAClB,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,gBAAgB;AACrB,UAAM,KAAK,QAAQ;AAAA,EACvB;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,QAAI,KAAK,wBAAwB,QAAW;AACxC,2BAAqB,KAAK,mBAAmB;AAC7C,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,KAAK,SAAS;AACd;AAAA,IACJ;AACA,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,SAAS,OAAO,MAAM;AAAA,IACjC;AACA,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,QAAQ,cAAc,mBAAmB,EAAE,WAAW,KAAK,CAAC;AAAA,EACrE;AAAA,EACA,KAAK,OAAO;AACR,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,UAAM,cAAc,KAAK,WAAW;AACpC,QAAI,KAAK,eAAe,CAAC,KAAK,cAAc,UAAU;AAClD,WAAK,cAAc;AACnB;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,WAAK,UAAU;AAAA,IACnB;AACA,QAAI,aAAa;AACb,iBAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,YAAI,OAAO,MAAM;AACb,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,QAAQ,cAAc,iBAAiB,EAAE,WAAW,KAAK,CAAC;AAC/D,SAAK,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,MAAM,UAAU;AACZ,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,KAAK;AACV,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,MAAM,QAAQ;AACV,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,wBAAwB;AAC7B,SAAK,WAAW,qBAAqB,KAAK,SAAS,IAAI;AACvD,SAAK,gBAAgB,qBAAqB,KAAK,SAAS,MAAM,KAAK,QAAQ;AAC3E,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA,EACA,SAAS,kBAAkBC,OAAM,QAAQ;AACrC,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB;AAAA,IACJ;AACA,SAAK,QAAQ,kBAAkBA,OAAM,MAAM;AAAA,EAC/C;AAAA,EACA,QAAQ,iBAAiBA,OAAM,QAAQ;AACnC,QAAI,CAAC,mBAAmB,CAAC,WAAW,IAAI,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,gBAAgB,EAAE,GAAG,qBAAqB;AAChD,QAAI,WAAW,eAAe,GAAG;AAC7B,oBAAc,WAAW;AACzB,UAAIA,OAAM;AACN,sBAAc,OAAOA;AAAA,MACzB;AACA,UAAI,QAAQ;AACR,sBAAc,SAAS;AAAA,MAC3B;AAAA,IACJ,OACK;AACD,YAAM,eAAe;AACrB,oBAAc,WAAW,gBAAgB,YAAY,aAAa;AAClE,oBAAc,OAAO,gBAAgB,QAAQ,aAAa;AAC1D,oBAAc,SAAS,gBAAgB,UAAU,aAAa;AAAA,IAClE;AACA,SAAK,QAAQ,yBAAyB,eAAe,IAAI;AAAA,EAC7D;AAAA,EACA,MAAM,QAAQ;AACV,QAAI,CAAC,WAAW,IAAI,KAAK,KAAK,SAAS;AACnC;AAAA,IACJ;AACA,UAAM,KAAK,KAAK;AAChB,SAAK,UAAU;AACf,UAAM,IAAI,QAAQ,CAAC,YAAY;AAC3B,WAAK,gBAAgB,WAAW,YAAY;AACxC,aAAK,gBAAgB,aAAa;AAClC,YAAI,KAAK,cAAc,mBAAmB,eAAe,KAAK,uBAAuB;AACjF,eAAK,sBAAsB,QAAQ,KAAK,cAAc,OAAO;AAAA,QACjE;AACA,mBAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,iBAAO,SAAU,MAAM,OAAO,MAAM;AAAA,QACxC;AACA,aAAK,QAAQ,cAAc,oBAAoB,EAAE,WAAW,KAAK,CAAC;AAClE,aAAK,KAAK;AACV,gBAAQ;AAAA,MACZ,GAAG,KAAK,MAAM;AAAA,IAClB,CAAC;AAAA,EACL;AAAA,EACA,OAAO;AACH,QAAI,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,SAAS;AACpC;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,mBAAa,KAAK,aAAa;AAC/B,aAAO,KAAK;AAAA,IAChB;AACA,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,gBAAgB,gBAAgB;AACrC,SAAK,MAAM;AACX,SAAK,UAAU,MAAM;AACrB,SAAK,OAAO,KAAK;AACjB,QAAI,KAAK,cAAc,mBAAmB,eAAe,KAAK,uBAAuB;AACjF,WAAK,sBAAsB,UAAU,KAAK,cAAc,OAAO;AAAA,IACnE;AACA,eAAW,CAAC,EAAE,MAAM,KAAK,KAAK,SAAS;AACnC,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC/B;AACA,eAAW,OAAO,KAAK,QAAQ,KAAK,GAAG;AACnC,WAAK,QAAQ,OAAO,GAAG;AAAA,IAC3B;AACA,SAAK,iBAAiB,KAAK;AAC3B,SAAK,QAAQ,cAAc,oBAAoB,EAAE,WAAW,KAAK,CAAC;AAAA,EACtE;AAAA,EACA,sBAAsB;AAClB,SAAK,cAAc,aAAa,CAAC;AACjC,UAAM,cAAc,KAAK,cAAc,cAAc,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,YAAY,KAAK,QAAQ;AAClH,SAAK,cAAc,SAAS,KAAK,aAAa;AAC9C,QAAI,KAAK,uBAAuB,aAAa;AACzC,aAAO;AAAA,IACX;AACA,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACX;AACJ;;;ACxcO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,aAAa,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA,iBAAiB,MAAM,UAAU;AAC7B,SAAK,oBAAoB,MAAM,QAAQ;AACvC,QAAI,MAAM,KAAK,WAAW,IAAI,IAAI;AAClC,QAAI,CAAC,KAAK;AACN,YAAM,CAAC;AACP,WAAK,WAAW,IAAI,MAAM,GAAG;AAAA,IACjC;AACA,QAAI,KAAK,QAAQ;AAAA,EACrB;AAAA,EACA,cAAc,MAAM,MAAM;AACtB,UAAM,YAAY,KAAK,WAAW,IAAI,IAAI;AAC1C,iBAAa,UAAU,QAAQ,CAAC,YAAY,QAAQ,IAAI,CAAC;AAAA,EAC7D;AAAA,EACA,iBAAiB,MAAM;AACnB,WAAO,CAAC,CAAC,KAAK,WAAW,IAAI,IAAI;AAAA,EACrC;AAAA,EACA,wBAAwB,MAAM;AAC1B,QAAI,CAAC,MAAM;AACP,WAAK,aAAa,oBAAI,IAAI;AAAA,IAC9B,OACK;AACD,WAAK,WAAW,OAAO,IAAI;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,oBAAoB,MAAM,UAAU;AAChC,UAAM,MAAM,KAAK,WAAW,IAAI,IAAI;AACpC,QAAI,CAAC,KAAK;AACN;AAAA,IACJ;AACA,UAAM,SAAS,IAAI,QAAQ,MAAM,IAAI,QAAQ,QAAQ;AACrD,QAAI,MAAM,GAAG;AACT;AAAA,IACJ;AACA,QAAI,WAAW,GAAG;AACd,WAAK,WAAW,OAAO,IAAI;AAAA,IAC/B,OACK;AACD,UAAI,OAAO,KAAK,CAAC;AAAA,IACrB;AAAA,EACJ;AACJ;;;AC3CA,SAAS,wBAAwB,WAAW,KAAK,cAAc,QAAQ,OAAO;AAC1E,MAAI,MAAM,IAAI,IAAI,SAAS;AAC3B,MAAI,CAAC,OAAO,OAAO;AACf,UAAM,CAAC,GAAG,aAAa,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AACxD,QAAI,IAAI,WAAW,GAAG;AAAA,EAC1B;AACA,SAAO;AACX;AACO,IAAM,UAAN,MAAc;AAAA,EACjB,YAAY,QAAQ;AAChB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB;AAAA,MACjB,aAAa,oBAAI,IAAI;AAAA,MACrB,QAAQ,oBAAI,IAAI;AAAA,MAChB,UAAU,oBAAI,IAAI;AAAA,IACtB;AACA,SAAK,cAAc,oBAAI,IAAI;AAC3B,SAAK,SAAS,oBAAI,IAAI;AACtB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,iBAAiB,oBAAI,IAAI;AAAA,EAClC;AAAA,EACA,cAAc,MAAM,gBAAgB;AAChC,SAAK,cAAc,YAAY,IAAI,MAAM,cAAc;AAAA,EAC3D;AAAA,EACA,iBAAiB,MAAM,WAAW;AAC9B,SAAK,cAAc,OAAO,IAAI,MAAM,SAAS;AAAA,EACjD;AAAA,EACA,mBAAmB,MAAM,aAAa;AAClC,SAAK,cAAc,SAAS,IAAI,MAAM,WAAW;AAAA,EACrD;AAAA,EACA,iBAAiB,MAAM,eAAe;AAClC,KAAC,KAAK,iBAAiB,IAAI,KAAK,KAAK,eAAe,IAAI,MAAM,aAAa;AAAA,EAC/E;AAAA,EACA,UAAU,QAAQ;AACd,KAAC,KAAK,UAAU,OAAO,EAAE,KAAK,KAAK,QAAQ,KAAK,MAAM;AAAA,EAC1D;AAAA,EACA,UAAU,WAAW,SAAS,WAAW,OAAO;AAC5C,KAAC,YAAY,CAAC,KAAK,UAAU,SAAS,MAAM,KAAK,QAAQ,IAAI,WAAW,OAAO;AAAA,EACnF;AAAA,EACA,eAAe,OAAO,QAAQ;AAC1B,8BAA0B,OAAO,CAAC,SAAS;AACvC,OAAC,KAAK,eAAe,IAAI,KAAK,KAAK,QAAQ,IAAI,MAAM,MAAM;AAAA,IAC/D,CAAC;AAAA,EACL;AAAA,EACA,QAAQ,WAAW;AACf,SAAK,SAAS,OAAO,SAAS;AAC9B,SAAK,OAAO,OAAO,SAAS;AAC5B,SAAK,YAAY,OAAO,SAAS;AAAA,EACrC;AAAA,EACA,oBAAoB,WAAW;AAC3B,UAAM,MAAM,oBAAI,IAAI;AACpB,eAAW,UAAU,KAAK,SAAS;AAC/B,aAAO,YAAY,UAAU,aAAa,KAAK,IAAI,IAAI,OAAO,IAAI,OAAO,UAAU,SAAS,CAAC;AAAA,IACjG;AACA,WAAO;AAAA,EACX;AAAA,EACA,eAAe,WAAW,QAAQ,OAAO;AACrC,WAAO,wBAAwB,WAAW,KAAK,aAAa,KAAK,cAAc,aAAa,KAAK;AAAA,EACrG;AAAA,EACA,UAAU,WAAW,QAAQ,OAAO;AAChC,WAAO,wBAAwB,WAAW,KAAK,QAAQ,KAAK,cAAc,QAAQ,KAAK;AAAA,EAC3F;AAAA,EACA,iBAAiB,MAAM;AACnB,WAAO,KAAK,eAAe,IAAI,IAAI;AAAA,EACvC;AAAA,EACA,UAAU,QAAQ;AACd,WAAO,KAAK,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM;AAAA,EACnD;AAAA,EACA,UAAU,QAAQ;AACd,WAAO,KAAK,QAAQ,IAAI,MAAM;AAAA,EAClC;AAAA,EACA,eAAe,MAAM;AACjB,WAAO,KAAK,QAAQ,IAAI,IAAI;AAAA,EAChC;AAAA,EACA,qBAAqB;AACjB,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC7B;AAAA,EACA,YAAY,WAAW,QAAQ,OAAO;AAClC,WAAO,wBAAwB,WAAW,KAAK,UAAU,KAAK,cAAc,UAAU,KAAK;AAAA,EAC/F;AAAA,EACA,YAAY,SAAS,eAAe;AAChC,eAAW,UAAU,KAAK,SAAS;AAC/B,aAAO,YAAY,SAAS,aAAa;AAAA,IAC7C;AAAA,EACJ;AAAA,EACA,qBAAqB,WAAW,YAAY,eAAe;AACvD,UAAM,WAAW,KAAK,SAAS,IAAI,SAAS;AAC5C,QAAI,CAAC,UAAU;AACX;AAAA,IACJ;AACA,eAAW,WAAW,UAAU;AAC5B,cAAQ,eAAe,QAAQ,YAAY,SAAS,GAAG,aAAa;AAAA,IACxE;AAAA,EACJ;AACJ;;;AC5FA,eAAe,eAAe,MAAM;AAChC,QAAM,MAAM,yBAAyB,KAAK,KAAK,KAAK,KAAK;AACzD,MAAI,CAAC,KAAK;AACN,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,WAAW,MAAM,MAAM,GAAG;AAChC,MAAI,SAAS,IAAI;AACb,WAAO,SAAS,KAAK;AAAA,EACzB;AACA,YAAU,EAAE,MAAM,GAAG,WAAW,IAAI,SAAS,MAAM,+BAA+B;AAClF,SAAO,KAAK;AAChB;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,CAAC,OAAO,MAAM,CAAC,OAAO,WAAW,CAAC,OAAO,OAAO,CAAC,OAAO;AACnE;AACA,SAAS,SAAS,KAAK;AACnB,SAAO,CAAC,cAAc,GAAG;AAC7B;AACO,IAAM,SAAN,MAAa;AAAA,EAChB,cAAc;AACV,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,YAAY,CAAC;AAClB,SAAK,mBAAmB,IAAI,gBAAgB;AAC5C,SAAK,eAAe;AACpB,SAAK,UAAU,IAAI,QAAQ,IAAI;AAAA,EACnC;AAAA,EACA,IAAI,UAAU;AACV,UAAM,MAAM,CAAC;AACb,eAAW,CAAC,MAAM,MAAM,KAAK,KAAK,UAAU;AACxC,UAAI,IAAI,IAAI;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,UAAU;AACV,WAAO;AAAA,EACX;AAAA,EACA,UAAU,cAAc,QAAQ;AAC5B,QAAI,SAAS,YAAY,GAAG;AACxB,UAAI,QAAQ;AACR,eAAO,OAAO;AACd,aAAK,SAAS,IAAI,cAAc,MAAM;AAAA,MAC1C;AAAA,IACJ,OACK;AACD,WAAK,SAAS,IAAI,aAAa,QAAQ,WAAW,YAAY;AAAA,IAClE;AAAA,EACJ;AAAA,EACA,iBAAiB,MAAM,UAAU;AAC7B,SAAK,iBAAiB,iBAAiB,MAAM,QAAQ;AAAA,EACzD;AAAA,EACA,MAAM,cAAc,MAAM,uBAAuB,UAAU,MAAM;AAC7D,SAAK,QAAQ,cAAc,MAAM,qBAAqB;AACtD,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,SAAS,MAAM,kBAAkB,UAAU,MAAM;AACnD,SAAK,QAAQ,iBAAiB,MAAM,gBAAgB;AACpD,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,mBAAmB,MAAM,oBAAoB,UAAU,MAAM;AAC/D,SAAK,QAAQ,mBAAmB,MAAM,kBAAkB;AACxD,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,iBAAiB,MAAM,WAAW,UAAU,MAAM;AACpD,SAAK,QAAQ,iBAAiB,MAAM,SAAS;AAC7C,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,UAAU,QAAQ,UAAU,MAAM;AACpC,SAAK,QAAQ,UAAU,MAAM;AAC7B,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,UAAU,QAAQ,SAAS,WAAW,OAAO,UAAU,MAAM;AAC/D,SAAK,QAAQ,UAAU,QAAQ,SAAS,QAAQ;AAChD,UAAM,KAAK,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,SAAS,OAAO,QAAQ,eAAe,sBAAsB,kBAAkB,UAAU,MAAM;AACjG,QAAI;AACJ,QAAI,cAAc,SAAS,UAAU,iBAAiB;AACtD,QAAI,UAAU,aAAa,GAAG;AAC1B,oBAAc;AACd,iBAAW;AAAA,IACf,OACK;AACD,iBAAW;AAAA,IACf;AACA,QAAI,UAAU,oBAAoB,GAAG;AACjC,oBAAc;AACd,wBAAkB;AAAA,IACtB,OACK;AACD,wBAAkB;AAAA,IACtB;AACA,QAAI,UAAU,gBAAgB,GAAG;AAC7B,oBAAc;AACd,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc;AAAA,IAClB;AACA,QAAI,WAAW,MAAM,GAAG;AACpB,qBAAe;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,MACV;AAAA,IACJ,OACK;AACD,qBAAe;AAAA,IACnB;AACA,SAAK,QAAQ,eAAe,OAAO,YAAY;AAC/C,UAAM,KAAK,QAAQ,WAAW;AAAA,EAClC;AAAA,EACA,cAAc,MAAM,MAAM;AACtB,SAAK,iBAAiB,cAAc,MAAM,IAAI;AAAA,EAClD;AAAA,EACA,MAAM;AACF,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ,OAAO;AACX,UAAM,MAAM,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK;AACxC,QAAI,CAAC,QAAQ,KAAK,WAAW;AACzB,UAAI,OAAO,OAAO,CAAC;AACnB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO;AACH,QAAI,KAAK,cAAc;AACnB;AAAA,IACJ;AACA,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,MAAM,KAAK,wBAAwB,SAAS;AACxC,WAAO,KAAK,cAAc,wBAAwB,OAAO;AAAA,EAC7D;AAAA,EACA,MAAM,cAAc,wBAAwB,gBAAgB,OAAO;AAC/D,QAAI;AACJ,QAAI,CAAC,SAAS,sBAAsB,GAAG;AACnC,eAAS,CAAC;AACV,UAAI,SAAS,sBAAsB,GAAG;AAClC,eAAO,KAAK;AAAA,MAChB,OACK;AACD,eAAO,UAAU;AAAA,MACrB;AACA,UAAI,SAAS,cAAc,GAAG;AAC1B,eAAO,QAAQ;AAAA,MACnB,OACK;AACD,eAAO,UAAU,kBAAkB,OAAO;AAAA,MAC9C;AACA,aAAO,QAAQ,SAAS,OAAO;AAAA,IACnC,OACK;AACD,eAAS;AAAA,IACb;AACA,WAAO,KAAK,YAAY,MAAM;AAAA,EAClC;AAAA,EACA,MAAM,SAAS,OAAO,gBAAgB,OAAO;AACzC,QAAI,KAAK;AACT,QAAI,SAAS,cAAc,KAAK,mBAAmB,QAAW;AAC1D,YAAM;AAAA,IACV,OACK;AACD,WAAK;AACL,YAAM;AAAA,IACV;AACA,WAAO,KAAK,YAAY,EAAE,IAAQ,KAAK,MAAM,CAAC;AAAA,EAClD;AAAA,EACA,MAAM,QAAQ,UAAU,MAAM;AAC1B,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,SAAK,IAAI,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC;AAAA,EACzC;AAAA,EACA,oBAAoB,MAAM,UAAU;AAChC,SAAK,iBAAiB,oBAAoB,MAAM,QAAQ;AAAA,EAC5D;AAAA,EACA,MAAM,IAAI,IAAI,SAAS,SAAS,OAAO;AACnC,UAAM,SAAS,EAAE,MAAM;AACvB,QAAI,SAAS,EAAE,GAAG;AACd,aAAO,KAAK;AAAA,IAChB,OACK;AACD,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,mBAAmB,aAAa;AAChC,aAAO,UAAU;AAAA,IACrB,OACK;AACD,aAAO,UAAU;AAAA,IACrB;AACA,QAAI,SAAS,OAAO,GAAG;AACnB,aAAO,QAAQ;AAAA,IACnB,OACK;AACD,aAAO,UAAU,WAAW,OAAO;AAAA,IACvC;AACA,WAAO,KAAK,YAAY,MAAM;AAAA,EAClC;AAAA,EACA,MAAM,QAAQ,IAAI,SAAS,gBAAgB,OAAO;AAC9C,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,aAAa;AAC3B,aAAO,UAAU;AACjB,aAAO,MAAM;AACb,aAAO,QAAQ;AAAA,IACnB,OACK;AACD,aAAO,KAAK;AACZ,aAAO,UAAU;AACjB,aAAO,MAAM;AACb,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO,KAAK,YAAY,MAAM;AAAA,EAClC;AAAA,EACA,kBAAkB,UAAU;AACxB,UAAM,MAAM,KAAK,IAAI;AACrB,QAAI,CAAC,IAAI,QAAQ;AACb,YAAM,IAAI,MAAM,GAAG,WAAW,+DAA+D;AAAA,IACjG;AACA,eAAW,WAAW,KAAK;AACvB,cAAQ,gBAAgB,QAAQ;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,MAAM,YAAY,QAAQ;AACtB,UAAM,KAAK,OAAO,MAAM,cAAc,KAAK,MAAM,UAAU,IAAI,GAAK,CAAC,IAAI,EAAE,OAAO,IAAI,IAAI,QAAQ,UAAU,MAAM,MAAM,eAAe,EAAE,UAAU,OAAO,SAAS,KAAK,MAAM,CAAC,IAAI,OAAO;AAC1L,QAAI,eAAe,OAAO,WAAW,SAAS,eAAe,EAAE;AAC/D,QAAI,CAAC,cAAc;AACf,qBAAe,SAAS,cAAc,KAAK;AAC3C,mBAAa,KAAK;AAClB,eAAS,KAAK,OAAO,YAAY;AAAA,IACrC;AACA,UAAM,iBAAiB,yBAAyB,SAAS,KAAK,GAAG,MAAM,KAAK,IAAI,GAAG,WAAW,IAAI,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AAC9H,QAAI,YAAY,GAAG;AACf,YAAM,MAAM,KAAK,QAAQ,QAAQ;AACjC,UAAI,OAAO,CAAC,IAAI,WAAW;AACvB,YAAI,QAAQ;AACZ,YAAI,OAAO,UAAU,CAAC;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,aAAa,QAAQ,YAAY,MAAM,UAAU;AACjD,iBAAW;AACX,eAAS,QAAQ,kBAAkB,IAAI;AAAA,IAC3C,OACK;AACD,YAAM,mBAAmB,aAAa,qBAAqB,QAAQ;AACnE,UAAI,iBAAiB,QAAQ;AACzB,mBAAW,iBAAiB,CAAC;AAC7B,iBAAS,QAAQ,kBAAkB,IAAI;AAAA,MAC3C,OACK;AACD,mBAAW,SAAS,cAAc,QAAQ;AAC1C,iBAAS,QAAQ,kBAAkB,IAAI;AACvC,qBAAa,YAAY,QAAQ;AAAA,MACrC;AAAA,IACJ;AACA,QAAI,CAAC,SAAS,MAAM,OAAO;AACvB,eAAS,MAAM,QAAQ;AAAA,IAC3B;AACA,QAAI,CAAC,SAAS,MAAM,QAAQ;AACxB,eAAS,MAAM,SAAS;AAAA,IAC5B;AACA,UAAM,UAAU,IAAI,UAAU,MAAM,IAAI,cAAc;AACtD,QAAI,YAAY,GAAG;AACf,UAAI,OAAO,UAAU,GAAG,OAAO;AAAA,IACnC,OACK;AACD,UAAI,KAAK,OAAO;AAAA,IACpB;AACA,YAAQ,OAAO,WAAW,QAAQ;AAClC,UAAM,QAAQ,MAAM;AACpB,WAAO;AAAA,EACX;AACJ;;;ACtRO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,MAAM;AACX,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACf,UAAM,aAAa,MAAM,OAAO,WAAW,WAAW,OAAO,MAAM;AACnE,QAAI,SAAS,MAAM,UAAa,SAAS,MAAM,UAAa,SAAS,MAAM,QAAW;AAClF,aAAO,SAAS,QAAQ;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,iBAAiB,OAAO;AACpB,UAAM,aAAa,MAAM,OAAO,WAAW,WAAW,OAAO,MAAM;AACnE,QAAI,SAAS,MAAM,UAAa,SAAS,MAAM,QAAW;AACtD,aAAO,SAAS;AAAA,QACZ,GAAG,cAAc,SAAS,CAAC;AAAA,QAC3B,GAAG,cAAc,SAAS,CAAC;AAAA,QAC3B,GAAG,cAAc,SAAS,CAAC;AAAA,MAC/B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,YAAY,OAAO;AACf,QAAI,CAAC,MAAM,WAAW,KAAK,GAAG;AAC1B;AAAA,IACJ;AACA,UAAM,QAAQ,sEAAsE,SAAS,MAAM,KAAK,KAAK;AAC7G,WAAO,SACD,WAAW;AAAA,MACT,GAAG,OAAO,SAAS,IAAI,WAAW,OAAO,CAAC,CAAC,IAAI;AAAA,MAC/C,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,MACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,MACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,IAC7B,CAAC,IACC;AAAA,EACV;AACJ;;;ACpCO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,MAAM;AACX,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACf,UAAM,aAAa,MAAM,OAAO,WAAW,WAAW,OAAO,MAAM;AACnE,QAAI,SAAS,MAAM,QAAW;AAC1B,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,iBAAiB,OAAO;AACpB,UAAM,aAAa,MAAM,OAAO,WAAW,WAAW,OAAO,MAAM;AACnE,QAAI,SAAS,MAAM,QAAW;AAC1B,aAAO;AAAA,QACH,GAAG,cAAc,SAAS,CAAC;AAAA,QAC3B,GAAG,cAAc,SAAS,CAAC;AAAA,QAC3B,GAAG,cAAc,SAAS,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,YAAY,OAAO;AACf,QAAI,CAAC,MAAM,WAAW,KAAK,YAAY,GAAG;AACtC;AAAA,IACJ;AACA,UAAM,QAAQ,oEAAoE,SAAS,MAAM,KAAK,KAAK;AAC3G,WAAO,SACD;AAAA,MACE,GAAG,OAAO,SAAS,IAAI,WAAW,OAAO,CAAC,CAAC,IAAI;AAAA,MAC/C,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,MACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,MACzB,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE;AAAA,IAC7B,IACE;AAAA,EACV;AACJ;;;AChCO,SAAS,OAAO;AACnB,QAAM,kBAAkB,IAAI,gBAAgB,GAAG,kBAAkB,IAAI,gBAAgB;AACrF,kBAAgB,eAAe;AAC/B,kBAAgB,eAAe;AAC/B,QAAM,SAAS,IAAI,OAAO;AAC1B,SAAO,KAAK;AACZ,SAAO;AACX;;;ACTA,IAAM,cAAc,KAAK;AACzB,IAAI,CAAC,MAAM,GAAG;AACV,SAAO,cAAc;AACzB;;;ACLA,IAAMC,YAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,QAAQ;AACtD,SAAS,YAAY,MAAM,MAAM,eAAe,MAAM,OAAO;AAChE,MAAI,CAACA,UAAS,IAAI,KAAK,CAACA,UAAS,IAAI,GAAG;AACpC,WAAO,SAAS;AAAA,EACpB;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI,EAAE,OAAO,SAAO,CAAC,aAAa,GAAG,CAAC,GAAG,QAAQ,OAAO,KAAK,IAAI,EAAE,OAAO,SAAO,CAAC,aAAa,GAAG,CAAC;AAC7H,MAAI,MAAM,WAAW,MAAM,QAAQ;AAC/B,WAAO;AAAA,EACX;AACA,aAAW,OAAO,OAAO;AACrB,UAAM,SAAS,KAAK,GAAG,GAAG,SAAS,KAAK,GAAG;AAC3C,QAAIA,UAAS,MAAM,KAAKA,UAAS,MAAM,GAAG;AACtC,UAAI,WAAW,QAAQ,WAAW,MAAM;AACpC;AAAA,MACJ;AACA,UAAI,CAAC,YAAY,QAAQ,QAAQ,YAAY,GAAG;AAC5C,eAAO;AAAA,MACX;AAAA,IACJ,WACS,MAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,GAAG;AACrD,UAAI,CAAC,kBAAkB,QAAQ,QAAQ,YAAY,GAAG;AAClD,eAAO;AAAA,MACX;AAAA,IACJ,WACS,WAAW,QAAQ;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,MAAM,cAAc;AACjD,MAAI,KAAK,WAAW,KAAK,QAAQ;AAC7B,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,UAAM,OAAO,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC;AACnC,QAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,IAAI,GAAG;AAC5C,UAAI,CAAC,kBAAkB,MAAM,MAAM,YAAY,GAAG;AAC9C,eAAO;AAAA,MACX;AAAA,IACJ,WACSA,UAAS,IAAI,KAAKA,UAAS,IAAI,GAAG;AACvC,UAAI,CAAC,YAAY,MAAM,MAAM,YAAY,GAAG;AACxC,eAAO;AAAA,MACX;AAAA,IACJ,WACS,SAAS,MAAM;AACpB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;A9EhDA,IAAM,YAAY;AAClB,IAAMC,aAAN,MAAM,mBAAkB,uBAAU;AAAA,EAC9B,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM,SAAS;AACrB;AAAA,IACJ;AACA,SAAK,MAAM,QAAQ,QAAQ;AAC3B,SAAK,SAAS;AAAA,MACV,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB,WAAW;AAC7B,UAAM,cAAc,UAAU,WAAW,UAAU,QAAQ,iBAAiB,KAAK,MAAM,WAAW,KAAK,MAAM;AAC7G,WAAQ,UAAU,QAAQ,KAAK,MAAM,OACjC,UAAU,OAAO,KAAK,MAAM,MAC5B,UAAU,oBAAoB,KAAK,MAAM,mBACzC,UAAU,cAAc,KAAK,MAAM,aACnC,UAAU,WAAW,KAAK,MAAM,UAChC,UAAU,UAAU,KAAK,MAAM,SAC/B,CAAC,YAAY,UAAU,OAAO,KAAK,MAAM,KAAK,KAC9C,UAAU,SAAS,KAAK,MAAM,QAC9B,UAAU,WAAW,KAAK,MAAM,UAChC,CAAC,YAAY,aAAa,gBAAgB,SAAO,IAAI,WAAW,GAAG,CAAC;AAAA,EAC5E;AAAA,EACA,qBAAqB;AACjB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,cAAc;AACV,SAAK,QAAQ,EAAE,KAAK,MAAM;AACtB,YAAM,YAAY;AAAA,IACtB,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,KAAC,YAAY;AACT,UAAI,KAAK,MAAM,MAAM;AACjB,cAAM,KAAK,MAAM,KAAK,WAAW;AAAA,MACrC;AACA,WAAK,SAAS;AAAA,QACV,MAAM;AAAA,MACV,GAAG,YAAY;AACX,cAAM,KAAK,cAAc;AAAA,MAC7B,CAAC;AAAA,IACL,GAAG;AAAA,EACP;AAAA,EACA,uBAAuB;AACnB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,QAAQ,WAAW,iBAAiB,GAAG,IAAI,KAAK;AAC/D,WAAQ,aAAAC,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,WAAsB,GAAO;AAAA,MAC9D,aAAAA,QAAM,cAAc,UAAU,EAAE,WAAW,iBAAiB,OAAO;AAAA,QAC3D,GAAG,KAAK,MAAM;AAAA,QACd;AAAA,QACA;AAAA,MACJ,EAAE,CAAC;AAAA,IAAC;AAAA,EAChB;AAAA,EACA,MAAM,UAAU;AACZ,SAAK,QAAQ;AACb,UAAM,KAAK,cAAc;AAAA,EAC7B;AAAA,EACA,MAAM,gBAAgB;AAClB,QAAI,CAAC,KAAK,MAAM,MAAM;AAClB;AAAA,IACJ;AACA,UAAM,KAAK,KAAK,MAAM,MAAM,WAAU,aAAa,MAAM,WAAW,YAAY,MAAM,YAAY,KAAK;AAAA,MACnG,KAAK,KAAK,MAAM;AAAA,MAChB;AAAA,MACA,SAAS,KAAK,MAAM,WAAW,KAAK,MAAM;AAAA,IAC9C,CAAC;AACD,QAAI,KAAK,MAAM,WAAW;AACtB,WAAK,MAAM,UAAU,UAAU;AAAA,IACnC;AACA,SAAK,SAAS;AAAA,MACV,SAAS;AAAA,IACb,CAAC;AACD,QAAI,KAAK,MAAM,QAAQ;AACnB,YAAM,KAAK,MAAM,OAAO,SAAS;AAAA,IACrC;AAAA,EACJ;AACJ;AACAD,WAAU,eAAe;AAAA,EACrB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,OAAO,CAAC;AAAA,EACR,KAAK;AAAA,EACL,IAAI;AACR;AACA,IAAO,oBAAQA;;;A+EjGf,IAAO,cAAQ;", "names": ["trail", "container", "theme", "container", "position", "capacity", "container", "init", "isObject", "Particles", "React"]}