{"version": 3, "sources": ["../../.pnpm/react-image-crop@11.0.10_react@18.3.1/node_modules/react-image-crop/dist/index.js"], "sourcesContent": ["var _ = Object.defineProperty;\nvar $ = (a, h, e) => h in a ? _(a, h, { enumerable: !0, configurable: !0, writable: !0, value: e }) : a[h] = e;\nvar m = (a, h, e) => $(a, typeof h != \"symbol\" ? h + \"\" : h, e);\nimport u, { PureComponent as K, createRef as P } from \"react\";\nconst E = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  unit: \"px\"\n}, b = (a, h, e) => Math.min(Math.max(a, h), e), H = (...a) => a.filter((h) => h && typeof h == \"string\").join(\" \"), X = (a, h) => a === h || a.width === h.width && a.height === h.height && a.x === h.x && a.y === h.y && a.unit === h.unit;\nfunction B(a, h, e, n) {\n  const t = D(a, e, n);\n  return a.width && (t.height = t.width / h), a.height && (t.width = t.height * h), t.y + t.height > n && (t.height = n - t.y, t.width = t.height * h), t.x + t.width > e && (t.width = e - t.x, t.height = t.width / h), a.unit === \"%\" ? v(t, e, n) : t;\n}\nfunction L(a, h, e) {\n  const n = D(a, h, e);\n  return n.x = (h - n.width) / 2, n.y = (e - n.height) / 2, a.unit === \"%\" ? v(n, h, e) : n;\n}\nfunction v(a, h, e) {\n  return a.unit === \"%\" ? { ...E, ...a, unit: \"%\" } : {\n    unit: \"%\",\n    x: a.x ? a.x / h * 100 : 0,\n    y: a.y ? a.y / e * 100 : 0,\n    width: a.width ? a.width / h * 100 : 0,\n    height: a.height ? a.height / e * 100 : 0\n  };\n}\nfunction D(a, h, e) {\n  return a.unit ? a.unit === \"px\" ? { ...E, ...a, unit: \"px\" } : {\n    unit: \"px\",\n    x: a.x ? a.x * h / 100 : 0,\n    y: a.y ? a.y * e / 100 : 0,\n    width: a.width ? a.width * h / 100 : 0,\n    height: a.height ? a.height * e / 100 : 0\n  } : { ...E, ...a, unit: \"px\" };\n}\nfunction k(a, h, e, n, t, d = 0, r = 0, o = n, w = t) {\n  const i = { ...a };\n  let s = Math.min(d, n), c = Math.min(r, t), g = Math.min(o, n), p = Math.min(w, t);\n  h && (h > 1 ? (s = r ? r * h : s, c = s / h, g = o * h) : (c = d ? d / h : c, s = c * h, p = w / h)), i.y < 0 && (i.height = Math.max(i.height + i.y, c), i.y = 0), i.x < 0 && (i.width = Math.max(i.width + i.x, s), i.x = 0);\n  const l = n - (i.x + i.width);\n  l < 0 && (i.x = Math.min(i.x, n - s), i.width += l);\n  const C = t - (i.y + i.height);\n  if (C < 0 && (i.y = Math.min(i.y, t - c), i.height += C), i.width < s && ((e === \"sw\" || e == \"nw\") && (i.x -= s - i.width), i.width = s), i.height < c && ((e === \"nw\" || e == \"ne\") && (i.y -= c - i.height), i.height = c), i.width > g && ((e === \"sw\" || e == \"nw\") && (i.x -= g - i.width), i.width = g), i.height > p && ((e === \"nw\" || e == \"ne\") && (i.y -= p - i.height), i.height = p), h) {\n    const y = i.width / i.height;\n    if (y < h) {\n      const f = Math.max(i.width / h, c);\n      (e === \"nw\" || e == \"ne\") && (i.y -= f - i.height), i.height = f;\n    } else if (y > h) {\n      const f = Math.max(i.height * h, s);\n      (e === \"sw\" || e == \"nw\") && (i.x -= f - i.width), i.width = f;\n    }\n  }\n  return i;\n}\nfunction I(a, h, e, n) {\n  const t = { ...a };\n  return h === \"ArrowLeft\" ? n === \"nw\" ? (t.x -= e, t.y -= e, t.width += e, t.height += e) : n === \"w\" ? (t.x -= e, t.width += e) : n === \"sw\" ? (t.x -= e, t.width += e, t.height += e) : n === \"ne\" ? (t.y += e, t.width -= e, t.height -= e) : n === \"e\" ? t.width -= e : n === \"se\" && (t.width -= e, t.height -= e) : h === \"ArrowRight\" && (n === \"nw\" ? (t.x += e, t.y += e, t.width -= e, t.height -= e) : n === \"w\" ? (t.x += e, t.width -= e) : n === \"sw\" ? (t.x += e, t.width -= e, t.height -= e) : n === \"ne\" ? (t.y -= e, t.width += e, t.height += e) : n === \"e\" ? t.width += e : n === \"se\" && (t.width += e, t.height += e)), h === \"ArrowUp\" ? n === \"nw\" ? (t.x -= e, t.y -= e, t.width += e, t.height += e) : n === \"n\" ? (t.y -= e, t.height += e) : n === \"ne\" ? (t.y -= e, t.width += e, t.height += e) : n === \"sw\" ? (t.x += e, t.width -= e, t.height -= e) : n === \"s\" ? t.height -= e : n === \"se\" && (t.width -= e, t.height -= e) : h === \"ArrowDown\" && (n === \"nw\" ? (t.x += e, t.y += e, t.width -= e, t.height -= e) : n === \"n\" ? (t.y += e, t.height -= e) : n === \"ne\" ? (t.y += e, t.width -= e, t.height -= e) : n === \"sw\" ? (t.x -= e, t.width += e, t.height += e) : n === \"s\" ? t.height += e : n === \"se\" && (t.width += e, t.height += e)), t;\n}\nconst M = { capture: !0, passive: !1 };\nlet N = 0;\nconst x = class x extends K {\n  constructor() {\n    super(...arguments);\n    m(this, \"docMoveBound\", !1);\n    m(this, \"mouseDownOnCrop\", !1);\n    m(this, \"dragStarted\", !1);\n    m(this, \"evData\", {\n      startClientX: 0,\n      startClientY: 0,\n      startCropX: 0,\n      startCropY: 0,\n      clientX: 0,\n      clientY: 0,\n      isResize: !0\n    });\n    m(this, \"componentRef\", P());\n    m(this, \"mediaRef\", P());\n    m(this, \"resizeObserver\");\n    m(this, \"initChangeCalled\", !1);\n    m(this, \"instanceId\", `rc-${N++}`);\n    m(this, \"state\", {\n      cropIsActive: !1,\n      newCropIsBeingDrawn: !1\n    });\n    m(this, \"onCropPointerDown\", (e) => {\n      const { crop: n, disabled: t } = this.props, d = this.getBox();\n      if (!n)\n        return;\n      const r = D(n, d.width, d.height);\n      if (t)\n        return;\n      e.cancelable && e.preventDefault(), this.bindDocMove(), this.componentRef.current.focus({ preventScroll: !0 });\n      const o = e.target.dataset.ord, w = !!o;\n      let i = e.clientX, s = e.clientY, c = r.x, g = r.y;\n      if (o) {\n        const p = e.clientX - d.x, l = e.clientY - d.y;\n        let C = 0, y = 0;\n        o === \"ne\" || o == \"e\" ? (C = p - (r.x + r.width), y = l - r.y, c = r.x, g = r.y + r.height) : o === \"se\" || o === \"s\" ? (C = p - (r.x + r.width), y = l - (r.y + r.height), c = r.x, g = r.y) : o === \"sw\" || o == \"w\" ? (C = p - r.x, y = l - (r.y + r.height), c = r.x + r.width, g = r.y) : (o === \"nw\" || o == \"n\") && (C = p - r.x, y = l - r.y, c = r.x + r.width, g = r.y + r.height), i = c + d.x + C, s = g + d.y + y;\n      }\n      this.evData = {\n        startClientX: i,\n        startClientY: s,\n        startCropX: c,\n        startCropY: g,\n        clientX: e.clientX,\n        clientY: e.clientY,\n        isResize: w,\n        ord: o\n      }, this.mouseDownOnCrop = !0, this.setState({ cropIsActive: !0 });\n    });\n    m(this, \"onComponentPointerDown\", (e) => {\n      const { crop: n, disabled: t, locked: d, keepSelection: r, onChange: o } = this.props, w = this.getBox();\n      if (t || d || r && n)\n        return;\n      e.cancelable && e.preventDefault(), this.bindDocMove(), this.componentRef.current.focus({ preventScroll: !0 });\n      const i = e.clientX - w.x, s = e.clientY - w.y, c = {\n        unit: \"px\",\n        x: i,\n        y: s,\n        width: 0,\n        height: 0\n      };\n      this.evData = {\n        startClientX: e.clientX,\n        startClientY: e.clientY,\n        startCropX: i,\n        startCropY: s,\n        clientX: e.clientX,\n        clientY: e.clientY,\n        isResize: !0\n      }, this.mouseDownOnCrop = !0, o(D(c, w.width, w.height), v(c, w.width, w.height)), this.setState({ cropIsActive: !0, newCropIsBeingDrawn: !0 });\n    });\n    m(this, \"onDocPointerMove\", (e) => {\n      const { crop: n, disabled: t, onChange: d, onDragStart: r } = this.props, o = this.getBox();\n      if (t || !n || !this.mouseDownOnCrop)\n        return;\n      e.cancelable && e.preventDefault(), this.dragStarted || (this.dragStarted = !0, r && r(e));\n      const { evData: w } = this;\n      w.clientX = e.clientX, w.clientY = e.clientY;\n      let i;\n      w.isResize ? i = this.resizeCrop() : i = this.dragCrop(), X(n, i) || d(\n        D(i, o.width, o.height),\n        v(i, o.width, o.height)\n      );\n    });\n    m(this, \"onComponentKeyDown\", (e) => {\n      const { crop: n, disabled: t, onChange: d, onComplete: r } = this.props;\n      if (t)\n        return;\n      const o = e.key;\n      let w = !1;\n      if (!n)\n        return;\n      const i = this.getBox(), s = this.makePixelCrop(i), g = (navigator.platform.match(\"Mac\") ? e.metaKey : e.ctrlKey) ? x.nudgeStepLarge : e.shiftKey ? x.nudgeStepMedium : x.nudgeStep;\n      if (o === \"ArrowLeft\" ? (s.x -= g, w = !0) : o === \"ArrowRight\" ? (s.x += g, w = !0) : o === \"ArrowUp\" ? (s.y -= g, w = !0) : o === \"ArrowDown\" && (s.y += g, w = !0), w) {\n        e.cancelable && e.preventDefault(), s.x = b(s.x, 0, i.width - s.width), s.y = b(s.y, 0, i.height - s.height);\n        const p = D(s, i.width, i.height), l = v(s, i.width, i.height);\n        d(p, l), r && r(p, l);\n      }\n    });\n    m(this, \"onHandlerKeyDown\", (e, n) => {\n      const {\n        aspect: t = 0,\n        crop: d,\n        disabled: r,\n        minWidth: o = 0,\n        minHeight: w = 0,\n        maxWidth: i,\n        maxHeight: s,\n        onChange: c,\n        onComplete: g\n      } = this.props, p = this.getBox();\n      if (r || !d)\n        return;\n      if (e.key === \"ArrowUp\" || e.key === \"ArrowDown\" || e.key === \"ArrowLeft\" || e.key === \"ArrowRight\")\n        e.stopPropagation(), e.preventDefault();\n      else\n        return;\n      const C = (navigator.platform.match(\"Mac\") ? e.metaKey : e.ctrlKey) ? x.nudgeStepLarge : e.shiftKey ? x.nudgeStepMedium : x.nudgeStep, y = D(d, p.width, p.height), f = I(y, e.key, C, n), R = k(\n        f,\n        t,\n        n,\n        p.width,\n        p.height,\n        o,\n        w,\n        i,\n        s\n      );\n      if (!X(d, R)) {\n        const Y = v(R, p.width, p.height);\n        c(R, Y), g && g(R, Y);\n      }\n    });\n    m(this, \"onDocPointerDone\", (e) => {\n      const { crop: n, disabled: t, onComplete: d, onDragEnd: r } = this.props, o = this.getBox();\n      this.unbindDocMove(), !(t || !n) && this.mouseDownOnCrop && (this.mouseDownOnCrop = !1, this.dragStarted = !1, r && r(e), d && d(D(n, o.width, o.height), v(n, o.width, o.height)), this.setState({ cropIsActive: !1, newCropIsBeingDrawn: !1 }));\n    });\n    m(this, \"onDragFocus\", () => {\n      var e;\n      (e = this.componentRef.current) == null || e.scrollTo(0, 0);\n    });\n  }\n  get document() {\n    return document;\n  }\n  // We unfortunately get the bounding box every time as x+y changes\n  // due to scrolling.\n  getBox() {\n    const e = this.mediaRef.current;\n    if (!e)\n      return { x: 0, y: 0, width: 0, height: 0 };\n    const { x: n, y: t, width: d, height: r } = e.getBoundingClientRect();\n    return { x: n, y: t, width: d, height: r };\n  }\n  componentDidUpdate(e) {\n    const { crop: n, onComplete: t } = this.props;\n    if (t && !e.crop && n) {\n      const { width: d, height: r } = this.getBox();\n      d && r && t(D(n, d, r), v(n, d, r));\n    }\n  }\n  componentWillUnmount() {\n    this.resizeObserver && this.resizeObserver.disconnect(), this.unbindDocMove();\n  }\n  bindDocMove() {\n    this.docMoveBound || (this.document.addEventListener(\"pointermove\", this.onDocPointerMove, M), this.document.addEventListener(\"pointerup\", this.onDocPointerDone, M), this.document.addEventListener(\"pointercancel\", this.onDocPointerDone, M), this.docMoveBound = !0);\n  }\n  unbindDocMove() {\n    this.docMoveBound && (this.document.removeEventListener(\"pointermove\", this.onDocPointerMove, M), this.document.removeEventListener(\"pointerup\", this.onDocPointerDone, M), this.document.removeEventListener(\"pointercancel\", this.onDocPointerDone, M), this.docMoveBound = !1);\n  }\n  getCropStyle() {\n    const { crop: e } = this.props;\n    if (e)\n      return {\n        top: `${e.y}${e.unit}`,\n        left: `${e.x}${e.unit}`,\n        width: `${e.width}${e.unit}`,\n        height: `${e.height}${e.unit}`\n      };\n  }\n  dragCrop() {\n    const { evData: e } = this, n = this.getBox(), t = this.makePixelCrop(n), d = e.clientX - e.startClientX, r = e.clientY - e.startClientY;\n    return t.x = b(e.startCropX + d, 0, n.width - t.width), t.y = b(e.startCropY + r, 0, n.height - t.height), t;\n  }\n  getPointRegion(e, n, t, d) {\n    const { evData: r } = this, o = r.clientX - e.x, w = r.clientY - e.y;\n    let i;\n    d && n ? i = n === \"nw\" || n === \"n\" || n === \"ne\" : i = w < r.startCropY;\n    let s;\n    return t && n ? s = n === \"nw\" || n === \"w\" || n === \"sw\" : s = o < r.startCropX, s ? i ? \"nw\" : \"sw\" : i ? \"ne\" : \"se\";\n  }\n  resolveMinDimensions(e, n, t = 0, d = 0) {\n    const r = Math.min(t, e.width), o = Math.min(d, e.height);\n    return !n || !r && !o ? [r, o] : n > 1 ? r ? [r, r / n] : [o * n, o] : o ? [o * n, o] : [r, r / n];\n  }\n  resizeCrop() {\n    const { evData: e } = this, { aspect: n = 0, maxWidth: t, maxHeight: d } = this.props, r = this.getBox(), [o, w] = this.resolveMinDimensions(r, n, this.props.minWidth, this.props.minHeight);\n    let i = this.makePixelCrop(r);\n    const s = this.getPointRegion(r, e.ord, o, w), c = e.ord || s;\n    let g = e.clientX - e.startClientX, p = e.clientY - e.startClientY;\n    (o && c === \"nw\" || c === \"w\" || c === \"sw\") && (g = Math.min(g, -o)), (w && c === \"nw\" || c === \"n\" || c === \"ne\") && (p = Math.min(p, -w));\n    const l = {\n      unit: \"px\",\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    s === \"ne\" ? (l.x = e.startCropX, l.width = g, n ? (l.height = l.width / n, l.y = e.startCropY - l.height) : (l.height = Math.abs(p), l.y = e.startCropY - l.height)) : s === \"se\" ? (l.x = e.startCropX, l.y = e.startCropY, l.width = g, n ? l.height = l.width / n : l.height = p) : s === \"sw\" ? (l.x = e.startCropX + g, l.y = e.startCropY, l.width = Math.abs(g), n ? l.height = l.width / n : l.height = p) : s === \"nw\" && (l.x = e.startCropX + g, l.width = Math.abs(g), n ? (l.height = l.width / n, l.y = e.startCropY - l.height) : (l.height = Math.abs(p), l.y = e.startCropY + p));\n    const C = k(\n      l,\n      n,\n      s,\n      r.width,\n      r.height,\n      o,\n      w,\n      t,\n      d\n    );\n    return n || x.xyOrds.indexOf(c) > -1 ? i = C : x.xOrds.indexOf(c) > -1 ? (i.x = C.x, i.width = C.width) : x.yOrds.indexOf(c) > -1 && (i.y = C.y, i.height = C.height), i.x = b(i.x, 0, r.width - i.width), i.y = b(i.y, 0, r.height - i.height), i;\n  }\n  renderCropSelection() {\n    const {\n      ariaLabels: e = x.defaultProps.ariaLabels,\n      disabled: n,\n      locked: t,\n      renderSelectionAddon: d,\n      ruleOfThirds: r,\n      crop: o\n    } = this.props, w = this.getCropStyle();\n    if (o)\n      return /* @__PURE__ */ u.createElement(\n        \"div\",\n        {\n          style: w,\n          className: \"ReactCrop__crop-selection\",\n          onPointerDown: this.onCropPointerDown,\n          \"aria-label\": e.cropArea,\n          tabIndex: 0,\n          onKeyDown: this.onComponentKeyDown,\n          role: \"group\"\n        },\n        !n && !t && /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__drag-elements\", onFocus: this.onDragFocus }, /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-n\", \"data-ord\": \"n\" }), /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-e\", \"data-ord\": \"e\" }), /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-s\", \"data-ord\": \"s\" }), /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-w\", \"data-ord\": \"w\" }), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-nw\",\n            \"data-ord\": \"nw\",\n            tabIndex: 0,\n            \"aria-label\": e.nwDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"nw\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-n\",\n            \"data-ord\": \"n\",\n            tabIndex: 0,\n            \"aria-label\": e.nDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"n\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-ne\",\n            \"data-ord\": \"ne\",\n            tabIndex: 0,\n            \"aria-label\": e.neDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"ne\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-e\",\n            \"data-ord\": \"e\",\n            tabIndex: 0,\n            \"aria-label\": e.eDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"e\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-se\",\n            \"data-ord\": \"se\",\n            tabIndex: 0,\n            \"aria-label\": e.seDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"se\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-s\",\n            \"data-ord\": \"s\",\n            tabIndex: 0,\n            \"aria-label\": e.sDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"s\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-sw\",\n            \"data-ord\": \"sw\",\n            tabIndex: 0,\n            \"aria-label\": e.swDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"sw\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ u.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-w\",\n            \"data-ord\": \"w\",\n            tabIndex: 0,\n            \"aria-label\": e.wDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"w\"),\n            role: \"button\"\n          }\n        )),\n        d && /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__selection-addon\", onPointerDown: (i) => i.stopPropagation() }, d(this.state)),\n        r && /* @__PURE__ */ u.createElement(u.Fragment, null, /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__rule-of-thirds-hz\" }), /* @__PURE__ */ u.createElement(\"div\", { className: \"ReactCrop__rule-of-thirds-vt\" }))\n      );\n  }\n  makePixelCrop(e) {\n    const n = { ...E, ...this.props.crop || {} };\n    return D(n, e.width, e.height);\n  }\n  render() {\n    const { aspect: e, children: n, circularCrop: t, className: d, crop: r, disabled: o, locked: w, style: i, ruleOfThirds: s } = this.props, { cropIsActive: c, newCropIsBeingDrawn: g } = this.state, p = r ? this.renderCropSelection() : null, l = H(\n      \"ReactCrop\",\n      d,\n      c && \"ReactCrop--active\",\n      o && \"ReactCrop--disabled\",\n      w && \"ReactCrop--locked\",\n      g && \"ReactCrop--new-crop\",\n      r && e && \"ReactCrop--fixed-aspect\",\n      r && t && \"ReactCrop--circular-crop\",\n      r && s && \"ReactCrop--rule-of-thirds\",\n      !this.dragStarted && r && !r.width && !r.height && \"ReactCrop--invisible-crop\",\n      t && \"ReactCrop--no-animate\"\n    );\n    return /* @__PURE__ */ u.createElement(\"div\", { ref: this.componentRef, className: l, style: i }, /* @__PURE__ */ u.createElement(\"div\", { ref: this.mediaRef, className: \"ReactCrop__child-wrapper\", onPointerDown: this.onComponentPointerDown }, n), r ? /* @__PURE__ */ u.createElement(\"svg\", { className: \"ReactCrop__crop-mask\", width: \"100%\", height: \"100%\" }, /* @__PURE__ */ u.createElement(\"defs\", null, /* @__PURE__ */ u.createElement(\"mask\", { id: `hole-${this.instanceId}` }, /* @__PURE__ */ u.createElement(\"rect\", { width: \"100%\", height: \"100%\", fill: \"white\" }), t ? /* @__PURE__ */ u.createElement(\n      \"ellipse\",\n      {\n        cx: `${r.x + r.width / 2}${r.unit}`,\n        cy: `${r.y + r.height / 2}${r.unit}`,\n        rx: `${r.width / 2}${r.unit}`,\n        ry: `${r.height / 2}${r.unit}`,\n        fill: \"black\"\n      }\n    ) : /* @__PURE__ */ u.createElement(\n      \"rect\",\n      {\n        x: `${r.x}${r.unit}`,\n        y: `${r.y}${r.unit}`,\n        width: `${r.width}${r.unit}`,\n        height: `${r.height}${r.unit}`,\n        fill: \"black\"\n      }\n    ))), /* @__PURE__ */ u.createElement(\"rect\", { fill: \"black\", fillOpacity: 0.5, width: \"100%\", height: \"100%\", mask: `url(#hole-${this.instanceId})` })) : void 0, p);\n  }\n};\nm(x, \"xOrds\", [\"e\", \"w\"]), m(x, \"yOrds\", [\"n\", \"s\"]), m(x, \"xyOrds\", [\"nw\", \"ne\", \"se\", \"sw\"]), m(x, \"nudgeStep\", 1), m(x, \"nudgeStepMedium\", 10), m(x, \"nudgeStepLarge\", 100), m(x, \"defaultProps\", {\n  ariaLabels: {\n    cropArea: \"Use the arrow keys to move the crop selection area\",\n    nwDragHandle: \"Use the arrow keys to move the north west drag handle to change the crop selection area\",\n    nDragHandle: \"Use the up and down arrow keys to move the north drag handle to change the crop selection area\",\n    neDragHandle: \"Use the arrow keys to move the north east drag handle to change the crop selection area\",\n    eDragHandle: \"Use the up and down arrow keys to move the east drag handle to change the crop selection area\",\n    seDragHandle: \"Use the arrow keys to move the south east drag handle to change the crop selection area\",\n    sDragHandle: \"Use the up and down arrow keys to move the south drag handle to change the crop selection area\",\n    swDragHandle: \"Use the arrow keys to move the south west drag handle to change the crop selection area\",\n    wDragHandle: \"Use the up and down arrow keys to move the west drag handle to change the crop selection area\"\n  }\n});\nlet S = x;\nexport {\n  S as Component,\n  S as ReactCrop,\n  X as areCropsEqual,\n  L as centerCrop,\n  b as clamp,\n  H as cls,\n  k as containCrop,\n  v as convertToPercentCrop,\n  D as convertToPixelCrop,\n  S as default,\n  E as defaultCrop,\n  B as makeAspectCrop,\n  I as nudgeCrop\n};\n"], "mappings": ";;;;;;;;AAGA,mBAAsD;AAHtD,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC7G,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC;AAE9D,IAAM,IAAI;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AANA,IAMG,IAAI,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAN9C,IAMiD,IAAI,IAAI,MAAM,EAAE,OAAO,CAAC,MAAM,KAAK,OAAO,KAAK,QAAQ,EAAE,KAAK,GAAG;AANlH,IAMqH,IAAI,CAAC,GAAG,MAAM,MAAM,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;AACzO,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,QAAM,IAAI,EAAE,GAAG,GAAG,CAAC;AACnB,SAAO,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ,MAAM,EAAE,QAAQ,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,SAAS,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI;AACxP;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,QAAM,IAAI,EAAE,GAAG,GAAG,CAAC;AACnB,SAAO,EAAE,KAAK,IAAI,EAAE,SAAS,GAAG,EAAE,KAAK,IAAI,EAAE,UAAU,GAAG,EAAE,SAAS,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI;AAC1F;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,EAAE,SAAS,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,IAAI,IAAI;AAAA,IAClD,MAAM;AAAA,IACN,GAAG,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM;AAAA,IACzB,GAAG,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM;AAAA,IACzB,OAAO,EAAE,QAAQ,EAAE,QAAQ,IAAI,MAAM;AAAA,IACrC,QAAQ,EAAE,SAAS,EAAE,SAAS,IAAI,MAAM;AAAA,EAC1C;AACF;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,EAAE,OAAO,EAAE,SAAS,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,KAAK,IAAI;AAAA,IAC7D,MAAM;AAAA,IACN,GAAG,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM;AAAA,IACzB,GAAG,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM;AAAA,IACzB,OAAO,EAAE,QAAQ,EAAE,QAAQ,IAAI,MAAM;AAAA,IACrC,QAAQ,EAAE,SAAS,EAAE,SAAS,IAAI,MAAM;AAAA,EAC1C,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,KAAK;AAC/B;AACA,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG;AACpD,QAAM,IAAI,EAAE,GAAG,EAAE;AACjB,MAAI,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC;AACjF,QAAM,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAE,IAAI,MAAM,EAAE,SAAS,KAAK,IAAI,EAAE,SAAS,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,IAAI,EAAE,IAAI,MAAM,EAAE,QAAQ,KAAK,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI;AAC5N,QAAM,IAAI,KAAK,EAAE,IAAI,EAAE;AACvB,MAAI,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,SAAS;AACjD,QAAM,IAAI,KAAK,EAAE,IAAI,EAAE;AACvB,MAAI,IAAI,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,UAAU,IAAI,EAAE,QAAQ,OAAO,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,SAAS,OAAO,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,QAAQ,OAAO,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,SAAS,OAAO,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,SAAS,EAAE,SAAS,IAAI,GAAG;AACrY,UAAM,IAAI,EAAE,QAAQ,EAAE;AACtB,QAAI,IAAI,GAAG;AACT,YAAM,IAAI,KAAK,IAAI,EAAE,QAAQ,GAAG,CAAC;AACjC,OAAC,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,SAAS,EAAE,SAAS;AAAA,IACjE,WAAW,IAAI,GAAG;AAChB,YAAM,IAAI,KAAK,IAAI,EAAE,SAAS,GAAG,CAAC;AAClC,OAAC,MAAM,QAAQ,KAAK,UAAU,EAAE,KAAK,IAAI,EAAE,QAAQ,EAAE,QAAQ;AAAA,IAC/D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,QAAM,IAAI,EAAE,GAAG,EAAE;AACjB,SAAO,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,EAAE,SAAS,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,MAAM,SAAS,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,iBAAiB,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,EAAE,SAAS,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,MAAM,SAAS,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,YAAY,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,MAAM,EAAE,UAAU,IAAI,MAAM,SAAS,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,gBAAgB,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,OAAO,EAAE,KAAK,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,MAAM,EAAE,UAAU,IAAI,MAAM,SAAS,EAAE,SAAS,GAAG,EAAE,UAAU,KAAK;AAC5tC;AACA,IAAM,IAAI,EAAE,SAAS,MAAI,SAAS,MAAG;AACrC,IAAI,IAAI;AACR,IAAM,IAAI,MAAMA,WAAU,aAAAC,cAAE;AAAA,EAC1B,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,MAAE,MAAM,gBAAgB,KAAE;AAC1B,MAAE,MAAM,mBAAmB,KAAE;AAC7B,MAAE,MAAM,eAAe,KAAE;AACzB,MAAE,MAAM,UAAU;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,MAAE,MAAM,oBAAgB,aAAAC,WAAE,CAAC;AAC3B,MAAE,MAAM,gBAAY,aAAAA,WAAE,CAAC;AACvB,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,oBAAoB,KAAE;AAC9B,MAAE,MAAM,cAAc,MAAM,GAAG,EAAE;AACjC,MAAE,MAAM,SAAS;AAAA,MACf,cAAc;AAAA,MACd,qBAAqB;AAAA,IACvB,CAAC;AACD,MAAE,MAAM,qBAAqB,CAAC,MAAM;AAClC,YAAM,EAAE,MAAM,GAAG,UAAU,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AAC7D,UAAI,CAAC;AACH;AACF,YAAM,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAChC,UAAI;AACF;AACF,QAAE,cAAc,EAAE,eAAe,GAAG,KAAK,YAAY,GAAG,KAAK,aAAa,QAAQ,MAAM,EAAE,eAAe,KAAG,CAAC;AAC7G,YAAM,IAAI,EAAE,OAAO,QAAQ,KAAK,IAAI,CAAC,CAAC;AACtC,UAAI,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,IAAI,EAAE;AACjD,UAAI,GAAG;AACL,cAAM,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE;AAC7C,YAAI,IAAI,GAAG,IAAI;AACf,cAAM,QAAQ,KAAK,OAAO,IAAI,KAAK,EAAE,IAAI,EAAE,QAAQ,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,UAAU,MAAM,QAAQ,MAAM,OAAO,IAAI,KAAK,EAAE,IAAI,EAAE,QAAQ,IAAI,KAAK,EAAE,IAAI,EAAE,SAAS,IAAI,EAAE,GAAG,IAAI,EAAE,KAAK,MAAM,QAAQ,KAAK,OAAO,IAAI,IAAI,EAAE,GAAG,IAAI,KAAK,EAAE,IAAI,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,MAAM,MAAM,QAAQ,KAAK,SAAS,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,OAAO,IAAI,EAAE,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,IAAI;AAAA,MACha;AACA,WAAK,SAAS;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS,EAAE;AAAA,QACX,SAAS,EAAE;AAAA,QACX,UAAU;AAAA,QACV,KAAK;AAAA,MACP,GAAG,KAAK,kBAAkB,MAAI,KAAK,SAAS,EAAE,cAAc,KAAG,CAAC;AAAA,IAClE,CAAC;AACD,MAAE,MAAM,0BAA0B,CAAC,MAAM;AACvC,YAAM,EAAE,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,eAAe,GAAG,UAAU,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AACvG,UAAI,KAAK,KAAK,KAAK;AACjB;AACF,QAAE,cAAc,EAAE,eAAe,GAAG,KAAK,YAAY,GAAG,KAAK,aAAa,QAAQ,MAAM,EAAE,eAAe,KAAG,CAAC;AAC7G,YAAM,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI;AAAA,QAClD,MAAM;AAAA,QACN,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,WAAK,SAAS;AAAA,QACZ,cAAc,EAAE;AAAA,QAChB,cAAc,EAAE;AAAA,QAChB,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS,EAAE;AAAA,QACX,SAAS,EAAE;AAAA,QACX,UAAU;AAAA,MACZ,GAAG,KAAK,kBAAkB,MAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,cAAc,MAAI,qBAAqB,KAAG,CAAC;AAAA,IAChJ,CAAC;AACD,MAAE,MAAM,oBAAoB,CAAC,MAAM;AACjC,YAAM,EAAE,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AAC1F,UAAI,KAAK,CAAC,KAAK,CAAC,KAAK;AACnB;AACF,QAAE,cAAc,EAAE,eAAe,GAAG,KAAK,gBAAgB,KAAK,cAAc,MAAI,KAAK,EAAE,CAAC;AACxF,YAAM,EAAE,QAAQ,EAAE,IAAI;AACtB,QAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE;AACrC,UAAI;AACJ,QAAE,WAAW,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC,KAAK;AAAA,QACnE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAAA,QACtB,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAAA,MACxB;AAAA,IACF,CAAC;AACD,MAAE,MAAM,sBAAsB,CAAC,MAAM;AACnC,YAAM,EAAE,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,YAAY,EAAE,IAAI,KAAK;AAClE,UAAI;AACF;AACF,YAAM,IAAI,EAAE;AACZ,UAAI,IAAI;AACR,UAAI,CAAC;AACH;AACF,YAAM,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,cAAc,CAAC,GAAG,KAAK,UAAU,SAAS,MAAM,KAAK,IAAI,EAAE,UAAU,EAAE,WAAWF,GAAE,iBAAiB,EAAE,WAAWA,GAAE,kBAAkBA,GAAE;AAC1K,UAAI,MAAM,eAAe,EAAE,KAAK,GAAG,IAAI,QAAM,MAAM,gBAAgB,EAAE,KAAK,GAAG,IAAI,QAAM,MAAM,aAAa,EAAE,KAAK,GAAG,IAAI,QAAM,MAAM,gBAAgB,EAAE,KAAK,GAAG,IAAI,OAAK,GAAG;AACxK,UAAE,cAAc,EAAE,eAAe,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,EAAE,SAAS,EAAE,MAAM;AAC3G,cAAM,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAC7D,UAAE,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,CAAC;AACD,MAAE,MAAM,oBAAoB,CAAC,GAAG,MAAM;AACpC,YAAM;AAAA,QACJ,QAAQ,IAAI;AAAA,QACZ,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU,IAAI;AAAA,QACd,WAAW,IAAI;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,MACd,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AAChC,UAAI,KAAK,CAAC;AACR;AACF,UAAI,EAAE,QAAQ,aAAa,EAAE,QAAQ,eAAe,EAAE,QAAQ,eAAe,EAAE,QAAQ;AACrF,UAAE,gBAAgB,GAAG,EAAE,eAAe;AAAA;AAEtC;AACF,YAAM,KAAK,UAAU,SAAS,MAAM,KAAK,IAAI,EAAE,UAAU,EAAE,WAAWA,GAAE,iBAAiB,EAAE,WAAWA,GAAE,kBAAkBA,GAAE,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,IAAI;AAAA,QAC7L;AAAA,QACA;AAAA,QACA;AAAA,QACA,EAAE;AAAA,QACF,EAAE;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,UAAI,CAAC,EAAE,GAAG,CAAC,GAAG;AACZ,cAAM,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAChC,UAAE,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,CAAC;AACD,MAAE,MAAM,oBAAoB,CAAC,MAAM;AACjC,YAAM,EAAE,MAAM,GAAG,UAAU,GAAG,YAAY,GAAG,WAAW,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO;AAC1F,WAAK,cAAc,GAAG,EAAE,KAAK,CAAC,MAAM,KAAK,oBAAoB,KAAK,kBAAkB,OAAI,KAAK,cAAc,OAAI,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,cAAc,OAAI,qBAAqB,MAAG,CAAC;AAAA,IACjP,CAAC;AACD,MAAE,MAAM,eAAe,MAAM;AAC3B,UAAI;AACJ,OAAC,IAAI,KAAK,aAAa,YAAY,QAAQ,EAAE,SAAS,GAAG,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AAAA,EACA,IAAI,WAAW;AACb,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,SAAS;AACP,UAAM,IAAI,KAAK,SAAS;AACxB,QAAI,CAAC;AACH,aAAO,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAC3C,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE,IAAI,EAAE,sBAAsB;AACpE,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAAA,EAC3C;AAAA,EACA,mBAAmB,GAAG;AACpB,UAAM,EAAE,MAAM,GAAG,YAAY,EAAE,IAAI,KAAK;AACxC,QAAI,KAAK,CAAC,EAAE,QAAQ,GAAG;AACrB,YAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,IAAI,KAAK,OAAO;AAC5C,WAAK,KAAK,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;AAAA,IACpC;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,SAAK,kBAAkB,KAAK,eAAe,WAAW,GAAG,KAAK,cAAc;AAAA,EAC9E;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,KAAK,SAAS,iBAAiB,eAAe,KAAK,kBAAkB,CAAC,GAAG,KAAK,SAAS,iBAAiB,aAAa,KAAK,kBAAkB,CAAC,GAAG,KAAK,SAAS,iBAAiB,iBAAiB,KAAK,kBAAkB,CAAC,GAAG,KAAK,eAAe;AAAA,EACvQ;AAAA,EACA,gBAAgB;AACd,SAAK,iBAAiB,KAAK,SAAS,oBAAoB,eAAe,KAAK,kBAAkB,CAAC,GAAG,KAAK,SAAS,oBAAoB,aAAa,KAAK,kBAAkB,CAAC,GAAG,KAAK,SAAS,oBAAoB,iBAAiB,KAAK,kBAAkB,CAAC,GAAG,KAAK,eAAe;AAAA,EAChR;AAAA,EACA,eAAe;AACb,UAAM,EAAE,MAAM,EAAE,IAAI,KAAK;AACzB,QAAI;AACF,aAAO;AAAA,QACL,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAAA,QACpB,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAAA,QACrB,OAAO,GAAG,EAAE,KAAK,GAAG,EAAE,IAAI;AAAA,QAC1B,QAAQ,GAAG,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,MAC9B;AAAA,EACJ;AAAA,EACA,WAAW;AACT,UAAM,EAAE,QAAQ,EAAE,IAAI,MAAM,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,cAAc,CAAC,GAAG,IAAI,EAAE,UAAU,EAAE,cAAc,IAAI,EAAE,UAAU,EAAE;AAC5H,WAAO,EAAE,IAAI,EAAE,EAAE,aAAa,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,aAAa,GAAG,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG;AAAA,EAC7G;AAAA,EACA,eAAe,GAAG,GAAG,GAAG,GAAG;AACzB,UAAM,EAAE,QAAQ,EAAE,IAAI,MAAM,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,UAAU,EAAE;AACnE,QAAI;AACJ,SAAK,IAAI,IAAI,MAAM,QAAQ,MAAM,OAAO,MAAM,OAAO,IAAI,IAAI,EAAE;AAC/D,QAAI;AACJ,WAAO,KAAK,IAAI,IAAI,MAAM,QAAQ,MAAM,OAAO,MAAM,OAAO,IAAI,IAAI,EAAE,YAAY,IAAI,IAAI,OAAO,OAAO,IAAI,OAAO;AAAA,EACrH;AAAA,EACA,qBAAqB,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG;AACvC,UAAM,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,MAAM;AACxD,WAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAAA,EACnG;AAAA,EACA,aAAa;AACX,UAAM,EAAE,QAAQ,EAAE,IAAI,MAAM,EAAE,QAAQ,IAAI,GAAG,UAAU,GAAG,WAAW,EAAE,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,KAAK,qBAAqB,GAAG,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,SAAS;AAC5L,QAAI,IAAI,KAAK,cAAc,CAAC;AAC5B,UAAM,IAAI,KAAK,eAAe,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,IAAI,EAAE,OAAO;AAC5D,QAAI,IAAI,EAAE,UAAU,EAAE,cAAc,IAAI,EAAE,UAAU,EAAE;AACtD,KAAC,KAAK,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,KAAK,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAC1I,UAAM,IAAI;AAAA,MACR,MAAM;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AACA,UAAM,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,GAAG,KAAK,EAAE,SAAS,EAAE,QAAQ,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,MAAM,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,GAAG,IAAI,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,SAAS,KAAK,MAAM,QAAQ,EAAE,IAAI,EAAE,aAAa,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,SAAS,KAAK,MAAM,SAAS,EAAE,IAAI,EAAE,aAAa,GAAG,EAAE,QAAQ,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,QAAQ,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,KAAK,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,aAAa;AAChkB,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA,EAAE;AAAA,MACF,EAAE;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,KAAKA,GAAE,OAAO,QAAQ,CAAC,IAAI,KAAK,IAAI,IAAIA,GAAE,MAAM,QAAQ,CAAC,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,SAASA,GAAE,MAAM,QAAQ,CAAC,IAAI,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,EAAE,SAAS,EAAE,MAAM,GAAG;AAAA,EACnP;AAAA,EACA,sBAAsB;AACpB,UAAM;AAAA,MACJ,YAAY,IAAIA,GAAE,aAAa;AAAA,MAC/B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,cAAc;AAAA,MACd,MAAM;AAAA,IACR,IAAI,KAAK,OAAO,IAAI,KAAK,aAAa;AACtC,QAAI;AACF,aAAuB,aAAAG,QAAE;AAAA,QACvB;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,UACX,eAAe,KAAK;AAAA,UACpB,cAAc,EAAE;AAAA,UAChB,UAAU;AAAA,UACV,WAAW,KAAK;AAAA,UAChB,MAAM;AAAA,QACR;AAAA,QACA,CAAC,KAAK,CAAC,KAAqB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,4BAA4B,SAAS,KAAK,YAAY,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,6BAA6B,YAAY,IAAI,CAAC,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,6BAA6B,YAAY,IAAI,CAAC,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,6BAA6B,YAAY,IAAI,CAAC,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,6BAA6B,YAAY,IAAI,CAAC,GAAmB,aAAAA,QAAE;AAAA,UAC7hB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,IAAI;AAAA,YAC/C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,GAAG;AAAA,YAC9C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,IAAI;AAAA,YAC/C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,GAAG;AAAA,YAC9C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,IAAI;AAAA,YAC/C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,GAAG;AAAA,YAC9C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,IAAI;AAAA,YAC/C,MAAM;AAAA,UACR;AAAA,QACF,GAAmB,aAAAA,QAAE;AAAA,UACnB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,cAAc,EAAE;AAAA,YAChB,WAAW,CAAC,MAAM,KAAK,iBAAiB,GAAG,GAAG;AAAA,YAC9C,MAAM;AAAA,UACR;AAAA,QACF,CAAC;AAAA,QACD,KAAqB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,8BAA8B,eAAe,CAAC,MAAM,EAAE,gBAAgB,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC;AAAA,QACjJ,KAAqB,aAAAA,QAAE,cAAc,aAAAA,QAAE,UAAU,MAAsB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,+BAA+B,CAAC,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,+BAA+B,CAAC,CAAC;AAAA,MACrO;AAAA,EACJ;AAAA,EACA,cAAc,GAAG;AACf,UAAM,IAAI,EAAE,GAAG,GAAG,GAAG,KAAK,MAAM,QAAQ,CAAC,EAAE;AAC3C,WAAO,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;AAAA,EAC/B;AAAA,EACA,SAAS;AACP,UAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,cAAc,GAAG,WAAW,GAAG,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,GAAG,cAAc,EAAE,IAAI,KAAK,OAAO,EAAE,cAAc,GAAG,qBAAqB,EAAE,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,oBAAoB,IAAI,MAAM,IAAI;AAAA,MACjP;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MACV,CAAC,KAAK,eAAe,KAAK,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU;AAAA,MACnD,KAAK;AAAA,IACP;AACA,WAAuB,aAAAA,QAAE,cAAc,OAAO,EAAE,KAAK,KAAK,cAAc,WAAW,GAAG,OAAO,EAAE,GAAmB,aAAAA,QAAE,cAAc,OAAO,EAAE,KAAK,KAAK,UAAU,WAAW,4BAA4B,eAAe,KAAK,uBAAuB,GAAG,CAAC,GAAG,IAAoB,aAAAA,QAAE,cAAc,OAAO,EAAE,WAAW,wBAAwB,OAAO,QAAQ,QAAQ,OAAO,GAAmB,aAAAA,QAAE,cAAc,QAAQ,MAAsB,aAAAA,QAAE,cAAc,QAAQ,EAAE,IAAI,QAAQ,KAAK,UAAU,GAAG,GAAmB,aAAAA,QAAE,cAAc,QAAQ,EAAE,OAAO,QAAQ,QAAQ,QAAQ,MAAM,QAAQ,CAAC,GAAG,IAAoB,aAAAA,QAAE;AAAA,MACjlB;AAAA,MACA;AAAA,QACE,IAAI,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI;AAAA,QACjC,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI;AAAA,QAClC,IAAI,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI;AAAA,QAC3B,IAAI,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI;AAAA,QAC5B,MAAM;AAAA,MACR;AAAA,IACF,IAAoB,aAAAA,QAAE;AAAA,MACpB;AAAA,MACA;AAAA,QACE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAAA,QAClB,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAAA,QAClB,OAAO,GAAG,EAAE,KAAK,GAAG,EAAE,IAAI;AAAA,QAC1B,QAAQ,GAAG,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,QAC5B,MAAM;AAAA,MACR;AAAA,IACF,CAAC,CAAC,GAAmB,aAAAA,QAAE,cAAc,QAAQ,EAAE,MAAM,SAAS,aAAa,KAAK,OAAO,QAAQ,QAAQ,QAAQ,MAAM,aAAa,KAAK,UAAU,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;AAAA,EACtK;AACF;AACA,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,mBAAmB,EAAE,GAAG,EAAE,GAAG,kBAAkB,GAAG,GAAG,EAAE,GAAG,gBAAgB;AAAA,EACnM,YAAY;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,EACf;AACF,CAAC;AACD,IAAI,IAAI;", "names": ["x", "K", "P", "u"]}