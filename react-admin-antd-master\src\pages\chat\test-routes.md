# 路由测试说明

## 测试步骤

1. 访问 http://localhost:5174/chat - 应该显示默认聊天界面
2. 在侧边栏点击"任务会话"按钮 - 应该跳转到当前会话的任务页面
3. 在侧边栏点击"专家咨询"下拉菜单，选择专家 - 应该跳转到专家详情页面
4. 在会话列表中点击某个会话的操作菜单，选择"打开任务会话" - 应该跳转到该会话的任务页面

## 预期路由

- `/chat` - 默认聊天界面
- `/chat/task/:sessionid` - 任务会话页面（例如：/chat/task/session-123）
- `/chat/expert/:id` - 专家详情页面（例如：/chat/expert/1）

## 功能验证

### 导航按钮状态
- 当前页面的导航按钮应该高亮显示（primary类型）
- 其他页面的按钮显示默认样式

### 路由参数
- 任务页面应该正确显示会话ID和会话信息
- 专家页面应该正确显示专家ID和专家信息

### 错误处理
- 访问不存在的会话ID应该显示"会话不存在"
- 访问不存在的专家ID应该显示"专家不存在"
