import {
  require_xeora
} from "./chunk-64A5RQK2.js";
import {
  require_xml_doc
} from "./chunk-AQPMUF6S.js";
import {
  require_xojo
} from "./chunk-NYUBPVOR.js";
import {
  require_xquery
} from "./chunk-7TXJMWFO.js";
import {
  require_yang
} from "./chunk-MBFA3BGK.js";
import {
  require_zig
} from "./chunk-QXBPHG7B.js";
import {
  require_core
} from "./chunk-S3LNBBSZ.js";
import {
  require_vim
} from "./chunk-IR6WYWXQ.js";
import {
  require_visual_basic
} from "./chunk-PYFWARAH.js";
import {
  require_warpscript
} from "./chunk-F33C6TLE.js";
import {
  require_wasm
} from "./chunk-BUXFYFXT.js";
import {
  require_web_idl
} from "./chunk-TP2BO6NJ.js";
import {
  require_wiki
} from "./chunk-S3YBPPML.js";
import {
  require_wolfram
} from "./chunk-X66QIC2H.js";
import {
  require_wren
} from "./chunk-XLP6AXME.js";
import {
  require_unrealscript
} from "./chunk-GG6AZQU6.js";
import {
  require_uorazor
} from "./chunk-24RJX4XR.js";
import {
  require_uri
} from "./chunk-ANZBKSVF.js";
import {
  require_v
} from "./chunk-UTR4EHYL.js";
import {
  require_vala
} from "./chunk-LAM44532.js";
import {
  require_velocity
} from "./chunk-2X7B3CZE.js";
import {
  require_verilog
} from "./chunk-LLJ3CJAR.js";
import {
  require_vhdl
} from "./chunk-VY5ACJXI.js";
import {
  require_tcl
} from "./chunk-KXBBUNCM.js";
import {
  require_textile
} from "./chunk-U6KTM3OX.js";
import {
  require_toml
} from "./chunk-V3EVJCGI.js";
import {
  require_tremor
} from "./chunk-6R4I4XH6.js";
import {
  require_tsx
} from "./chunk-D7KQVAFQ.js";
import {
  require_tt2
} from "./chunk-HBFWEXVT.js";
import {
  require_twig
} from "./chunk-YVEWSE3D.js";
import {
  require_typoscript
} from "./chunk-VI5YJ32B.js";
import {
  require_swift
} from "./chunk-FXRMH5PD.js";
import {
  require_systemd
} from "./chunk-E4FSEOSH.js";
import {
  require_t4_cs
} from "./chunk-ISDLIZ4F.js";
import {
  require_t4_vb
} from "./chunk-PWT7XM3J.js";
import {
  require_t4_templating
} from "./chunk-FZBO6NU4.js";
import {
  require_vbnet
} from "./chunk-FWHEYOX3.js";
import {
  require_tap
} from "./chunk-ZRB7V5X7.js";
import {
  require_yaml
} from "./chunk-5VFNYBJT.js";
import {
  require_soy
} from "./chunk-BNS53AEJ.js";
import {
  require_sparql
} from "./chunk-CYM6D73R.js";
import {
  require_turtle
} from "./chunk-A3YBWAN4.js";
import {
  require_splunk_spl
} from "./chunk-VU3DSECT.js";
import {
  require_sqf
} from "./chunk-6EHHAE7H.js";
import {
  require_squirrel
} from "./chunk-TEH5CYTJ.js";
import {
  require_stan
} from "./chunk-JW4MHOBZ.js";
import {
  require_stylus
} from "./chunk-LJFP3WS3.js";
import {
  require_scss
} from "./chunk-AX5RSCI5.js";
import {
  require_shell_session
} from "./chunk-IV5C4YY4.js";
import {
  require_smali
} from "./chunk-XVRP3PQF.js";
import {
  require_smalltalk
} from "./chunk-ARXHFS32.js";
import {
  require_smarty
} from "./chunk-R73YRH4P.js";
import {
  require_sml
} from "./chunk-JYARA34U.js";
import {
  require_solidity
} from "./chunk-NOW6VIE2.js";
import {
  require_solution_file
} from "./chunk-7PHW64YW.js";
import {
  require_rest
} from "./chunk-U33SC5NJ.js";
import {
  require_rip
} from "./chunk-AVLJSICL.js";
import {
  require_roboconf
} from "./chunk-4WEUJEJK.js";
import {
  require_robotframework
} from "./chunk-UNMC26F2.js";
import {
  require_rust
} from "./chunk-2IJSMIVF.js";
import {
  require_sas
} from "./chunk-6OFKBPBX.js";
import {
  require_sass
} from "./chunk-LKSHDFQD.js";
import {
  require_scala
} from "./chunk-HEAIRKIY.js";
import {
  require_qore
} from "./chunk-42FDLAWF.js";
import {
  require_qsharp
} from "./chunk-GJ3FRGWD.js";
import {
  require_r
} from "./chunk-CPETBBIS.js";
import {
  require_racket
} from "./chunk-WPDWJN4G.js";
import {
  require_reason
} from "./chunk-4C7R4ZCB.js";
import {
  require_regex
} from "./chunk-OA52JZAY.js";
import {
  require_rego
} from "./chunk-ZF6S5HIW.js";
import {
  require_renpy
} from "./chunk-6JV6FVDM.js";
import {
  require_pug
} from "./chunk-IWCNFKZT.js";
import {
  require_puppet
} from "./chunk-TM52RSEM.js";
import {
  require_pure
} from "./chunk-PUWKX7GZ.js";
import {
  require_purebasic
} from "./chunk-JID6G773.js";
import {
  require_purescript
} from "./chunk-62ZRXJW4.js";
import {
  require_python
} from "./chunk-KAQS6YGL.js";
import {
  require_q
} from "./chunk-UDC5ICUX.js";
import {
  require_qml
} from "./chunk-EEHB7T4J.js";
import {
  require_powerquery
} from "./chunk-JQSPBWBR.js";
import {
  require_powershell
} from "./chunk-XUYY56ZI.js";
import {
  require_processing
} from "./chunk-EBFWRW7Z.js";
import {
  require_prolog
} from "./chunk-4DWX22H6.js";
import {
  require_promql
} from "./chunk-CYX7GNWV.js";
import {
  require_properties
} from "./chunk-2KUPZTDW.js";
import {
  require_protobuf
} from "./chunk-H4S5AJAJ.js";
import {
  require_psl
} from "./chunk-BQWCQGGK.js";
import {
  require_pascal
} from "./chunk-QIOOV2P5.js";
import {
  require_pascaligo
} from "./chunk-Z23B4OHP.js";
import {
  require_pcaxis
} from "./chunk-XNZCVGLJ.js";
import {
  require_peoplecode
} from "./chunk-3ADJB5WE.js";
import {
  require_perl
} from "./chunk-PSJB2FEC.js";
import {
  require_php_extras
} from "./chunk-LFX7YAMR.js";
import {
  require_phpdoc
} from "./chunk-L4P4MD54.js";
import {
  require_plsql
} from "./chunk-5OJ4QKHW.js";
import {
  require_nsis
} from "./chunk-WDQ743PA.js";
import {
  require_objectivec
} from "./chunk-VBDYUA5C.js";
import {
  require_ocaml
} from "./chunk-UNMLKFAV.js";
import {
  require_opencl
} from "./chunk-SNV7CI74.js";
import {
  require_openqasm
} from "./chunk-272TYSOW.js";
import {
  require_oz
} from "./chunk-NOLRKRRA.js";
import {
  require_parigp
} from "./chunk-JWS342WX.js";
import {
  require_parser
} from "./chunk-T6N3XNPW.js";
import {
  require_nand2tetris_hdl
} from "./chunk-JHQGPCZ2.js";
import {
  require_naniscript
} from "./chunk-QEAYO6L2.js";
import {
  require_nasm
} from "./chunk-CXLR5RLE.js";
import {
  require_neon
} from "./chunk-VLTPHF2V.js";
import {
  require_nevod
} from "./chunk-2CDTTCS5.js";
import {
  require_nginx
} from "./chunk-KBGJF7Y5.js";
import {
  require_nim
} from "./chunk-7RNESOLO.js";
import {
  require_nix
} from "./chunk-2IOCYMY3.js";
import {
  require_mel
} from "./chunk-X6U6ARVP.js";
import {
  require_mermaid
} from "./chunk-WFFDRZYB.js";
import {
  require_mizar
} from "./chunk-KXJJJY7I.js";
import {
  require_mongodb
} from "./chunk-YPDFSFM4.js";
import {
  require_monkey
} from "./chunk-7QDSLUJ4.js";
import {
  require_moonscript
} from "./chunk-CCECACCS.js";
import {
  require_n1ql
} from "./chunk-V2YQ4EYS.js";
import {
  require_n4js
} from "./chunk-JNGFGV3I.js";
import {
  require_log
} from "./chunk-H75AYQQM.js";
import {
  require_lolcode
} from "./chunk-4EA4LONW.js";
import {
  require_magma
} from "./chunk-FFYOWM3M.js";
import {
  require_makefile
} from "./chunk-ST5TCBLP.js";
import {
  require_markdown
} from "./chunk-I66YXHHO.js";
import {
  require_matlab
} from "./chunk-GTF6OMQS.js";
import {
  require_maxscript
} from "./chunk-6LVGODMP.js";
import {
  require_latte
} from "./chunk-U6TNVTEZ.js";
import {
  require_less
} from "./chunk-JPYKF2A7.js";
import {
  require_lilypond
} from "./chunk-24ZT7QLX.js";
import {
  require_scheme
} from "./chunk-QLDAVMWD.js";
import {
  require_liquid
} from "./chunk-NC5ZFXPH.js";
import {
  require_lisp
} from "./chunk-25NM7BHO.js";
import {
  require_livescript
} from "./chunk-WOXNAWE6.js";
import {
  require_llvm
} from "./chunk-BH6SWN6U.js";
import {
  require_julia
} from "./chunk-OIXJZ532.js";
import {
  require_keepalived
} from "./chunk-ENOMTEAX.js";
import {
  require_keyman
} from "./chunk-AK3SUHXY.js";
import {
  require_kotlin
} from "./chunk-Z4BCKU5S.js";
import {
  require_kumir
} from "./chunk-ANJMKVHH.js";
import {
  require_kusto
} from "./chunk-WHP2U7KH.js";
import {
  require_latex
} from "./chunk-W2KCZIP2.js";
import {
  require_php
} from "./chunk-EZ3ARRGN.js";
import {
  require_js_templates
} from "./chunk-VVDQOMJA.js";
import {
  require_jsdoc
} from "./chunk-QXAXHCZQ.js";
import {
  require_typescript
} from "./chunk-KZB5DCYN.js";
import {
  require_json5
} from "./chunk-VJ7NHV6I.js";
import {
  require_jsonp
} from "./chunk-L75OVERN.js";
import {
  require_json
} from "./chunk-ZKTXDMHC.js";
import {
  require_jsstacktrace
} from "./chunk-N5STPBUE.js";
import {
  require_jsx
} from "./chunk-6QORJ4Q5.js";
import {
  require_javadoc
} from "./chunk-6OACV7VS.js";
import {
  require_javadoclike
} from "./chunk-TK53SHLB.js";
import {
  require_javastacktrace
} from "./chunk-DKFHXYME.js";
import {
  require_jexl
} from "./chunk-DY3L3347.js";
import {
  require_jolie
} from "./chunk-YT2JO3K7.js";
import {
  require_jq
} from "./chunk-SUFKDLVZ.js";
import {
  require_js_extras
} from "./chunk-FAOBVY7I.js";
import {
  require_idris
} from "./chunk-HQ2XZHOZ.js";
import {
  require_iecst
} from "./chunk-YS5DEEGK.js";
import {
  require_ignore
} from "./chunk-AIU5Y4E4.js";
import {
  require_inform7
} from "./chunk-2JI2JIMS.js";
import {
  require_ini
} from "./chunk-TX6P7NQG.js";
import {
  require_io
} from "./chunk-UBBVOFRW.js";
import {
  require_j
} from "./chunk-34HYNGSX.js";
import {
  require_java
} from "./chunk-32GNKVOG.js";
import {
  require_hlsl
} from "./chunk-3LZM5W74.js";
import {
  require_hoon
} from "./chunk-7E4JKFCQ.js";
import {
  require_hpkp
} from "./chunk-NZNVUTKJ.js";
import {
  require_hsts
} from "./chunk-NMKL24IO.js";
import {
  require_http
} from "./chunk-BJHFWVGT.js";
import {
  require_ichigojam
} from "./chunk-JU2Z7LUX.js";
import {
  require_icon
} from "./chunk-4J5GABO2.js";
import {
  require_icu_message_format
} from "./chunk-HXHTFIZC.js";
import {
  require_go
} from "./chunk-VZMLXTA4.js";
import {
  require_graphql
} from "./chunk-3P4YTVZF.js";
import {
  require_groovy
} from "./chunk-EBAJYE6H.js";
import {
  require_haml
} from "./chunk-G3HSV4AW.js";
import {
  require_handlebars
} from "./chunk-TOCZOWOH.js";
import {
  require_haskell
} from "./chunk-G4KDME7U.js";
import {
  require_haxe
} from "./chunk-WSUKORX3.js";
import {
  require_hcl
} from "./chunk-3G5OPJSA.js";
import {
  require_gdscript
} from "./chunk-PY3XYFGN.js";
import {
  require_gedcom
} from "./chunk-TIN56WDF.js";
import {
  require_gherkin
} from "./chunk-5HVYOBMM.js";
import {
  require_git
} from "./chunk-OJETLXDO.js";
import {
  require_glsl
} from "./chunk-NAATUKQC.js";
import {
  require_gml
} from "./chunk-SQLBQTKY.js";
import {
  require_gn
} from "./chunk-OOQSMVHB.js";
import {
  require_go_module
} from "./chunk-34SRACAL.js";
import {
  require_false
} from "./chunk-I5XZKNRV.js";
import {
  require_firestore_security_rules
} from "./chunk-LSIVRPGC.js";
import {
  require_flow
} from "./chunk-LIPHBJAF.js";
import {
  require_fortran
} from "./chunk-GTHRTNUE.js";
import {
  require_fsharp
} from "./chunk-IY6RYC3W.js";
import {
  require_ftl
} from "./chunk-FC2TJY5I.js";
import {
  require_gap
} from "./chunk-EPI2UWQK.js";
import {
  require_gcode
} from "./chunk-AE3UAPUT.js";
import {
  require_elixir
} from "./chunk-5P3YOUHH.js";
import {
  require_elm
} from "./chunk-JBLUW7Q3.js";
import {
  require_erb
} from "./chunk-IGV6QIJA.js";
import {
  require_erlang
} from "./chunk-PFUR5B6T.js";
import {
  require_etlua
} from "./chunk-7GNUXDU5.js";
import {
  require_lua
} from "./chunk-J7TGCEB7.js";
import {
  require_excel_formula
} from "./chunk-RFKO2GK2.js";
import {
  require_factor
} from "./chunk-YF3WXO4L.js";
import {
  require_django
} from "./chunk-EATG3ES7.js";
import {
  require_dns_zone_file
} from "./chunk-2VNTEMAS.js";
import {
  require_docker
} from "./chunk-S2YDGNVK.js";
import {
  require_dot
} from "./chunk-EYQGO2U2.js";
import {
  require_ebnf
} from "./chunk-I7WPY5DN.js";
import {
  require_editorconfig
} from "./chunk-6XIJ7FEI.js";
import {
  require_eiffel
} from "./chunk-4QV5QMYZ.js";
import {
  require_ejs
} from "./chunk-RFKM63RW.js";
import {
  require_cypher
} from "./chunk-KOAMFMDE.js";
import {
  require_d
} from "./chunk-RJKICCUT.js";
import {
  require_dart
} from "./chunk-IJLI7ZHC.js";
import {
  require_dataweave
} from "./chunk-SNCTQEOW.js";
import {
  require_dax
} from "./chunk-URJS4RGC.js";
import {
  require_dhall
} from "./chunk-SFQ757B3.js";
import {
  require_diff
} from "./chunk-ZMZLMUAL.js";
import {
  require_markup_templating
} from "./chunk-SXFXITWF.js";
import {
  require_coq
} from "./chunk-IOLCNM6N.js";
import {
  require_crystal
} from "./chunk-6FOV3XLB.js";
import {
  require_ruby
} from "./chunk-PIL6UOMZ.js";
import {
  require_cshtml
} from "./chunk-MQG6DRPW.js";
import {
  require_csp
} from "./chunk-5S5SOT5S.js";
import {
  require_css_extras
} from "./chunk-PU7GDRMF.js";
import {
  require_csv
} from "./chunk-E2L456V4.js";
import {
  require_chaiscript
} from "./chunk-7OCCDICW.js";
import {
  require_cil
} from "./chunk-RKG5FLHY.js";
import {
  require_clojure
} from "./chunk-BOHXIGP4.js";
import {
  require_cmake
} from "./chunk-RQHK7QRQ.js";
import {
  require_cobol
} from "./chunk-2D666G6C.js";
import {
  require_coffeescript
} from "./chunk-VQTRS5HX.js";
import {
  require_concurnas
} from "./chunk-HI63JXEJ.js";
import {
  require_birb
} from "./chunk-UMA7PRZW.js";
import {
  require_bison
} from "./chunk-3P42EIFX.js";
import {
  require_bnf
} from "./chunk-QOY5FWXI.js";
import {
  require_brainfuck
} from "./chunk-47FRU5SI.js";
import {
  require_brightscript
} from "./chunk-BOEIGX7S.js";
import {
  require_bro
} from "./chunk-UXLCWFWL.js";
import {
  require_bsl
} from "./chunk-WLMBHP5H.js";
import {
  require_cfscript
} from "./chunk-T3PW33XQ.js";
import {
  require_autoit
} from "./chunk-DQ5RAWZB.js";
import {
  require_avisynth
} from "./chunk-QF4FFPER.js";
import {
  require_avro_idl
} from "./chunk-QGQS4WOW.js";
import {
  require_bash
} from "./chunk-AAIG5G6P.js";
import {
  require_basic
} from "./chunk-TZ3ZLG4V.js";
import {
  require_batch
} from "./chunk-VXQ3AAQV.js";
import {
  require_bbcode
} from "./chunk-YC5ODAWM.js";
import {
  require_bicep
} from "./chunk-B6EXDXZS.js";
import {
  require_arduino
} from "./chunk-Q4U3KTRN.js";
import {
  require_arff
} from "./chunk-XNADDNHI.js";
import {
  require_asciidoc
} from "./chunk-D6CQWJ4G.js";
import {
  require_asm6502
} from "./chunk-BIJE6XBF.js";
import {
  require_asmatmel
} from "./chunk-USKL5VHZ.js";
import {
  require_aspnet
} from "./chunk-CWRR636I.js";
import {
  require_csharp
} from "./chunk-FNIKGPFW.js";
import {
  require_autohotkey
} from "./chunk-7OPL7M53.js";
import {
  require_apacheconf
} from "./chunk-BBAFC5FL.js";
import {
  require_apex
} from "./chunk-OFIQAQKS.js";
import {
  require_sql
} from "./chunk-ZNDGWRYG.js";
import {
  require_apl
} from "./chunk-CU4CXULM.js";
import {
  require_applescript
} from "./chunk-WTFCB4NP.js";
import {
  require_aql
} from "./chunk-DRQ6G3J6.js";
import {
  require_cpp
} from "./chunk-UWGSMG7I.js";
import {
  require_c
} from "./chunk-GGAUT4BI.js";
import {
  require_abap
} from "./chunk-TKAE7BYX.js";
import {
  require_abnf
} from "./chunk-OHYAGNEC.js";
import {
  require_actionscript
} from "./chunk-PVIPFVKQ.js";
import {
  require_ada
} from "./chunk-JG6Z5P4D.js";
import {
  require_agda
} from "./chunk-ZZ35KGOD.js";
import {
  require_al
} from "./chunk-FIUUUXN4.js";
import {
  require_antlr4
} from "./chunk-N263ZFSH.js";
import {
  __commonJS
} from "./chunk-OL46QLBJ.js";

// node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/.pnpm/refractor@3.6.0/node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-JSD2IZFN.js.map
