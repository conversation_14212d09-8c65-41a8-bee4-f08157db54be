{"version": 3, "sources": ["../../.pnpm/mdast-util-newline-to-break@2.0.0/node_modules/mdast-util-newline-to-break/lib/index.js", "../../.pnpm/remark-breaks@4.0.0/node_modules/remark-breaks/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast-util-find-and-replace').ReplaceFunction} ReplaceFunction\n */\n\nimport {findAndReplace} from 'mdast-util-find-and-replace'\n\n/**\n * Turn normal line endings into hard breaks.\n *\n * @param {Nodes} tree\n *   Tree to change.\n * @returns {undefined}\n *   Nothing.\n */\nexport function newlineToBreak(tree) {\n  findAndReplace(tree, [/\\r?\\n|\\r/g, replace])\n}\n\n/**\n * Replace line endings.\n *\n * @type {ReplaceFunction}\n */\nfunction replace() {\n  return {type: 'break'}\n}\n", "/**\n * @typedef {import('mdast').Root} Root\n */\n\nimport {newlineToBreak} from 'mdast-util-newline-to-break'\n\n/**\n * Support hard breaks without needing spaces or escapes (turns enters into\n * `<br>`s).\n *\n * @returns\n *   Transform.\n */\nexport default function remarkBreaks() {\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    newlineToBreak(tree)\n  }\n}\n"], "mappings": ";;;;;;;AAeO,SAAS,eAAe,MAAM;AACnC,iBAAe,MAAM,CAAC,aAAa,OAAO,CAAC;AAC7C;AAOA,SAAS,UAAU;AACjB,SAAO,EAAC,MAAM,QAAO;AACvB;;;ACbe,SAAR,eAAgC;AASrC,SAAO,SAAU,MAAM;AACrB,mBAAe,IAAI;AAAA,EACrB;AACF;", "names": []}