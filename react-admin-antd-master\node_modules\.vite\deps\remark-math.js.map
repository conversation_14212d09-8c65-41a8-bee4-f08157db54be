{"version": 3, "sources": ["../../.pnpm/mdast-util-math@3.0.0/node_modules/mdast-util-math/lib/index.js", "../../.pnpm/micromark-extension-math@3.1.0/node_modules/micromark-extension-math/dev/lib/math-flow.js", "../../.pnpm/micromark-extension-math@3.1.0/node_modules/micromark-extension-math/dev/lib/math-text.js", "../../.pnpm/micromark-extension-math@3.1.0/node_modules/micromark-extension-math/dev/lib/syntax.js", "../../.pnpm/micromark-extension-math@3.1.0/node_modules/micromark-extension-math/dev/lib/html.js", "../../.pnpm/remark-math@6.0.0/node_modules/remark-math/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('../index.js').InlineMath} InlineMath\n * @typedef {import('../index.js').Math} Math\n *\n * @typedef ToOptions\n *   Configuration.\n * @property {boolean | null | undefined} [singleDollarTextMath=true]\n *   Whether to support math (text) with a single dollar (default: `true`).\n *\n *   Single dollars work in Pandoc and many other places, but often interfere\n *   with “normal” dollars in text.\n *   If you turn this off, you can still use two or more dollars for text math.\n */\n\nimport {ok as assert} from 'devlop'\nimport {longestStreak} from 'longest-streak'\n\n/**\n * Create an extension for `mdast-util-from-markdown`.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nexport function mathFromMarkdown() {\n  return {\n    enter: {\n      mathFlow: enterMathFlow,\n      mathFlowFenceMeta: enterMathFlowMeta,\n      mathText: enterMathText\n    },\n    exit: {\n      mathFlow: exitMathFlow,\n      mathFlowFence: exitMathFlowFence,\n      mathFlowFenceMeta: exitMathFlowMeta,\n      mathFlowValue: exitMathData,\n      mathText: exitMathText,\n      mathTextData: exitMathData\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMathFlow(token) {\n    /** @type {HastElement} */\n    const code = {\n      type: 'element',\n      tagName: 'code',\n      properties: {className: ['language-math', 'math-display']},\n      children: []\n    }\n    this.enter(\n      {\n        type: 'math',\n        meta: null,\n        value: '',\n        data: {hName: 'pre', hChildren: [code]}\n      },\n      token\n    )\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMathFlowMeta() {\n    this.buffer()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMathFlowMeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node.type === 'math')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMathFlowFence() {\n    // Exit if this is the closing fence.\n    if (this.data.mathFlowInside) return\n    this.buffer()\n    this.data.mathFlowInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMathFlow(token) {\n    const data = this.resume().replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    const node = this.stack[this.stack.length - 1]\n    assert(node.type === 'math')\n    this.exit(token)\n    node.value = data\n    // @ts-expect-error: we defined it in `enterMathFlow`.\n    const code = /** @type {HastElement} */ (node.data.hChildren[0])\n    assert(code.type === 'element')\n    assert(code.tagName === 'code')\n    code.children.push({type: 'text', value: data})\n    this.data.mathFlowInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function enterMathText(token) {\n    this.enter(\n      {\n        type: 'inlineMath',\n        value: '',\n        data: {\n          hName: 'code',\n          hProperties: {className: ['language-math', 'math-inline']},\n          hChildren: []\n        }\n      },\n      token\n    )\n    this.buffer()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMathText(token) {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node.type === 'inlineMath')\n    this.exit(token)\n    node.value = data\n    const children = /** @type {Array<HastElementContent>} */ (\n      // @ts-expect-error: we defined it in `enterMathFlow`.\n      node.data.hChildren\n    )\n    children.push({type: 'text', value: data})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {FromMarkdownHandle}\n   */\n  function exitMathData(token) {\n    this.config.enter.data.call(this, token)\n    this.config.exit.data.call(this, token)\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown`.\n *\n * @param {ToOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nexport function mathToMarkdown(options) {\n  let single = (options || {}).singleDollarTextMath\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  inlineMath.peek = inlineMathPeek\n\n  return {\n    unsafe: [\n      {character: '\\r', inConstruct: 'mathFlowMeta'},\n      {character: '\\n', inConstruct: 'mathFlowMeta'},\n      {\n        character: '$',\n        after: single ? undefined : '\\\\$',\n        inConstruct: 'phrasing'\n      },\n      {character: '$', inConstruct: 'mathFlowMeta'},\n      {atBreak: true, character: '$', after: '\\\\$'}\n    ],\n    handlers: {math, inlineMath}\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {Math} node\n   */\n  // Note: fixing this code? Please also fix the similar code for code:\n  // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/main/lib/handle/code.js>\n  function math(node, _, state, info) {\n    const raw = node.value || ''\n    const tracker = state.createTracker(info)\n    const sequence = '$'.repeat(Math.max(longestStreak(raw, '$') + 1, 2))\n    const exit = state.enter('mathFlow')\n    let value = tracker.move(sequence)\n\n    if (node.meta) {\n      const subexit = state.enter('mathFlowMeta')\n      value += tracker.move(\n        state.safe(node.meta, {\n          after: '\\n',\n          before: value,\n          encode: ['$'],\n          ...tracker.current()\n        })\n      )\n      subexit()\n    }\n\n    value += tracker.move('\\n')\n\n    if (raw) {\n      value += tracker.move(raw + '\\n')\n    }\n\n    value += tracker.move(sequence)\n    exit()\n    return value\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {InlineMath} node\n   */\n  // Note: fixing this code? Please also fix the similar code for inline code:\n  // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/main/lib/handle/inline-code.js>\n  function inlineMath(node, _, state) {\n    let value = node.value || ''\n    let size = 1\n\n    if (!single) size++\n\n    // If there is a single dollar sign on its own in the math, use a fence of\n    // two.\n    // If there are two in a row, use one.\n    while (\n      new RegExp('(^|[^$])' + '\\\\$'.repeat(size) + '([^$]|$)').test(value)\n    ) {\n      size++\n    }\n\n    const sequence = '$'.repeat(size)\n\n    // If this is not just spaces or eols (tabs don’t count), and either the\n    // first and last character are a space or eol, or the first or last\n    // character are dollar signs, then pad with spaces.\n    if (\n      // Contains non-space.\n      /[^ \\r\\n]/.test(value) &&\n      // Starts with space and ends with space.\n      ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) ||\n        // Starts or ends with dollar.\n        /^\\$|\\$$/.test(value))\n    ) {\n      value = ' ' + value + ' '\n    }\n\n    let index = -1\n\n    // We have a potential problem: certain characters after eols could result in\n    // blocks being seen.\n    // For example, if someone injected the string `'\\n# b'`, then that would\n    // result in an ATX heading.\n    // We can’t escape characters in `inlineMath`, but because eols are\n    // transformed to spaces when going from markdown to HTML anyway, we can swap\n    // them out.\n    while (++index < state.unsafe.length) {\n      const pattern = state.unsafe[index]\n\n      // Only look for `atBreak`s.\n      // Btw: note that `atBreak` patterns will always start the regex at LF or\n      // CR.\n      if (!pattern.atBreak) continue\n\n      const expression = state.compilePattern(pattern)\n      /** @type {RegExpExecArray | null} */\n      let match\n\n      while ((match = expression.exec(value))) {\n        let position = match.index\n\n        // Support CRLF (patterns only look for one of the characters).\n        if (\n          value.codePointAt(position) === 10 /* `\\n` */ &&\n          value.codePointAt(position - 1) === 13 /* `\\r` */\n        ) {\n          position--\n        }\n\n        value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n      }\n    }\n\n    return sequence + value + sequence\n  }\n\n  /**\n   * @returns {string}\n   */\n  function inlineMathPeek() {\n    return '$'\n  }\n}\n", "/**\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const mathFlow = {\n  tokenize: tokenizeMathFenced,\n  concrete: true,\n  name: 'mathFlow'\n}\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeMathFenced(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  const initialSize =\n    tail && tail[1].type === types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let sizeOpen = 0\n\n  return start\n\n  /**\n   * Start of math.\n   *\n   * ```markdown\n   * > | $$\n   *     ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.dollarSign, 'expected `$`')\n    effects.enter('mathFlow')\n    effects.enter('mathFlowFence')\n    effects.enter('mathFlowFenceSequence')\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | $$\n   *      ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.dollarSign) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    if (sizeOpen < 2) {\n      return nok(code)\n    }\n\n    effects.exit('mathFlowFenceSequence')\n    return factorySpace(effects, metaBefore, types.whitespace)(code)\n  }\n\n  /**\n   * In opening fence, before meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *       ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return metaAfter(code)\n    }\n\n    effects.enter('mathFlowFenceMeta')\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *        ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit('mathFlowFenceMeta')\n      return metaAfter(code)\n    }\n\n    if (code === codes.dollarSign) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * After meta.\n   *\n   * ```markdown\n   * > | $$\n   *       ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function metaAfter(code) {\n    // Guaranteed to be eol/eof.\n    effects.exit('mathFlowFence')\n\n    if (self.interrupt) {\n      return ok(code)\n    }\n\n    return effects.attempt(\n      nonLazyContinuation,\n      beforeNonLazyContinuation,\n      after\n    )(code)\n  }\n\n  /**\n   * After eol/eof in math, at a non-lazy closing fence or content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   * > | $$\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeNonLazyContinuation(code) {\n    return effects.attempt(\n      {tokenize: tokenizeClosingFence, partial: true},\n      after,\n      contentStart\n    )(code)\n  }\n\n  /**\n   * Before math content, definitely not before a closing fence.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return (\n      initialSize\n        ? factorySpace(\n            effects,\n            beforeContentChunk,\n            types.linePrefix,\n            initialSize + 1\n          )\n        : beforeContentChunk\n    )(code)\n  }\n\n  /**\n   * Before math content, after optional prefix.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(\n        nonLazyContinuation,\n        beforeNonLazyContinuation,\n        after\n      )(code)\n    }\n\n    effects.enter('mathFlowValue')\n    return contentChunk(code)\n  }\n\n  /**\n   * In math content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *      ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit('mathFlowValue')\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After math (ha!).\n   *\n   * ```markdown\n   *   | $$\n   *   | \\frac{1}{2}\n   * > | $$\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit('mathFlow')\n    return ok(code)\n  }\n\n  /** @type {Tokenizer} */\n  function tokenizeClosingFence(effects, ok, nok) {\n    let size = 0\n\n    assert(self.parser.constructs.disable.null, 'expected `disable.null`')\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     */\n    return factorySpace(\n      effects,\n      beforeSequenceClose,\n      types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : constants.tabSize\n    )\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      effects.enter('mathFlowFence')\n      effects.enter('mathFlowFenceSequence')\n      return sequenceClose(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === codes.dollarSign) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size < sizeOpen) {\n        return nok(code)\n      }\n\n      effects.exit('mathFlowFenceSequence')\n      return factorySpace(effects, afterSequenceClose, types.whitespace)(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n    function afterSequenceClose(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit('mathFlowFence')\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (code === null) {\n      return ok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n", "/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Construct, Previous, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n// To do: next major: clean spaces in HTML compiler.\n// This has to be coordinated together with `mdast-util-math`.\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/**\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Construct}\n *   Construct.\n */\nexport function mathText(options) {\n  const options_ = options || {}\n  let single = options_.singleDollarTextMath\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    tokenize: tokenizeMathText,\n    resolve: resolveMathText,\n    previous,\n    name: 'mathText'\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeMathText(effects, ok, nok) {\n    const self = this\n    let sizeOpen = 0\n    /** @type {number} */\n    let size\n    /** @type {Token} */\n    let token\n\n    return start\n\n    /**\n     * Start of math (text).\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * > | \\$a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      assert(code === codes.dollarSign, 'expected `$`')\n      assert(previous.call(self, self.previous), 'expected correct previous')\n      effects.enter('mathText')\n      effects.enter('mathTextSequence')\n      return sequenceOpen(code)\n    }\n\n    /**\n     * In opening sequence.\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceOpen(code) {\n      if (code === codes.dollarSign) {\n        effects.consume(code)\n        sizeOpen++\n        return sequenceOpen\n      }\n\n      // Not enough markers in the sequence.\n      if (sizeOpen < 2 && !single) {\n        return nok(code)\n      }\n\n      effects.exit('mathTextSequence')\n      return between(code)\n    }\n\n    /**\n     * Between something and something else.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^^\n     * ```\n     *\n     * @type {State}\n     */\n    function between(code) {\n      if (code === codes.eof) {\n        return nok(code)\n      }\n\n      if (code === codes.dollarSign) {\n        token = effects.enter('mathTextSequence')\n        size = 0\n        return sequenceClose(code)\n      }\n\n      // Tabs don’t work, and virtual spaces don’t make sense.\n      if (code === codes.space) {\n        effects.enter('space')\n        effects.consume(code)\n        effects.exit('space')\n        return between\n      }\n\n      if (markdownLineEnding(code)) {\n        effects.enter(types.lineEnding)\n        effects.consume(code)\n        effects.exit(types.lineEnding)\n        return between\n      }\n\n      // Data.\n      effects.enter('mathTextData')\n      return data(code)\n    }\n\n    /**\n     * In data.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */\n    function data(code) {\n      if (\n        code === codes.eof ||\n        code === codes.space ||\n        code === codes.dollarSign ||\n        markdownLineEnding(code)\n      ) {\n        effects.exit('mathTextData')\n        return between(code)\n      }\n\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * In closing sequence.\n     *\n     * ```markdown\n     * > | `a`\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */\n\n    function sequenceClose(code) {\n      // More.\n      if (code === codes.dollarSign) {\n        effects.consume(code)\n        size++\n        return sequenceClose\n      }\n\n      // Done!\n      if (size === sizeOpen) {\n        effects.exit('mathTextSequence')\n        effects.exit('mathText')\n        return ok(code)\n      }\n\n      // More or less accents: mark as data.\n      token.type = 'mathTextData'\n      return data(code)\n    }\n  }\n}\n\n/** @type {Resolver} */\nfunction resolveMathText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === 'mathTextData') {\n        // Then we have padding.\n        events[tailExitIndex][1].type = 'mathTextPadding'\n        events[headEnterIndex][1].type = 'mathTextPadding'\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === types.lineEnding\n    ) {\n      events[enter][1].type = 'mathTextData'\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== codes.dollarSign ||\n    this.events[this.events.length - 1][1].type === types.characterEscape\n  )\n}\n", "/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport {codes} from 'micromark-util-symbol'\nimport {mathFlow} from './math-flow.js'\nimport {mathText} from './math-text.js'\n\n/**\n * Create an extension for `micromark` to enable math syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable math syntax.\n */\nexport function math(options) {\n  return {\n    flow: {[codes.dollarSign]: mathFlow},\n    text: {[codes.dollarSign]: mathText(options)}\n  }\n}\n", "/**\n * @import {HtmlOptions as Options} from 'micromark-extension-math'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\nimport katex from 'katex'\n\nconst renderToString = katex.renderToString\n\n/**\n * Create an extension for `micromark` to support math when serializing to\n * HTML.\n *\n * > 👉 **Note**: this uses KaTeX to render math.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions`, to\n *   support math when serializing to HTML.\n */\nexport function mathHtml(options) {\n  return {\n    enter: {\n      mathFlow() {\n        this.lineEndingIfNeeded()\n        this.tag('<div class=\"math math-display\">')\n      },\n      mathFlowFenceMeta() {\n        this.buffer()\n      },\n      mathText() {\n        // Double?\n        this.tag('<span class=\"math math-inline\">')\n        this.buffer()\n      }\n    },\n    exit: {\n      mathFlow() {\n        const value = this.resume()\n        this.tag(math(value.replace(/(?:\\r?\\n|\\r)$/, ''), true))\n        this.tag('</div>')\n        this.setData('mathFlowOpen')\n        this.setData('slurpOneLineEnding')\n      },\n      mathFlowFence() {\n        // After the first fence.\n        if (!this.getData('mathFlowOpen')) {\n          this.setData('mathFlowOpen', true)\n          this.setData('slurpOneLineEnding', true)\n          this.buffer()\n        }\n      },\n      mathFlowFenceMeta() {\n        this.resume()\n      },\n      mathFlowValue(token) {\n        this.raw(this.sliceSerialize(token))\n      },\n      mathText() {\n        const value = this.resume()\n        this.tag(math(value, false))\n        this.tag('</span>')\n      },\n      mathTextData(token) {\n        this.raw(this.sliceSerialize(token))\n      }\n    }\n  }\n\n  /**\n   * @param {string} value\n   *   Math text.\n   * @param {boolean} displayMode\n   *   Whether the math is in display mode.\n   * @returns {string}\n   *   HTML.\n   */\n  function math(value, displayMode) {\n    return renderToString(value, {...options, displayMode})\n  }\n}\n", "/// <reference types=\"mdast-util-math\" />\n/// <reference types=\"remark-parse\" />\n/// <reference types=\"remark-stringify\" />\n\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-math').ToOptions} Options\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\nimport {mathFromMarkdown, mathToMarkdown} from 'mdast-util-math'\nimport {math} from 'micromark-extension-math'\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Add support for math.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkMath(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push(math(settings))\n  fromMarkdownExtensions.push(mathFromMarkdown())\n  toMarkdownExtensions.push(mathToMarkdown(settings))\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA8BO,SAAS,mBAAmB;AACjC,SAAO;AAAA,IACL,OAAO;AAAA,MACL,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,eAAe;AAAA,MACf,mBAAmB;AAAA,MACnB,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF;AAMA,WAAS,cAAc,OAAO;AAE5B,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,WAAW,CAAC,iBAAiB,cAAc,EAAC;AAAA,MACzD,UAAU,CAAC;AAAA,IACb;AACA,SAAK;AAAA,MACH;AAAA,QACE,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,EAAC,OAAO,OAAO,WAAW,CAAC,IAAI,EAAC;AAAA,MACxC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAMA,WAAS,oBAAoB;AAC3B,SAAK,OAAO;AAAA,EACd;AAMA,WAAS,mBAAmB;AAC1B,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,SAAS,MAAM;AAC3B,SAAK,OAAO;AAAA,EACd;AAMA,WAAS,oBAAoB;AAE3B,QAAI,KAAK,KAAK,eAAgB;AAC9B,SAAK,OAAO;AACZ,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,aAAa,OAAO;AAC3B,UAAM,OAAO,KAAK,OAAO,EAAE,QAAQ,4BAA4B,EAAE;AACjE,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,SAAS,MAAM;AAC3B,SAAK,KAAK,KAAK;AACf,SAAK,QAAQ;AAEb,UAAM;AAAA;AAAA,MAAmC,KAAK,KAAK,UAAU,CAAC;AAAA;AAC9D,OAAO,KAAK,SAAS,SAAS;AAC9B,OAAO,KAAK,YAAY,MAAM;AAC9B,SAAK,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAC9C,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,cAAc,OAAO;AAC5B,SAAK;AAAA,MACH;AAAA,QACE,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,OAAO;AAAA,UACP,aAAa,EAAC,WAAW,CAAC,iBAAiB,aAAa,EAAC;AAAA,UACzD,WAAW,CAAC;AAAA,QACd;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,SAAK,OAAO;AAAA,EACd;AAMA,WAAS,aAAa,OAAO;AAC3B,UAAM,OAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,SAAS,YAAY;AACjC,SAAK,KAAK,KAAK;AACf,SAAK,QAAQ;AACb,UAAM;AAAA;AAAA;AAAA,MAEJ,KAAK,KAAK;AAAA;AAEZ,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EAC3C;AAMA,WAAS,aAAa,OAAO;AAC3B,SAAK,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK;AACvC,SAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK;AAAA,EACxC;AACF;AAUO,SAAS,eAAe,SAAS;AACtC,MAAI,UAAU,WAAW,CAAC,GAAG;AAE7B,MAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,aAAS;AAAA,EACX;AAEA,aAAW,OAAO;AAElB,SAAO;AAAA,IACL,QAAQ;AAAA,MACN,EAAC,WAAW,MAAM,aAAa,eAAc;AAAA,MAC7C,EAAC,WAAW,MAAM,aAAa,eAAc;AAAA,MAC7C;AAAA,QACE,WAAW;AAAA,QACX,OAAO,SAAS,SAAY;AAAA,QAC5B,aAAa;AAAA,MACf;AAAA,MACA,EAAC,WAAW,KAAK,aAAa,eAAc;AAAA,MAC5C,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,MAAK;AAAA,IAC9C;AAAA,IACA,UAAU,EAAC,MAAAA,OAAM,WAAU;AAAA,EAC7B;AAQA,WAASA,MAAK,MAAM,GAAG,OAAO,MAAM;AAClC,UAAM,MAAM,KAAK,SAAS;AAC1B,UAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAM,WAAW,IAAI,OAAO,KAAK,IAAI,cAAc,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AACpE,UAAM,OAAO,MAAM,MAAM,UAAU;AACnC,QAAI,QAAQ,QAAQ,KAAK,QAAQ;AAEjC,QAAI,KAAK,MAAM;AACb,YAAM,UAAU,MAAM,MAAM,cAAc;AAC1C,eAAS,QAAQ;AAAA,QACf,MAAM,KAAK,KAAK,MAAM;AAAA,UACpB,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ,CAAC,GAAG;AAAA,UACZ,GAAG,QAAQ,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AACA,cAAQ;AAAA,IACV;AAEA,aAAS,QAAQ,KAAK,IAAI;AAE1B,QAAI,KAAK;AACP,eAAS,QAAQ,KAAK,MAAM,IAAI;AAAA,IAClC;AAEA,aAAS,QAAQ,KAAK,QAAQ;AAC9B,SAAK;AACL,WAAO;AAAA,EACT;AAQA,WAAS,WAAW,MAAM,GAAG,OAAO;AAClC,QAAI,QAAQ,KAAK,SAAS;AAC1B,QAAI,OAAO;AAEX,QAAI,CAAC,OAAQ;AAKb,WACE,IAAI,OAAO,aAAa,MAAM,OAAO,IAAI,IAAI,UAAU,EAAE,KAAK,KAAK,GACnE;AACA;AAAA,IACF;AAEA,UAAM,WAAW,IAAI,OAAO,IAAI;AAKhC;AAAA;AAAA,MAEE,WAAW,KAAK,KAAK;AAAA,OAEnB,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,MAE/C,UAAU,KAAK,KAAK;AAAA,MACtB;AACA,cAAQ,MAAM,QAAQ;AAAA,IACxB;AAEA,QAAI,QAAQ;AASZ,WAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AACpC,YAAM,UAAU,MAAM,OAAO,KAAK;AAKlC,UAAI,CAAC,QAAQ,QAAS;AAEtB,YAAM,aAAa,MAAM,eAAe,OAAO;AAE/C,UAAI;AAEJ,aAAQ,QAAQ,WAAW,KAAK,KAAK,GAAI;AACvC,YAAI,WAAW,MAAM;AAGrB,YACE,MAAM,YAAY,QAAQ,MAAM,MAChC,MAAM,YAAY,WAAW,CAAC,MAAM,IACpC;AACA;AAAA,QACF;AAEA,gBAAQ,MAAM,MAAM,GAAG,QAAQ,IAAI,MAAM,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,MACtE;AAAA,IACF;AAEA,WAAO,WAAW,QAAQ;AAAA,EAC5B;AAKA,WAAS,iBAAiB;AACxB,WAAO;AAAA,EACT;AACF;;;AClTO,IAAM,WAAW;AAAA,EACtB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,MAAM;AACR;AAGA,IAAM,sBAAsB;AAAA,EAC1B,UAAU;AAAA,EACV,SAAS;AACX;AAMA,SAAS,mBAAmB,SAASC,KAAI,KAAK;AAC5C,QAAM,OAAO;AACb,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,QAAM,cACJ,QAAQ,KAAK,CAAC,EAAE,SAAS,MAAM,aAC3B,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,SACtC;AACN,MAAI,WAAW;AAEf,SAAO;AAcP,WAAS,MAAM,MAAM;AACnB,OAAO,SAAS,MAAM,YAAY,cAAc;AAChD,YAAQ,MAAM,UAAU;AACxB,YAAQ,MAAM,eAAe;AAC7B,YAAQ,MAAM,uBAAuB;AACrC,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,YAAY;AAC7B,cAAQ,QAAQ,IAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,GAAG;AAChB,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,KAAK,uBAAuB;AACpC,WAAO,aAAa,SAAS,YAAY,MAAM,UAAU,EAAE,IAAI;AAAA,EACjE;AAeA,WAAS,WAAW,MAAM;AACxB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,aAAO,UAAU,IAAI;AAAA,IACvB;AAEA,YAAQ,MAAM,mBAAmB;AACjC,YAAQ,MAAM,MAAM,aAAa,EAAC,aAAa,UAAU,kBAAiB,CAAC;AAC3E,WAAO,KAAK,IAAI;AAAA,EAClB;AAcA,WAAS,KAAK,MAAM;AAClB,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,MAAM,WAAW;AAC9B,cAAQ,KAAK,mBAAmB;AAChC,aAAO,UAAU,IAAI;AAAA,IACvB;AAEA,QAAI,SAAS,MAAM,YAAY;AAC7B,aAAO,IAAI,IAAI;AAAA,IACjB;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,UAAU,MAAM;AAEvB,YAAQ,KAAK,eAAe;AAE5B,QAAI,KAAK,WAAW;AAClB,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAeA,WAAS,0BAA0B,MAAM;AACvC,WAAO,QAAQ;AAAA,MACb,EAAC,UAAU,sBAAsB,SAAS,KAAI;AAAA,MAC9C;AAAA,MACA;AAAA,IACF,EAAE,IAAI;AAAA,EACR;AAcA,WAAS,aAAa,MAAM;AAC1B,YACE,cACI;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,IACA,oBACJ,IAAI;AAAA,EACR;AAcA,WAAS,mBAAmB,MAAM;AAChC,QAAI,SAAS,MAAM,KAAK;AACtB,aAAO,MAAM,IAAI;AAAA,IACnB;AAEA,QAAI,mBAAmB,IAAI,GAAG;AAC5B,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI;AAAA,IACR;AAEA,YAAQ,MAAM,eAAe;AAC7B,WAAO,aAAa,IAAI;AAAA,EAC1B;AAcA,WAAS,aAAa,MAAM;AAC1B,QAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,cAAQ,KAAK,eAAe;AAC5B,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAEA,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACT;AAcA,WAAS,MAAM,MAAM;AACnB,YAAQ,KAAK,UAAU;AACvB,WAAOA,IAAG,IAAI;AAAA,EAChB;AAGA,WAAS,qBAAqBC,UAASD,KAAIE,MAAK;AAC9C,QAAI,OAAO;AAEX,OAAO,KAAK,OAAO,WAAW,QAAQ,MAAM,yBAAyB;AAWrE,WAAO;AAAA,MACLD;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,IAChB;AAcA,aAAS,oBAAoB,MAAM;AACjC,MAAAA,SAAQ,MAAM,eAAe;AAC7B,MAAAA,SAAQ,MAAM,uBAAuB;AACrC,aAAO,cAAc,IAAI;AAAA,IAC3B;AAcA,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,MAAM,YAAY;AAC7B;AACA,QAAAA,SAAQ,QAAQ,IAAI;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,UAAU;AACnB,eAAOC,KAAI,IAAI;AAAA,MACjB;AAEA,MAAAD,SAAQ,KAAK,uBAAuB;AACpC,aAAO,aAAaA,UAAS,oBAAoB,MAAM,UAAU,EAAE,IAAI;AAAA,IACzE;AAcA,aAAS,mBAAmB,MAAM;AAChC,UAAI,SAAS,MAAM,OAAO,mBAAmB,IAAI,GAAG;AAClD,QAAAA,SAAQ,KAAK,eAAe;AAC5B,eAAOD,IAAG,IAAI;AAAA,MAChB;AAEA,aAAOE,KAAI,IAAI;AAAA,IACjB;AAAA,EACF;AACF;AAMA,SAAS,4BAA4B,SAASF,KAAI,KAAK;AACrD,QAAM,OAAO;AAEb,SAAO;AAGP,WAAS,MAAM,MAAM;AACnB,QAAI,SAAS,MAAM;AACjB,aAAOA,IAAG,IAAI;AAAA,IAChB;AAEA,OAAO,mBAAmB,IAAI,GAAG,cAAc;AAC/C,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQ,IAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO;AAAA,EACT;AAGA,WAAS,UAAU,MAAM;AACvB,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAIA,IAAG,IAAI;AAAA,EAChE;AACF;;;ACvXO,SAAS,SAAS,SAAS;AAChC,QAAM,WAAW,WAAW,CAAC;AAC7B,MAAI,SAAS,SAAS;AAEtB,MAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,aAAS;AAAA,EACX;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT;AAAA,IACA,MAAM;AAAA,EACR;AAMA,WAAS,iBAAiB,SAASG,KAAI,KAAK;AAC1C,UAAM,OAAO;AACb,QAAI,WAAW;AAEf,QAAI;AAEJ,QAAI;AAEJ,WAAO;AAcP,aAAS,MAAM,MAAM;AACnB,SAAO,SAAS,MAAM,YAAY,cAAc;AAChD,SAAO,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG,2BAA2B;AACtE,cAAQ,MAAM,UAAU;AACxB,cAAQ,MAAM,kBAAkB;AAChC,aAAO,aAAa,IAAI;AAAA,IAC1B;AAaA,aAAS,aAAa,MAAM;AAC1B,UAAI,SAAS,MAAM,YAAY;AAC7B,gBAAQ,QAAQ,IAAI;AACpB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,WAAW,KAAK,CAAC,QAAQ;AAC3B,eAAO,IAAI,IAAI;AAAA,MACjB;AAEA,cAAQ,KAAK,kBAAkB;AAC/B,aAAO,QAAQ,IAAI;AAAA,IACrB;AAYA,aAAS,QAAQ,MAAM;AACrB,UAAI,SAAS,MAAM,KAAK;AACtB,eAAO,IAAI,IAAI;AAAA,MACjB;AAEA,UAAI,SAAS,MAAM,YAAY;AAC7B,gBAAQ,QAAQ,MAAM,kBAAkB;AACxC,eAAO;AACP,eAAO,cAAc,IAAI;AAAA,MAC3B;AAGA,UAAI,SAAS,MAAM,OAAO;AACxB,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,QAAQ,IAAI;AACpB,gBAAQ,KAAK,OAAO;AACpB,eAAO;AAAA,MACT;AAEA,UAAI,mBAAmB,IAAI,GAAG;AAC5B,gBAAQ,MAAM,MAAM,UAAU;AAC9B,gBAAQ,QAAQ,IAAI;AACpB,gBAAQ,KAAK,MAAM,UAAU;AAC7B,eAAO;AAAA,MACT;AAGA,cAAQ,MAAM,cAAc;AAC5B,aAAO,KAAK,IAAI;AAAA,IAClB;AAYA,aAAS,KAAK,MAAM;AAClB,UACE,SAAS,MAAM,OACf,SAAS,MAAM,SACf,SAAS,MAAM,cACf,mBAAmB,IAAI,GACvB;AACA,gBAAQ,KAAK,cAAc;AAC3B,eAAO,QAAQ,IAAI;AAAA,MACrB;AAEA,cAAQ,QAAQ,IAAI;AACpB,aAAO;AAAA,IACT;AAaA,aAAS,cAAc,MAAM;AAE3B,UAAI,SAAS,MAAM,YAAY;AAC7B,gBAAQ,QAAQ,IAAI;AACpB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,UAAU;AACrB,gBAAQ,KAAK,kBAAkB;AAC/B,gBAAQ,KAAK,UAAU;AACvB,eAAOA,IAAG,IAAI;AAAA,MAChB;AAGA,YAAM,OAAO;AACb,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF;AACF;AAGA,SAAS,gBAAgB,QAAQ;AAC/B,MAAI,gBAAgB,OAAO,SAAS;AACpC,MAAI,iBAAiB;AAErB,MAAI;AAEJ,MAAI;AAGJ,OACG,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,MAAM,cACxC,OAAO,cAAc,EAAE,CAAC,EAAE,SAAS,aACpC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,MAAM,cACvC,OAAO,aAAa,EAAE,CAAC,EAAE,SAAS,UACpC;AACA,YAAQ;AAGR,WAAO,EAAE,QAAQ,eAAe;AAC9B,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,gBAAgB;AAE5C,eAAO,aAAa,EAAE,CAAC,EAAE,OAAO;AAChC,eAAO,cAAc,EAAE,CAAC,EAAE,OAAO;AACjC,0BAAkB;AAClB,yBAAiB;AACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,UAAQ,iBAAiB;AACzB;AAEA,SAAO,EAAE,SAAS,eAAe;AAC/B,QAAI,UAAU,QAAW;AACvB,UACE,UAAU,iBACV,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,gBAAQ;AAAA,MACV;AAAA,IACF,WACE,UAAU,iBACV,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,YAChC;AACA,aAAO,KAAK,EAAE,CAAC,EAAE,OAAO;AAExB,UAAI,UAAU,QAAQ,GAAG;AACvB,eAAO,KAAK,EAAE,CAAC,EAAE,MAAM,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC5C,eAAO,OAAO,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAC1C,yBAAiB,QAAQ,QAAQ;AACjC,gBAAQ,QAAQ;AAAA,MAClB;AAEA,cAAQ;AAAA,IACV;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,SAAS,MAAM;AAEtB,SACE,SAAS,MAAM,cACf,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM;AAE1D;;;ACxPO,SAAS,KAAK,SAAS;AAC5B,SAAO;AAAA,IACL,MAAM,EAAC,CAAC,MAAM,UAAU,GAAG,SAAQ;AAAA,IACnC,MAAM,EAAC,CAAC,MAAM,UAAU,GAAG,SAAS,OAAO,EAAC;AAAA,EAC9C;AACF;;;AChBA,IAAM,iBAAiB,MAAM;;;ACO7B,IAAM,eAAe,CAAC;AAUP,SAAR,WAA4B,SAAS;AAG1C,QAAM;AAAA;AAAA,IAAiC;AAAA;AACvC,QAAM,WAAW,WAAW;AAC5B,QAAM,OAAO,KAAK,KAAK;AAEvB,QAAM,sBACJ,KAAK,wBAAwB,KAAK,sBAAsB,CAAC;AAC3D,QAAM,yBACJ,KAAK,2BAA2B,KAAK,yBAAyB,CAAC;AACjE,QAAM,uBACJ,KAAK,yBAAyB,KAAK,uBAAuB,CAAC;AAE7D,sBAAoB,KAAK,KAAK,QAAQ,CAAC;AACvC,yBAAuB,KAAK,iBAAiB,CAAC;AAC9C,uBAAqB,KAAK,eAAe,QAAQ,CAAC;AACpD;", "names": ["math", "ok", "effects", "nok", "ok"]}