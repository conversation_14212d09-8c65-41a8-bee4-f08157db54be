import React, { useEffect, useRef } from "react";
import {
  Avatar,
  Button,
  Space,
  Tooltip,
  Dropdown,
  message as antdMessage,
} from "antd";
import {
  UserOutlined,
  RobotOutlined,
  CopyOutlined,
  DeleteOutlined,
  MoreOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { ChatMessage } from "../types";
import { Markdown } from "./Markdown";
import {
  getMessageTextContent,
  getMessageImages,
  copyToClipboard,
} from "../utils/message";
import { useConfigStore } from "../stores";
import styles from "./chat.module.scss";
import { ThinkingIndicator } from "./ThinkingIndicator";

interface MessageListProps {
  messages: ChatMessage[];
  onDeleteMessage?: (messageId: string) => void;
  onEditMessage?: (messageId: string, newContent: string) => void;
  loading?: boolean;
}

interface MessageItemProps {
  message: ChatMessage;
  onDelete?: (messageId: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  fontSize: number;
  fontFamily: string;
  loading: boolean;
}

const MessageItem: React.FC<MessageItemProps> = observer(
  ({ message, onDelete, onEdit, fontSize, fontFamily, loading = false }) => {
    const isUser = message.role === "user";
    const isAssistant = message.role === "assistant";
    const isSystem = message.role === "system";

    const textContent = getMessageTextContent(message);
    const images = getMessageImages(message);
    const configStore = useConfigStore();
    const handleCopy = () => {
      copyToClipboard(textContent);
      antdMessage.success("已复制到剪贴板");
    };

    const handleDelete = () => {
      if (onDelete) {
        onDelete(message.id);
      }
    };

    const menuItems = [
      {
        key: "copy",
        label: "复制",
        icon: <CopyOutlined />,
        onClick: handleCopy,
      },
      {
        key: "delete",
        label: "删除",
        icon: <DeleteOutlined />,
        onClick: handleDelete,
        danger: true,
      },
    ];

    const getAvatarIcon = () => {
      if (isUser) return <UserOutlined />;
      if (isAssistant) return <RobotOutlined />;
      return <UserOutlined />;
    };

    const getAvatarColor = () => {
      if (isUser) return "#1890ff";
      if (isAssistant) return "#52c41a";
      return "#faad14";
    };

    // 计算动态宽度
    const calculateDynamicWidth = () => {
      if (!isAssistant || !message.streaming) {
        return "95%"; // 非AI回复或已完成的消息使用固定宽度
      }

      // 根据内容长度和类型计算宽度
      const contentLength = textContent.length;
      const minWidthPercent = 25; // 最小宽度百分比
      const maxWidthPercent = 95; // 最大宽度百分比

      // 检查内容类型，调整增长速度
      let growthRate = 1.5; // 基础增长率

      if (textContent.includes("```")) {
        growthRate = 3; // 代码块增长更快
      } else if (textContent.includes("|")) {
        growthRate = 2.5; // 表格增长较快
      } else if (textContent.includes("\n")) {
        growthRate = 2; // 多行文本增长较快
      }

      // 使用对数函数让增长更自然
      const widthPercent = Math.min(
        maxWidthPercent,
        minWidthPercent + Math.log(contentLength + 1) * growthRate * 3
      );

      return `${Math.max(minWidthPercent, widthPercent)}%`;
    };

    // 气泡框样式
    const getBubbleStyle = () => {
      const dynamicWidth = calculateDynamicWidth();

      const baseStyle = {
        maxWidth: dynamicWidth, // 使用动态宽度
        // minWidth: "200px", // 增加最小宽度
        padding: "12px 16px",
        borderRadius: "18px",
        wordBreak: "break-word" as const,
        position: "relative" as const,
        fontSize,
        fontFamily,
        lineHeight: 1.4,
        // 添加过渡动画，流式回复时更快的动画
        transition: message.streaming
          ? "all 0.2s ease-out"
          : "all 0.3s ease-out",
      };

      if (isUser) {
        return {
          ...baseStyle,
          // backgroundColor: "#1890ff",
          color: "#fff",
          marginLeft: "auto",
          borderTopRightRadius: "6px",

          borderBottomRightRadius: "18px",
        };
      } else {
        return {
          ...baseStyle,
          backgroundColor: "#f5f5f5",
          color: "#333",
          marginRight: "0",
          borderBottomLeftRadius: "6px",
          border: "1px solid #e8e8e8",
        };
      }
    };

    return (
      <div
        className={`${styles["message-item"]} message-item ${message.role}`}
        style={{
          display: "flex",
          flexDirection: isUser ? "row-reverse" : "row",
          gap: "8px",
          padding: "8px 16px",
          alignItems: "flex-start",
        }}
      >
        {/* 头像 */}
        <Avatar
          size={32}
          style={{
            backgroundColor: getAvatarColor(),
            flexShrink: 0,
          }}
          icon={getAvatarIcon()}
        />

        {/* 消息气泡 */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: isUser ? "flex-end" : "flex-start",
            maxWidth: "100%", // 容器占满可用空间
            minWidth: 0,
            width: "100%", // 确保容器能够充分利用空间
            flex: 1, // 弹性布局
          }}
        >
          {/* 时间戳 */}
          <div
            style={{
              fontSize: fontSize * 0.75,
              color: "#999",
              marginBottom: "4px",
              padding: "0 4px",
            }}
          >
            {message.date}
          </div>

          {/* 消息内容气泡 */}
          <div
            className={`${
              isUser ? styles["user-bubble"] : styles["ai-bubble"]
            } ${isAssistant && message.streaming ? styles["streaming"] : ""}`}
            style={getBubbleStyle()}
          >
            {/* 图片内容 */}
            {images.length > 0 && (
              <div style={{ marginBottom: textContent ? "8px" : 0 }}>
                <Space wrap size={4}>
                  {images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`消息图片 ${index + 1}`}
                      style={{
                        maxWidth: "150px",
                        maxHeight: "150px",
                        borderRadius: "8px",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        window.open(image, "_blank");
                      }}
                    />
                  ))}
                </Space>
              </div>
            )}

            {/* 文本内容 */}
            {textContent && (
              <div
                className="message-content"
                style={{
                  animation: message.streaming
                    ? "textAppear 0.3s ease-out"
                    : "none",
                }}
              >
                <Markdown
                  content={textContent}
                  loading={message.streaming}
                  fontSize={fontSize}
                  fontFamily={fontFamily}
                />
              </div>
            )}

            {/* 工具调用结果 */}
            {message.tools && message.tools.length > 0 && (
              <div style={{ marginTop: "8px" }}>
                {message.tools.map((tool, index) => (
                  <div
                    key={tool.id || index}
                    style={{
                      backgroundColor: isUser
                        ? "rgba(255,255,255,0.2)"
                        : "#f0f0f0",
                      border: `1px solid ${
                        isUser ? "rgba(255,255,255,0.3)" : "#e0e0e0"
                      }`,
                      borderRadius: "8px",
                      padding: "8px",
                      marginBottom: "4px",
                      fontSize: fontSize * 0.85,
                    }}
                  >
                    <div
                      style={{
                        fontWeight: "bold",
                        marginBottom: "4px",
                        color: tool.isError
                          ? "#ff4d4f"
                          : isUser
                          ? "#fff"
                          : "#52c41a",
                      }}
                    >
                      🔧 {tool.function?.name || "工具调用"}
                      {tool.isError && " (错误)"}
                    </div>
                    {tool.function?.arguments && (
                      <div
                        style={{
                          fontSize: fontSize * 0.75,
                          color: isUser ? "rgba(255,255,255,0.8)" : "#666",
                          marginBottom: "4px",
                        }}
                      >
                        参数: {tool.function.arguments}
                      </div>
                    )}
                    {tool.content && (
                      <div style={{ fontSize: fontSize * 0.8 }}>
                        {tool.isError ? (
                          <span style={{ color: "#ff4d4f" }}>
                            {tool.errorMsg || tool.content}
                          </span>
                        ) : (
                          tool.content
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* 错误状态 */}
            {message.isError && (
              <div
                style={{
                  backgroundColor: isUser ? "rgba(255,77,79,0.2)" : "#fff2f0",
                  border: `1px solid ${
                    isUser ? "rgba(255,77,79,0.4)" : "#ffccc7"
                  }`,
                  borderRadius: "8px",
                  padding: "6px 8px",
                  marginTop: "8px",
                  color: "#ff4d4f",
                  fontSize: fontSize * 0.85,
                }}
              >
                ⚠️ 消息发送失败，请重试
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div
            style={{
              display: "flex",
              alignSelf: isUser ? "flex-end" : "flex-start",
            }}
            className="message-actions"
          >
            <Dropdown
              menu={{ items: menuItems }}
              trigger={["click"]}
              placement={isUser ? "bottomLeft" : "bottomRight"}
            >
              <Button
                type="text"
                size="small"
                icon={<MoreOutlined />}
                style={{
                  fontSize: "12px",
                  color: "#999",
                  padding: "2px 4px",
                  height: "auto",
                }}
              />
            </Dropdown>

            {/* 加载指示器 */}
            {loading && !isUser && (
              <ThinkingIndicator fontSize={configStore.fontSize} />
            )}
          </div>
        </div>
      </div>
    );
  }
);

export const MessageList: React.FC<MessageListProps> = observer(
  ({ messages, onDeleteMessage, onEditMessage, loading = false }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const configStore = useConfigStore();
    // 自动滚动到底部
    useEffect(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, [messages.length]);

    // 添加CSS样式
    useEffect(() => {
      const style = document.createElement("style");
      style.textContent = `
        .message-item:hover .message-actions {
          opacity: 1 !important;
        }
        .message-content .markdown-content {
          margin: 0;
        }
        .message-content .markdown-content p {
          margin: 0 0 8px 0;
        }
        .message-content .markdown-content p:last-child {
          margin: 0;
        }
        .message-content .markdown-content pre {
          margin: 8px 0;
          background-color: rgba(0,0,0,0.05) !important;
        }
        .message-content .markdown-content code {
          background-color: rgba(0,0,0,0.1) !important;
        }
      `;
      document.head.appendChild(style);

      return () => {
        document.head.removeChild(style);
      };
    }, []);

    if (messages.length === 0) {
      return (
        <div
          style={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            color: "#666",
            fontSize: configStore.fontSize,

            padding: "20px",
            height: "100%",
          }}
        >
          <div
            style={{
              backgroundColor: "#f0f7ff",
              borderRadius: "50%",
              width: "80px",
              height: "80px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              marginBottom: "24px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
            }}
          >
            <RobotOutlined style={{ fontSize: "40px", color: "#1890ff" }} />
          </div>
          <div
            style={{
              fontSize: configStore.fontSize * 1.2,
              fontWeight: "bold",
              marginBottom: "8px",
              color: "#333",
            }}
          >
            开始新的对话吧！
          </div>
          <div
            style={{
              fontSize: configStore.fontSize * 0.9,
              color: "#666",
              textAlign: "center",
              maxWidth: "300px",
              lineHeight: 1.5,
            }}
          >
            你可以问我任何问题，比如：
            <ul
              style={{
                textAlign: "left",
                marginTop: "8px",
                paddingLeft: "20px",
              }}
            >
              <li>如何使用React Hooks？</li>
              <li>帮我写一个简单的登录页面</li>
              <li>解释一下Redux和MobX的区别</li>
            </ul>
          </div>
        </div>
      );
    }

    return (
      <div
        className={`${styles["message-list"]} message-list`}
        style={{
          flex: 1,
          overflow: "auto",
          padding: "16px",
          backgroundColor: "#fafafa",
        }}
      >
        {messages.map((message) => (
          <MessageItem
            key={message.id}
            message={message}
            loading={loading}
            onDelete={onDeleteMessage}
            onEdit={onEditMessage}
            fontSize={configStore.fontSize}
            fontFamily="inherit"
          />
        ))}

        <div ref={messagesEndRef} />
      </div>
    );
  }
);
