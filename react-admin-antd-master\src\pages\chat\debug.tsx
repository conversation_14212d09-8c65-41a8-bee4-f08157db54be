import React from "react";
import { Card, Typography, Space, Button } from "antd";
import { useNavigate, useLocation } from "react-router-dom";

const { Title, Text } = Typography;

// 调试页面，用于测试路由
const DebugPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const testRoutes = [
    { path: "/chat", label: "聊天首页" },
    { path: "/chat/task/test-session-123", label: "测试任务会话" },
    { path: "/chat/expert/1", label: "专家1" },
    { path: "/chat/expert/2", label: "专家2" },
    { path: "/chat/expert/3", label: "专家3" },
  ];

  return (
    <div style={{ padding: "20px" }}>
      <Card>
        <Title level={3}>路由调试页面</Title>
        <Text>当前路径: {location.pathname}</Text>
        
        <div style={{ marginTop: "20px" }}>
          <Title level={4}>测试路由</Title>
          <Space direction="vertical" style={{ width: "100%" }}>
            {testRoutes.map((route) => (
              <Button
                key={route.path}
                type={location.pathname === route.path ? "primary" : "default"}
                onClick={() => navigate(route.path)}
                block
              >
                {route.label} ({route.path})
              </Button>
            ))}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DebugPage;
