import React from "react";
import { useParams } from "react-router-dom";
import { Layout, Typography, Card, Button } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

const { Header, Content } = Layout;
const { Title, Text } = Typography;

// 任务会话页面组件
const TaskSessionPage: React.FC = () => {
  const { sessionid } = useParams<{ sessionid: string }>();
  const navigate = useNavigate();

  if (!sessionid) {
    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Card>
          <div style={{ textAlign: "center", padding: "20px" }}>
            <Title level={4}>参数错误</Title>
            <Text type="secondary">缺少会话ID参数</Text>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <Layout style={{ height: "100%", backgroundColor: "#fff" }}>
      {/* 任务会话头部 */}
      <Header
        style={{
          backgroundColor: "#fff",
          borderBottom: "1px solid #f0f0f0",
          padding: "0 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          height: "64px",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/chat")}
            style={{ marginRight: "8px" }}
          >
            返回
          </Button>
          <Title
            level={4}
            style={{
              margin: 0,
              fontSize: "18px",
              color: "#333",
            }}
          >
            任务会话
          </Title>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            会话ID: {sessionid}
          </Text>
        </div>
      </Header>

      {/* 内容区 */}
      <Content
        style={{
          display: "flex",
          flexDirection: "column",
          height: "calc(100% - 64px)",
          overflow: "hidden",
          padding: "20px",
        }}
      >
        <Card>
          <Title level={4}>任务会话详情</Title>
          <Text>会话ID: {sessionid}</Text>
          <div style={{ marginTop: "20px" }}>
            <Text type="secondary">
              这是任务会话页面。您可以在这里查看和管理特定会话的任务相关信息。
            </Text>
          </div>
          <div style={{ marginTop: "20px" }}>
            <Button type="primary" onClick={() => navigate("/chat")}>
              返回聊天首页
            </Button>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default TaskSessionPage;
