{"version": 3, "sources": ["../../.pnpm/react-split@2.0.14_react@18.3.1/node_modules/react-split/dist/react-split.es.js", "../../.pnpm/split.js@1.6.5/node_modules/split.js/dist/split.es.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport Split from 'split.js';\n\nfunction objectWithoutProperties (obj, exclude) { var target = {}; for (var k in obj) if (Object.prototype.hasOwnProperty.call(obj, k) && exclude.indexOf(k) === -1) target[k] = obj[k]; return target; }\n\nvar SplitWrapper = /*@__PURE__*/(function (superclass) {\n    function SplitWrapper () {\n        superclass.apply(this, arguments);\n    }\n\n    if ( superclass ) SplitWrapper.__proto__ = superclass;\n    SplitWrapper.prototype = Object.create( superclass && superclass.prototype );\n    SplitWrapper.prototype.constructor = SplitWrapper;\n\n    SplitWrapper.prototype.componentDidMount = function componentDidMount () {\n        var ref = this.props;\n        ref.children;\n        var gutter = ref.gutter;\n        var rest = objectWithoutProperties( ref, [\"children\", \"gutter\"] );\n        var options = rest;\n\n        options.gutter = function (index, direction) {\n            var gutterElement;\n\n            if (gutter) {\n                gutterElement = gutter(index, direction);\n            } else {\n                gutterElement = document.createElement('div');\n                gutterElement.className = \"gutter gutter-\" + direction;\n            }\n\n            // eslint-disable-next-line no-underscore-dangle\n            gutterElement.__isSplitGutter = true;\n            return gutterElement\n        };\n\n        this.split = Split(this.parent.children, options);\n    };\n\n    SplitWrapper.prototype.componentDidUpdate = function componentDidUpdate (prevProps) {\n        var this$1 = this;\n\n        var ref = this.props;\n        ref.children;\n        var minSize = ref.minSize;\n        var sizes = ref.sizes;\n        var collapsed = ref.collapsed;\n        var rest = objectWithoutProperties( ref, [\"children\", \"minSize\", \"sizes\", \"collapsed\"] );\n        var options = rest;\n        var prevMinSize = prevProps.minSize;\n        var prevSizes = prevProps.sizes;\n        var prevCollapsed = prevProps.collapsed;\n\n        var otherProps = [\n            'maxSize',\n            'expandToMin',\n            'gutterSize',\n            'gutterAlign',\n            'snapOffset',\n            'dragInterval',\n            'direction',\n            'cursor' ];\n\n        var needsRecreate = otherProps\n            // eslint-disable-next-line react/destructuring-assignment\n            .map(function (prop) { return this$1.props[prop] !== prevProps[prop]; })\n            .reduce(function (accum, same) { return accum || same; }, false);\n\n        // Compare minSize when both are arrays, when one is an array and when neither is an array\n        if (Array.isArray(minSize) && Array.isArray(prevMinSize)) {\n            var minSizeChanged = false;\n\n            minSize.forEach(function (minSizeI, i) {\n                minSizeChanged = minSizeChanged || minSizeI !== prevMinSize[i];\n            });\n\n            needsRecreate = needsRecreate || minSizeChanged;\n        } else if (Array.isArray(minSize) || Array.isArray(prevMinSize)) {\n            needsRecreate = true;\n        } else {\n            needsRecreate = needsRecreate || minSize !== prevMinSize;\n        }\n\n        // Destroy and re-create split if options changed\n        if (needsRecreate) {\n            options.minSize = minSize;\n            options.sizes = sizes || this.split.getSizes();\n            this.split.destroy(true, true);\n            options.gutter = function (index, direction, pairB) { return pairB.previousSibling; };\n            this.split = Split(\n                Array.from(this.parent.children).filter(\n                    // eslint-disable-next-line no-underscore-dangle\n                    function (element) { return !element.__isSplitGutter; }\n                ),\n                options\n            );\n        } else if (sizes) {\n            // If only the size has changed, set the size. No need to do this if re-created.\n            var sizeChanged = false;\n\n            sizes.forEach(function (sizeI, i) {\n                sizeChanged = sizeChanged || sizeI !== prevSizes[i];\n            });\n\n            if (sizeChanged) {\n                // eslint-disable-next-line react/destructuring-assignment\n                this.split.setSizes(this.props.sizes);\n            }\n        }\n\n        // Collapse after re-created or when collapsed changed.\n        if (\n            Number.isInteger(collapsed) &&\n            (collapsed !== prevCollapsed || needsRecreate)\n        ) {\n            this.split.collapse(collapsed);\n        }\n    };\n\n    SplitWrapper.prototype.componentWillUnmount = function componentWillUnmount () {\n        this.split.destroy();\n        delete this.split;\n    };\n\n    SplitWrapper.prototype.render = function render () {\n        var this$1 = this;\n\n        var ref = this.props;\n        ref.sizes;\n        ref.minSize;\n        ref.maxSize;\n        ref.expandToMin;\n        ref.gutterSize;\n        ref.gutterAlign;\n        ref.snapOffset;\n        ref.dragInterval;\n        ref.direction;\n        ref.cursor;\n        ref.gutter;\n        ref.elementStyle;\n        ref.gutterStyle;\n        ref.onDrag;\n        ref.onDragStart;\n        ref.onDragEnd;\n        ref.collapsed;\n        var children = ref.children;\n        var rest$1 = objectWithoutProperties( ref, [\"sizes\", \"minSize\", \"maxSize\", \"expandToMin\", \"gutterSize\", \"gutterAlign\", \"snapOffset\", \"dragInterval\", \"direction\", \"cursor\", \"gutter\", \"elementStyle\", \"gutterStyle\", \"onDrag\", \"onDragStart\", \"onDragEnd\", \"collapsed\", \"children\"] );\n        var rest = rest$1;\n\n        return (\n            React.createElement( 'div', Object.assign({},\n                { ref: function (parent) {\n                    this$1.parent = parent;\n                } }, rest),\n                children\n            )\n        )\n    };\n\n    return SplitWrapper;\n}(React.Component));\n\nSplitWrapper.propTypes = {\n    sizes: PropTypes.arrayOf(PropTypes.number),\n    minSize: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.arrayOf(PropTypes.number) ]),\n    maxSize: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.arrayOf(PropTypes.number) ]),\n    expandToMin: PropTypes.bool,\n    gutterSize: PropTypes.number,\n    gutterAlign: PropTypes.string,\n    snapOffset: PropTypes.oneOfType([\n        PropTypes.number,\n        PropTypes.arrayOf(PropTypes.number) ]),\n    dragInterval: PropTypes.number,\n    direction: PropTypes.string,\n    cursor: PropTypes.string,\n    gutter: PropTypes.func,\n    elementStyle: PropTypes.func,\n    gutterStyle: PropTypes.func,\n    onDrag: PropTypes.func,\n    onDragStart: PropTypes.func,\n    onDragEnd: PropTypes.func,\n    collapsed: PropTypes.number,\n    children: PropTypes.arrayOf(PropTypes.element),\n};\n\nSplitWrapper.defaultProps = {\n    sizes: undefined,\n    minSize: undefined,\n    maxSize: undefined,\n    expandToMin: undefined,\n    gutterSize: undefined,\n    gutterAlign: undefined,\n    snapOffset: undefined,\n    dragInterval: undefined,\n    direction: undefined,\n    cursor: undefined,\n    gutter: undefined,\n    elementStyle: undefined,\n    gutterStyle: undefined,\n    onDrag: undefined,\n    onDragStart: undefined,\n    onDragEnd: undefined,\n    collapsed: undefined,\n    children: undefined,\n};\n\nexport default SplitWrapper;\n", "// The programming goals of Split.js are to deliver readable, understandable and\n// maintainable code, while at the same time manually optimizing for tiny minified file size,\n// browser compatibility without additional requirements\n// and very few assumptions about the user's page layout.\nvar global = typeof window !== 'undefined' ? window : null;\nvar ssr = global === null;\nvar document = !ssr ? global.document : undefined;\n\n// Save a couple long function names that are used frequently.\n// This optimization saves around 400 bytes.\nvar addEventListener = 'addEventListener';\nvar removeEventListener = 'removeEventListener';\nvar getBoundingClientRect = 'getBoundingClientRect';\nvar gutterStartDragging = '_a';\nvar aGutterSize = '_b';\nvar bGutterSize = '_c';\nvar HORIZONTAL = 'horizontal';\nvar NOOP = function () { return false; };\n\n// Helper function determines which prefixes of CSS calc we need.\n// We only need to do this once on startup, when this anonymous function is called.\n//\n// Tests -webkit, -moz and -o prefixes. Modified from StackOverflow:\n// http://stackoverflow.com/questions/16625140/js-feature-detection-to-detect-the-usage-of-webkit-calc-over-calc/16625167#16625167\nvar calc = ssr\n    ? 'calc'\n    : ((['', '-webkit-', '-moz-', '-o-']\n          .filter(function (prefix) {\n              var el = document.createElement('div');\n              el.style.cssText = \"width:\" + prefix + \"calc(9px)\";\n\n              return !!el.style.length\n          })\n          .shift()) + \"calc\");\n\n// Helper function checks if its argument is a string-like type\nvar isString = function (v) { return typeof v === 'string' || v instanceof String; };\n\n// Helper function allows elements and string selectors to be used\n// interchangeably. In either case an element is returned. This allows us to\n// do `Split([elem1, elem2])` as well as `Split(['#id1', '#id2'])`.\nvar elementOrSelector = function (el) {\n    if (isString(el)) {\n        var ele = document.querySelector(el);\n        if (!ele) {\n            throw new Error((\"Selector \" + el + \" did not match a DOM element\"))\n        }\n        return ele\n    }\n\n    return el\n};\n\n// Helper function gets a property from the properties object, with a default fallback\nvar getOption = function (options, propName, def) {\n    var value = options[propName];\n    if (value !== undefined) {\n        return value\n    }\n    return def\n};\n\nvar getGutterSize = function (gutterSize, isFirst, isLast, gutterAlign) {\n    if (isFirst) {\n        if (gutterAlign === 'end') {\n            return 0\n        }\n        if (gutterAlign === 'center') {\n            return gutterSize / 2\n        }\n    } else if (isLast) {\n        if (gutterAlign === 'start') {\n            return 0\n        }\n        if (gutterAlign === 'center') {\n            return gutterSize / 2\n        }\n    }\n\n    return gutterSize\n};\n\n// Default options\nvar defaultGutterFn = function (i, gutterDirection) {\n    var gut = document.createElement('div');\n    gut.className = \"gutter gutter-\" + gutterDirection;\n    return gut\n};\n\nvar defaultElementStyleFn = function (dim, size, gutSize) {\n    var style = {};\n\n    if (!isString(size)) {\n        style[dim] = calc + \"(\" + size + \"% - \" + gutSize + \"px)\";\n    } else {\n        style[dim] = size;\n    }\n\n    return style\n};\n\nvar defaultGutterStyleFn = function (dim, gutSize) {\n    var obj;\n\n    return (( obj = {}, obj[dim] = (gutSize + \"px\"), obj ));\n};\n\n// The main function to initialize a split. Split.js thinks about each pair\n// of elements as an independant pair. Dragging the gutter between two elements\n// only changes the dimensions of elements in that pair. This is key to understanding\n// how the following functions operate, since each function is bound to a pair.\n//\n// A pair object is shaped like this:\n//\n// {\n//     a: DOM element,\n//     b: DOM element,\n//     aMin: Number,\n//     bMin: Number,\n//     dragging: Boolean,\n//     parent: DOM element,\n//     direction: 'horizontal' | 'vertical'\n// }\n//\n// The basic sequence:\n//\n// 1. Set defaults to something sane. `options` doesn't have to be passed at all.\n// 2. Initialize a bunch of strings based on the direction we're splitting.\n//    A lot of the behavior in the rest of the library is paramatized down to\n//    rely on CSS strings and classes.\n// 3. Define the dragging helper functions, and a few helpers to go with them.\n// 4. Loop through the elements while pairing them off. Every pair gets an\n//    `pair` object and a gutter.\n// 5. Actually size the pair elements, insert gutters and attach event listeners.\nvar Split = function (idsOption, options) {\n    if ( options === void 0 ) options = {};\n\n    if (ssr) { return {} }\n\n    var ids = idsOption;\n    var dimension;\n    var clientAxis;\n    var position;\n    var positionEnd;\n    var clientSize;\n    var elements;\n\n    // Allow HTMLCollection to be used as an argument when supported\n    if (Array.from) {\n        ids = Array.from(ids);\n    }\n\n    // All DOM elements in the split should have a common parent. We can grab\n    // the first elements parent and hope users read the docs because the\n    // behavior will be whacky otherwise.\n    var firstElement = elementOrSelector(ids[0]);\n    var parent = firstElement.parentNode;\n    var parentStyle = getComputedStyle ? getComputedStyle(parent) : null;\n    var parentFlexDirection = parentStyle ? parentStyle.flexDirection : null;\n\n    // Set default options.sizes to equal percentages of the parent element.\n    var sizes = getOption(options, 'sizes') || ids.map(function () { return 100 / ids.length; });\n\n    // Standardize minSize and maxSize to an array if it isn't already.\n    // This allows minSize and maxSize to be passed as a number.\n    var minSize = getOption(options, 'minSize', 100);\n    var minSizes = Array.isArray(minSize) ? minSize : ids.map(function () { return minSize; });\n    var maxSize = getOption(options, 'maxSize', Infinity);\n    var maxSizes = Array.isArray(maxSize) ? maxSize : ids.map(function () { return maxSize; });\n\n    // Get other options\n    var expandToMin = getOption(options, 'expandToMin', false);\n    var gutterSize = getOption(options, 'gutterSize', 10);\n    var gutterAlign = getOption(options, 'gutterAlign', 'center');\n    var snapOffset = getOption(options, 'snapOffset', 30);\n    var snapOffsets = Array.isArray(snapOffset) ? snapOffset : ids.map(function () { return snapOffset; });\n    var dragInterval = getOption(options, 'dragInterval', 1);\n    var direction = getOption(options, 'direction', HORIZONTAL);\n    var cursor = getOption(\n        options,\n        'cursor',\n        direction === HORIZONTAL ? 'col-resize' : 'row-resize'\n    );\n    var gutter = getOption(options, 'gutter', defaultGutterFn);\n    var elementStyle = getOption(\n        options,\n        'elementStyle',\n        defaultElementStyleFn\n    );\n    var gutterStyle = getOption(options, 'gutterStyle', defaultGutterStyleFn);\n\n    // 2. Initialize a bunch of strings based on the direction we're splitting.\n    // A lot of the behavior in the rest of the library is paramatized down to\n    // rely on CSS strings and classes.\n    if (direction === HORIZONTAL) {\n        dimension = 'width';\n        clientAxis = 'clientX';\n        position = 'left';\n        positionEnd = 'right';\n        clientSize = 'clientWidth';\n    } else if (direction === 'vertical') {\n        dimension = 'height';\n        clientAxis = 'clientY';\n        position = 'top';\n        positionEnd = 'bottom';\n        clientSize = 'clientHeight';\n    }\n\n    // 3. Define the dragging helper functions, and a few helpers to go with them.\n    // Each helper is bound to a pair object that contains its metadata. This\n    // also makes it easy to store references to listeners that that will be\n    // added and removed.\n    //\n    // Even though there are no other functions contained in them, aliasing\n    // this to self saves 50 bytes or so since it's used so frequently.\n    //\n    // The pair object saves metadata like dragging state, position and\n    // event listener references.\n\n    function setElementSize(el, size, gutSize, i) {\n        // Split.js allows setting sizes via numbers (ideally), or if you must,\n        // by string, like '300px'. This is less than ideal, because it breaks\n        // the fluid layout that `calc(% - px)` provides. You're on your own if you do that,\n        // make sure you calculate the gutter size by hand.\n        var style = elementStyle(dimension, size, gutSize, i);\n\n        Object.keys(style).forEach(function (prop) {\n            // eslint-disable-next-line no-param-reassign\n            el.style[prop] = style[prop];\n        });\n    }\n\n    function setGutterSize(gutterElement, gutSize, i) {\n        var style = gutterStyle(dimension, gutSize, i);\n\n        Object.keys(style).forEach(function (prop) {\n            // eslint-disable-next-line no-param-reassign\n            gutterElement.style[prop] = style[prop];\n        });\n    }\n\n    function getSizes() {\n        return elements.map(function (element) { return element.size; })\n    }\n\n    // Supports touch events, but not multitouch, so only the first\n    // finger `touches[0]` is counted.\n    function getMousePosition(e) {\n        if ('touches' in e) { return e.touches[0][clientAxis] }\n        return e[clientAxis]\n    }\n\n    // Actually adjust the size of elements `a` and `b` to `offset` while dragging.\n    // calc is used to allow calc(percentage + gutterpx) on the whole split instance,\n    // which allows the viewport to be resized without additional logic.\n    // Element a's size is the same as offset. b's size is total size - a size.\n    // Both sizes are calculated from the initial parent percentage,\n    // then the gutter size is subtracted.\n    function adjust(offset) {\n        var a = elements[this.a];\n        var b = elements[this.b];\n        var percentage = a.size + b.size;\n\n        a.size = (offset / this.size) * percentage;\n        b.size = percentage - (offset / this.size) * percentage;\n\n        setElementSize(a.element, a.size, this[aGutterSize], a.i);\n        setElementSize(b.element, b.size, this[bGutterSize], b.i);\n    }\n\n    // drag, where all the magic happens. The logic is really quite simple:\n    //\n    // 1. Ignore if the pair is not dragging.\n    // 2. Get the offset of the event.\n    // 3. Snap offset to min if within snappable range (within min + snapOffset).\n    // 4. Actually adjust each element in the pair to offset.\n    //\n    // ---------------------------------------------------------------------\n    // |    | <- a.minSize               ||              b.minSize -> |    |\n    // |    |  | <- this.snapOffset      ||     this.snapOffset -> |  |    |\n    // |    |  |                         ||                        |  |    |\n    // |    |  |                         ||                        |  |    |\n    // ---------------------------------------------------------------------\n    // | <- this.start                                        this.size -> |\n    function drag(e) {\n        var offset;\n        var a = elements[this.a];\n        var b = elements[this.b];\n\n        if (!this.dragging) { return }\n\n        // Get the offset of the event from the first side of the\n        // pair `this.start`. Then offset by the initial position of the\n        // mouse compared to the gutter size.\n        offset =\n            getMousePosition(e) -\n            this.start +\n            (this[aGutterSize] - this.dragOffset);\n\n        if (dragInterval > 1) {\n            offset = Math.round(offset / dragInterval) * dragInterval;\n        }\n\n        // If within snapOffset of min or max, set offset to min or max.\n        // snapOffset buffers a.minSize and b.minSize, so logic is opposite for both.\n        // Include the appropriate gutter sizes to prevent overflows.\n        if (offset <= a.minSize + a.snapOffset + this[aGutterSize]) {\n            offset = a.minSize + this[aGutterSize];\n        } else if (\n            offset >=\n            this.size - (b.minSize + b.snapOffset + this[bGutterSize])\n        ) {\n            offset = this.size - (b.minSize + this[bGutterSize]);\n        }\n\n        if (offset >= a.maxSize - a.snapOffset + this[aGutterSize]) {\n            offset = a.maxSize + this[aGutterSize];\n        } else if (\n            offset <=\n            this.size - (b.maxSize - b.snapOffset + this[bGutterSize])\n        ) {\n            offset = this.size - (b.maxSize + this[bGutterSize]);\n        }\n\n        // Actually adjust the size.\n        adjust.call(this, offset);\n\n        // Call the drag callback continously. Don't do anything too intensive\n        // in this callback.\n        getOption(options, 'onDrag', NOOP)(getSizes());\n    }\n\n    // Cache some important sizes when drag starts, so we don't have to do that\n    // continously:\n    //\n    // `size`: The total size of the pair. First + second + first gutter + second gutter.\n    // `start`: The leading side of the first element.\n    //\n    // ------------------------------------------------\n    // |      aGutterSize -> |||                      |\n    // |                     |||                      |\n    // |                     |||                      |\n    // |                     ||| <- bGutterSize       |\n    // ------------------------------------------------\n    // | <- start                             size -> |\n    function calculateSizes() {\n        // Figure out the parent size minus padding.\n        var a = elements[this.a].element;\n        var b = elements[this.b].element;\n\n        var aBounds = a[getBoundingClientRect]();\n        var bBounds = b[getBoundingClientRect]();\n\n        this.size =\n            aBounds[dimension] +\n            bBounds[dimension] +\n            this[aGutterSize] +\n            this[bGutterSize];\n        this.start = aBounds[position];\n        this.end = aBounds[positionEnd];\n    }\n\n    function innerSize(element) {\n        // Return nothing if getComputedStyle is not supported (< IE9)\n        // Or if parent element has no layout yet\n        if (!getComputedStyle) { return null }\n\n        var computedStyle = getComputedStyle(element);\n\n        if (!computedStyle) { return null }\n\n        var size = element[clientSize];\n\n        if (size === 0) { return null }\n\n        if (direction === HORIZONTAL) {\n            size -=\n                parseFloat(computedStyle.paddingLeft) +\n                parseFloat(computedStyle.paddingRight);\n        } else {\n            size -=\n                parseFloat(computedStyle.paddingTop) +\n                parseFloat(computedStyle.paddingBottom);\n        }\n\n        return size\n    }\n\n    // When specifying percentage sizes that are less than the computed\n    // size of the element minus the gutter, the lesser percentages must be increased\n    // (and decreased from the other elements) to make space for the pixels\n    // subtracted by the gutters.\n    function trimToMin(sizesToTrim) {\n        // Try to get inner size of parent element.\n        // If it's no supported, return original sizes.\n        var parentSize = innerSize(parent);\n        if (parentSize === null) {\n            return sizesToTrim\n        }\n\n        if (minSizes.reduce(function (a, b) { return a + b; }, 0) > parentSize) {\n            return sizesToTrim\n        }\n\n        // Keep track of the excess pixels, the amount of pixels over the desired percentage\n        // Also keep track of the elements with pixels to spare, to decrease after if needed\n        var excessPixels = 0;\n        var toSpare = [];\n\n        var pixelSizes = sizesToTrim.map(function (size, i) {\n            // Convert requested percentages to pixel sizes\n            var pixelSize = (parentSize * size) / 100;\n            var elementGutterSize = getGutterSize(\n                gutterSize,\n                i === 0,\n                i === sizesToTrim.length - 1,\n                gutterAlign\n            );\n            var elementMinSize = minSizes[i] + elementGutterSize;\n\n            // If element is too smal, increase excess pixels by the difference\n            // and mark that it has no pixels to spare\n            if (pixelSize < elementMinSize) {\n                excessPixels += elementMinSize - pixelSize;\n                toSpare.push(0);\n                return elementMinSize\n            }\n\n            // Otherwise, mark the pixels it has to spare and return it's original size\n            toSpare.push(pixelSize - elementMinSize);\n            return pixelSize\n        });\n\n        // If nothing was adjusted, return the original sizes\n        if (excessPixels === 0) {\n            return sizesToTrim\n        }\n\n        return pixelSizes.map(function (pixelSize, i) {\n            var newPixelSize = pixelSize;\n\n            // While there's still pixels to take, and there's enough pixels to spare,\n            // take as many as possible up to the total excess pixels\n            if (excessPixels > 0 && toSpare[i] - excessPixels > 0) {\n                var takenPixels = Math.min(\n                    excessPixels,\n                    toSpare[i] - excessPixels\n                );\n\n                // Subtract the amount taken for the next iteration\n                excessPixels -= takenPixels;\n                newPixelSize = pixelSize - takenPixels;\n            }\n\n            // Return the pixel size adjusted as a percentage\n            return (newPixelSize / parentSize) * 100\n        })\n    }\n\n    // stopDragging is very similar to startDragging in reverse.\n    function stopDragging() {\n        var self = this;\n        var a = elements[self.a].element;\n        var b = elements[self.b].element;\n\n        if (self.dragging) {\n            getOption(options, 'onDragEnd', NOOP)(getSizes());\n        }\n\n        self.dragging = false;\n\n        // Remove the stored event listeners. This is why we store them.\n        global[removeEventListener]('mouseup', self.stop);\n        global[removeEventListener]('touchend', self.stop);\n        global[removeEventListener]('touchcancel', self.stop);\n        global[removeEventListener]('mousemove', self.move);\n        global[removeEventListener]('touchmove', self.move);\n\n        // Clear bound function references\n        self.stop = null;\n        self.move = null;\n\n        a[removeEventListener]('selectstart', NOOP);\n        a[removeEventListener]('dragstart', NOOP);\n        b[removeEventListener]('selectstart', NOOP);\n        b[removeEventListener]('dragstart', NOOP);\n\n        a.style.userSelect = '';\n        a.style.webkitUserSelect = '';\n        a.style.MozUserSelect = '';\n        a.style.pointerEvents = '';\n\n        b.style.userSelect = '';\n        b.style.webkitUserSelect = '';\n        b.style.MozUserSelect = '';\n        b.style.pointerEvents = '';\n\n        self.gutter.style.cursor = '';\n        self.parent.style.cursor = '';\n        document.body.style.cursor = '';\n    }\n\n    // startDragging calls `calculateSizes` to store the inital size in the pair object.\n    // It also adds event listeners for mouse/touch events,\n    // and prevents selection while dragging so avoid the selecting text.\n    function startDragging(e) {\n        // Right-clicking can't start dragging.\n        if ('button' in e && e.button !== 0) {\n            return\n        }\n\n        // Alias frequently used variables to save space. 200 bytes.\n        var self = this;\n        var a = elements[self.a].element;\n        var b = elements[self.b].element;\n\n        // Call the onDragStart callback.\n        if (!self.dragging) {\n            getOption(options, 'onDragStart', NOOP)(getSizes());\n        }\n\n        // Don't actually drag the element. We emulate that in the drag function.\n        e.preventDefault();\n\n        // Set the dragging property of the pair object.\n        self.dragging = true;\n\n        // Create two event listeners bound to the same pair object and store\n        // them in the pair object.\n        self.move = drag.bind(self);\n        self.stop = stopDragging.bind(self);\n\n        // All the binding. `window` gets the stop events in case we drag out of the elements.\n        global[addEventListener]('mouseup', self.stop);\n        global[addEventListener]('touchend', self.stop);\n        global[addEventListener]('touchcancel', self.stop);\n        global[addEventListener]('mousemove', self.move);\n        global[addEventListener]('touchmove', self.move);\n\n        // Disable selection. Disable!\n        a[addEventListener]('selectstart', NOOP);\n        a[addEventListener]('dragstart', NOOP);\n        b[addEventListener]('selectstart', NOOP);\n        b[addEventListener]('dragstart', NOOP);\n\n        a.style.userSelect = 'none';\n        a.style.webkitUserSelect = 'none';\n        a.style.MozUserSelect = 'none';\n        a.style.pointerEvents = 'none';\n\n        b.style.userSelect = 'none';\n        b.style.webkitUserSelect = 'none';\n        b.style.MozUserSelect = 'none';\n        b.style.pointerEvents = 'none';\n\n        // Set the cursor at multiple levels\n        self.gutter.style.cursor = cursor;\n        self.parent.style.cursor = cursor;\n        document.body.style.cursor = cursor;\n\n        // Cache the initial sizes of the pair.\n        calculateSizes.call(self);\n\n        // Determine the position of the mouse compared to the gutter\n        self.dragOffset = getMousePosition(e) - self.end;\n    }\n\n    // adjust sizes to ensure percentage is within min size and gutter.\n    sizes = trimToMin(sizes);\n\n    // 5. Create pair and element objects. Each pair has an index reference to\n    // elements `a` and `b` of the pair (first and second elements).\n    // Loop through the elements while pairing them off. Every pair gets a\n    // `pair` object and a gutter.\n    //\n    // Basic logic:\n    //\n    // - Starting with the second element `i > 0`, create `pair` objects with\n    //   `a = i - 1` and `b = i`\n    // - Set gutter sizes based on the _pair_ being first/last. The first and last\n    //   pair have gutterSize / 2, since they only have one half gutter, and not two.\n    // - Create gutter elements and add event listeners.\n    // - Set the size of the elements, minus the gutter sizes.\n    //\n    // -----------------------------------------------------------------------\n    // |     i=0     |         i=1         |        i=2       |      i=3     |\n    // |             |                     |                  |              |\n    // |           pair 0                pair 1             pair 2           |\n    // |             |                     |                  |              |\n    // -----------------------------------------------------------------------\n    var pairs = [];\n    elements = ids.map(function (id, i) {\n        // Create the element object.\n        var element = {\n            element: elementOrSelector(id),\n            size: sizes[i],\n            minSize: minSizes[i],\n            maxSize: maxSizes[i],\n            snapOffset: snapOffsets[i],\n            i: i,\n        };\n\n        var pair;\n\n        if (i > 0) {\n            // Create the pair object with its metadata.\n            pair = {\n                a: i - 1,\n                b: i,\n                dragging: false,\n                direction: direction,\n                parent: parent,\n            };\n\n            pair[aGutterSize] = getGutterSize(\n                gutterSize,\n                i - 1 === 0,\n                false,\n                gutterAlign\n            );\n            pair[bGutterSize] = getGutterSize(\n                gutterSize,\n                false,\n                i === ids.length - 1,\n                gutterAlign\n            );\n\n            // if the parent has a reverse flex-direction, switch the pair elements.\n            if (\n                parentFlexDirection === 'row-reverse' ||\n                parentFlexDirection === 'column-reverse'\n            ) {\n                var temp = pair.a;\n                pair.a = pair.b;\n                pair.b = temp;\n            }\n        }\n\n        // Determine the size of the current element. IE8 is supported by\n        // staticly assigning sizes without draggable gutters. Assigns a string\n        // to `size`.\n        //\n        // Create gutter elements for each pair.\n        if (i > 0) {\n            var gutterElement = gutter(i, direction, element.element);\n            setGutterSize(gutterElement, gutterSize, i);\n\n            // Save bound event listener for removal later\n            pair[gutterStartDragging] = startDragging.bind(pair);\n\n            // Attach bound event listener\n            gutterElement[addEventListener](\n                'mousedown',\n                pair[gutterStartDragging]\n            );\n            gutterElement[addEventListener](\n                'touchstart',\n                pair[gutterStartDragging]\n            );\n\n            parent.insertBefore(gutterElement, element.element);\n\n            pair.gutter = gutterElement;\n        }\n\n        setElementSize(\n            element.element,\n            element.size,\n            getGutterSize(\n                gutterSize,\n                i === 0,\n                i === ids.length - 1,\n                gutterAlign\n            ),\n            i\n        );\n\n        // After the first iteration, and we have a pair object, append it to the\n        // list of pairs.\n        if (i > 0) {\n            pairs.push(pair);\n        }\n\n        return element\n    });\n\n    function adjustToMin(element) {\n        var isLast = element.i === pairs.length;\n        var pair = isLast ? pairs[element.i - 1] : pairs[element.i];\n\n        calculateSizes.call(pair);\n\n        var size = isLast\n            ? pair.size - element.minSize - pair[bGutterSize]\n            : element.minSize + pair[aGutterSize];\n\n        adjust.call(pair, size);\n    }\n\n    elements.forEach(function (element) {\n        var computedSize = element.element[getBoundingClientRect]()[dimension];\n\n        if (computedSize < element.minSize) {\n            if (expandToMin) {\n                adjustToMin(element);\n            } else {\n                // eslint-disable-next-line no-param-reassign\n                element.minSize = computedSize;\n            }\n        }\n    });\n\n    function setSizes(newSizes) {\n        var trimmed = trimToMin(newSizes);\n        trimmed.forEach(function (newSize, i) {\n            if (i > 0) {\n                var pair = pairs[i - 1];\n\n                var a = elements[pair.a];\n                var b = elements[pair.b];\n\n                a.size = trimmed[i - 1];\n                b.size = newSize;\n\n                setElementSize(a.element, a.size, pair[aGutterSize], a.i);\n                setElementSize(b.element, b.size, pair[bGutterSize], b.i);\n            }\n        });\n    }\n\n    function destroy(preserveStyles, preserveGutter) {\n        pairs.forEach(function (pair) {\n            if (preserveGutter !== true) {\n                pair.parent.removeChild(pair.gutter);\n            } else {\n                pair.gutter[removeEventListener](\n                    'mousedown',\n                    pair[gutterStartDragging]\n                );\n                pair.gutter[removeEventListener](\n                    'touchstart',\n                    pair[gutterStartDragging]\n                );\n            }\n\n            if (preserveStyles !== true) {\n                var style = elementStyle(\n                    dimension,\n                    pair.a.size,\n                    pair[aGutterSize]\n                );\n\n                Object.keys(style).forEach(function (prop) {\n                    elements[pair.a].element.style[prop] = '';\n                    elements[pair.b].element.style[prop] = '';\n                });\n            }\n        });\n    }\n\n    return {\n        setSizes: setSizes,\n        getSizes: getSizes,\n        collapse: function collapse(i) {\n            adjustToMin(elements[i]);\n        },\n        destroy: destroy,\n        parent: parent,\n        pairs: pairs,\n    }\n};\n\nexport default Split;\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAkB;AAClB,wBAAsB;;;ACGtB,IAAI,SAAS,OAAO,WAAW,cAAc,SAAS;AACtD,IAAI,MAAM,WAAW;AACrB,IAAIA,YAAW,CAAC,MAAM,OAAO,WAAW;AAIxC,IAAI,mBAAmB;AACvB,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,OAAO,WAAY;AAAE,SAAO;AAAO;AAOvC,IAAI,OAAO,MACL,SACE,CAAC,IAAI,YAAY,SAAS,KAAK,EAC5B,OAAO,SAAU,QAAQ;AACtB,MAAI,KAAKA,UAAS,cAAc,KAAK;AACrC,KAAG,MAAM,UAAU,WAAW,SAAS;AAEvC,SAAO,CAAC,CAAC,GAAG,MAAM;AACtB,CAAC,EACA,MAAM,IAAK;AAGtB,IAAI,WAAW,SAAU,GAAG;AAAE,SAAO,OAAO,MAAM,YAAY,aAAa;AAAQ;AAKnF,IAAI,oBAAoB,SAAU,IAAI;AAClC,MAAI,SAAS,EAAE,GAAG;AACd,QAAI,MAAMA,UAAS,cAAc,EAAE;AACnC,QAAI,CAAC,KAAK;AACN,YAAM,IAAI,MAAO,cAAc,KAAK,8BAA+B;AAAA,IACvE;AACA,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AAGA,IAAI,YAAY,SAAU,SAAS,UAAU,KAAK;AAC9C,MAAI,QAAQ,QAAQ,QAAQ;AAC5B,MAAI,UAAU,QAAW;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAI,gBAAgB,SAAU,YAAY,SAAS,QAAQ,aAAa;AACpE,MAAI,SAAS;AACT,QAAI,gBAAgB,OAAO;AACvB,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,UAAU;AAC1B,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ,WAAW,QAAQ;AACf,QAAI,gBAAgB,SAAS;AACzB,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,UAAU;AAC1B,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AAEA,SAAO;AACX;AAGA,IAAI,kBAAkB,SAAU,GAAG,iBAAiB;AAChD,MAAI,MAAMA,UAAS,cAAc,KAAK;AACtC,MAAI,YAAY,mBAAmB;AACnC,SAAO;AACX;AAEA,IAAI,wBAAwB,SAAU,KAAK,MAAM,SAAS;AACtD,MAAI,QAAQ,CAAC;AAEb,MAAI,CAAC,SAAS,IAAI,GAAG;AACjB,UAAM,GAAG,IAAI,OAAO,MAAM,OAAO,SAAS,UAAU;AAAA,EACxD,OAAO;AACH,UAAM,GAAG,IAAI;AAAA,EACjB;AAEA,SAAO;AACX;AAEA,IAAI,uBAAuB,SAAU,KAAK,SAAS;AAC/C,MAAI;AAEJ,SAAU,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,UAAU,MAAO;AACrD;AA6BA,IAAI,QAAQ,SAAU,WAAW,SAAS;AACtC,MAAK,YAAY,OAAS,WAAU,CAAC;AAErC,MAAI,KAAK;AAAE,WAAO,CAAC;AAAA,EAAE;AAErB,MAAI,MAAM;AACV,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAGJ,MAAI,MAAM,MAAM;AACZ,UAAM,MAAM,KAAK,GAAG;AAAA,EACxB;AAKA,MAAI,eAAe,kBAAkB,IAAI,CAAC,CAAC;AAC3C,MAAI,SAAS,aAAa;AAC1B,MAAI,cAAc,mBAAmB,iBAAiB,MAAM,IAAI;AAChE,MAAI,sBAAsB,cAAc,YAAY,gBAAgB;AAGpE,MAAI,QAAQ,UAAU,SAAS,OAAO,KAAK,IAAI,IAAI,WAAY;AAAE,WAAO,MAAM,IAAI;AAAA,EAAQ,CAAC;AAI3F,MAAI,UAAU,UAAU,SAAS,WAAW,GAAG;AAC/C,MAAI,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,IAAI,IAAI,WAAY;AAAE,WAAO;AAAA,EAAS,CAAC;AACzF,MAAI,UAAU,UAAU,SAAS,WAAW,QAAQ;AACpD,MAAI,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,IAAI,IAAI,WAAY;AAAE,WAAO;AAAA,EAAS,CAAC;AAGzF,MAAI,cAAc,UAAU,SAAS,eAAe,KAAK;AACzD,MAAI,aAAa,UAAU,SAAS,cAAc,EAAE;AACpD,MAAI,cAAc,UAAU,SAAS,eAAe,QAAQ;AAC5D,MAAI,aAAa,UAAU,SAAS,cAAc,EAAE;AACpD,MAAI,cAAc,MAAM,QAAQ,UAAU,IAAI,aAAa,IAAI,IAAI,WAAY;AAAE,WAAO;AAAA,EAAY,CAAC;AACrG,MAAI,eAAe,UAAU,SAAS,gBAAgB,CAAC;AACvD,MAAI,YAAY,UAAU,SAAS,aAAa,UAAU;AAC1D,MAAI,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,cAAc,aAAa,eAAe;AAAA,EAC9C;AACA,MAAI,SAAS,UAAU,SAAS,UAAU,eAAe;AACzD,MAAI,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,MAAI,cAAc,UAAU,SAAS,eAAe,oBAAoB;AAKxE,MAAI,cAAc,YAAY;AAC1B,gBAAY;AACZ,iBAAa;AACb,eAAW;AACX,kBAAc;AACd,iBAAa;AAAA,EACjB,WAAW,cAAc,YAAY;AACjC,gBAAY;AACZ,iBAAa;AACb,eAAW;AACX,kBAAc;AACd,iBAAa;AAAA,EACjB;AAaA,WAAS,eAAe,IAAI,MAAM,SAAS,GAAG;AAK1C,QAAI,QAAQ,aAAa,WAAW,MAAM,SAAS,CAAC;AAEpD,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,MAAM;AAEvC,SAAG,MAAM,IAAI,IAAI,MAAM,IAAI;AAAA,IAC/B,CAAC;AAAA,EACL;AAEA,WAAS,cAAc,eAAe,SAAS,GAAG;AAC9C,QAAI,QAAQ,YAAY,WAAW,SAAS,CAAC;AAE7C,WAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,MAAM;AAEvC,oBAAc,MAAM,IAAI,IAAI,MAAM,IAAI;AAAA,IAC1C,CAAC;AAAA,EACL;AAEA,WAAS,WAAW;AAChB,WAAO,SAAS,IAAI,SAAU,SAAS;AAAE,aAAO,QAAQ;AAAA,IAAM,CAAC;AAAA,EACnE;AAIA,WAAS,iBAAiB,GAAG;AACzB,QAAI,aAAa,GAAG;AAAE,aAAO,EAAE,QAAQ,CAAC,EAAE,UAAU;AAAA,IAAE;AACtD,WAAO,EAAE,UAAU;AAAA,EACvB;AAQA,WAAS,OAAO,QAAQ;AACpB,QAAI,IAAI,SAAS,KAAK,CAAC;AACvB,QAAI,IAAI,SAAS,KAAK,CAAC;AACvB,QAAI,aAAa,EAAE,OAAO,EAAE;AAE5B,MAAE,OAAQ,SAAS,KAAK,OAAQ;AAChC,MAAE,OAAO,aAAc,SAAS,KAAK,OAAQ;AAE7C,mBAAe,EAAE,SAAS,EAAE,MAAM,KAAK,WAAW,GAAG,EAAE,CAAC;AACxD,mBAAe,EAAE,SAAS,EAAE,MAAM,KAAK,WAAW,GAAG,EAAE,CAAC;AAAA,EAC5D;AAgBA,WAAS,KAAK,GAAG;AACb,QAAI;AACJ,QAAI,IAAI,SAAS,KAAK,CAAC;AACvB,QAAI,IAAI,SAAS,KAAK,CAAC;AAEvB,QAAI,CAAC,KAAK,UAAU;AAAE;AAAA,IAAO;AAK7B,aACI,iBAAiB,CAAC,IAClB,KAAK,SACJ,KAAK,WAAW,IAAI,KAAK;AAE9B,QAAI,eAAe,GAAG;AAClB,eAAS,KAAK,MAAM,SAAS,YAAY,IAAI;AAAA,IACjD;AAKA,QAAI,UAAU,EAAE,UAAU,EAAE,aAAa,KAAK,WAAW,GAAG;AACxD,eAAS,EAAE,UAAU,KAAK,WAAW;AAAA,IACzC,WACI,UACA,KAAK,QAAQ,EAAE,UAAU,EAAE,aAAa,KAAK,WAAW,IAC1D;AACE,eAAS,KAAK,QAAQ,EAAE,UAAU,KAAK,WAAW;AAAA,IACtD;AAEA,QAAI,UAAU,EAAE,UAAU,EAAE,aAAa,KAAK,WAAW,GAAG;AACxD,eAAS,EAAE,UAAU,KAAK,WAAW;AAAA,IACzC,WACI,UACA,KAAK,QAAQ,EAAE,UAAU,EAAE,aAAa,KAAK,WAAW,IAC1D;AACE,eAAS,KAAK,QAAQ,EAAE,UAAU,KAAK,WAAW;AAAA,IACtD;AAGA,WAAO,KAAK,MAAM,MAAM;AAIxB,cAAU,SAAS,UAAU,IAAI,EAAE,SAAS,CAAC;AAAA,EACjD;AAeA,WAAS,iBAAiB;AAEtB,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AACzB,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AAEzB,QAAI,UAAU,EAAE,qBAAqB,EAAE;AACvC,QAAI,UAAU,EAAE,qBAAqB,EAAE;AAEvC,SAAK,OACD,QAAQ,SAAS,IACjB,QAAQ,SAAS,IACjB,KAAK,WAAW,IAChB,KAAK,WAAW;AACpB,SAAK,QAAQ,QAAQ,QAAQ;AAC7B,SAAK,MAAM,QAAQ,WAAW;AAAA,EAClC;AAEA,WAAS,UAAU,SAAS;AAGxB,QAAI,CAAC,kBAAkB;AAAE,aAAO;AAAA,IAAK;AAErC,QAAI,gBAAgB,iBAAiB,OAAO;AAE5C,QAAI,CAAC,eAAe;AAAE,aAAO;AAAA,IAAK;AAElC,QAAI,OAAO,QAAQ,UAAU;AAE7B,QAAI,SAAS,GAAG;AAAE,aAAO;AAAA,IAAK;AAE9B,QAAI,cAAc,YAAY;AAC1B,cACI,WAAW,cAAc,WAAW,IACpC,WAAW,cAAc,YAAY;AAAA,IAC7C,OAAO;AACH,cACI,WAAW,cAAc,UAAU,IACnC,WAAW,cAAc,aAAa;AAAA,IAC9C;AAEA,WAAO;AAAA,EACX;AAMA,WAAS,UAAU,aAAa;AAG5B,QAAI,aAAa,UAAU,MAAM;AACjC,QAAI,eAAe,MAAM;AACrB,aAAO;AAAA,IACX;AAEA,QAAI,SAAS,OAAO,SAAU,GAAG,GAAG;AAAE,aAAO,IAAI;AAAA,IAAG,GAAG,CAAC,IAAI,YAAY;AACpE,aAAO;AAAA,IACX;AAIA,QAAI,eAAe;AACnB,QAAI,UAAU,CAAC;AAEf,QAAI,aAAa,YAAY,IAAI,SAAU,MAAM,GAAG;AAEhD,UAAI,YAAa,aAAa,OAAQ;AACtC,UAAI,oBAAoB;AAAA,QACpB;AAAA,QACA,MAAM;AAAA,QACN,MAAM,YAAY,SAAS;AAAA,QAC3B;AAAA,MACJ;AACA,UAAI,iBAAiB,SAAS,CAAC,IAAI;AAInC,UAAI,YAAY,gBAAgB;AAC5B,wBAAgB,iBAAiB;AACjC,gBAAQ,KAAK,CAAC;AACd,eAAO;AAAA,MACX;AAGA,cAAQ,KAAK,YAAY,cAAc;AACvC,aAAO;AAAA,IACX,CAAC;AAGD,QAAI,iBAAiB,GAAG;AACpB,aAAO;AAAA,IACX;AAEA,WAAO,WAAW,IAAI,SAAU,WAAW,GAAG;AAC1C,UAAI,eAAe;AAInB,UAAI,eAAe,KAAK,QAAQ,CAAC,IAAI,eAAe,GAAG;AACnD,YAAI,cAAc,KAAK;AAAA,UACnB;AAAA,UACA,QAAQ,CAAC,IAAI;AAAA,QACjB;AAGA,wBAAgB;AAChB,uBAAe,YAAY;AAAA,MAC/B;AAGA,aAAQ,eAAe,aAAc;AAAA,IACzC,CAAC;AAAA,EACL;AAGA,WAAS,eAAe;AACpB,QAAI,OAAO;AACX,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AACzB,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AAEzB,QAAI,KAAK,UAAU;AACf,gBAAU,SAAS,aAAa,IAAI,EAAE,SAAS,CAAC;AAAA,IACpD;AAEA,SAAK,WAAW;AAGhB,WAAO,mBAAmB,EAAE,WAAW,KAAK,IAAI;AAChD,WAAO,mBAAmB,EAAE,YAAY,KAAK,IAAI;AACjD,WAAO,mBAAmB,EAAE,eAAe,KAAK,IAAI;AACpD,WAAO,mBAAmB,EAAE,aAAa,KAAK,IAAI;AAClD,WAAO,mBAAmB,EAAE,aAAa,KAAK,IAAI;AAGlD,SAAK,OAAO;AACZ,SAAK,OAAO;AAEZ,MAAE,mBAAmB,EAAE,eAAe,IAAI;AAC1C,MAAE,mBAAmB,EAAE,aAAa,IAAI;AACxC,MAAE,mBAAmB,EAAE,eAAe,IAAI;AAC1C,MAAE,mBAAmB,EAAE,aAAa,IAAI;AAExC,MAAE,MAAM,aAAa;AACrB,MAAE,MAAM,mBAAmB;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,gBAAgB;AAExB,MAAE,MAAM,aAAa;AACrB,MAAE,MAAM,mBAAmB;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,gBAAgB;AAExB,SAAK,OAAO,MAAM,SAAS;AAC3B,SAAK,OAAO,MAAM,SAAS;AAC3B,IAAAA,UAAS,KAAK,MAAM,SAAS;AAAA,EACjC;AAKA,WAAS,cAAc,GAAG;AAEtB,QAAI,YAAY,KAAK,EAAE,WAAW,GAAG;AACjC;AAAA,IACJ;AAGA,QAAI,OAAO;AACX,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AACzB,QAAI,IAAI,SAAS,KAAK,CAAC,EAAE;AAGzB,QAAI,CAAC,KAAK,UAAU;AAChB,gBAAU,SAAS,eAAe,IAAI,EAAE,SAAS,CAAC;AAAA,IACtD;AAGA,MAAE,eAAe;AAGjB,SAAK,WAAW;AAIhB,SAAK,OAAO,KAAK,KAAK,IAAI;AAC1B,SAAK,OAAO,aAAa,KAAK,IAAI;AAGlC,WAAO,gBAAgB,EAAE,WAAW,KAAK,IAAI;AAC7C,WAAO,gBAAgB,EAAE,YAAY,KAAK,IAAI;AAC9C,WAAO,gBAAgB,EAAE,eAAe,KAAK,IAAI;AACjD,WAAO,gBAAgB,EAAE,aAAa,KAAK,IAAI;AAC/C,WAAO,gBAAgB,EAAE,aAAa,KAAK,IAAI;AAG/C,MAAE,gBAAgB,EAAE,eAAe,IAAI;AACvC,MAAE,gBAAgB,EAAE,aAAa,IAAI;AACrC,MAAE,gBAAgB,EAAE,eAAe,IAAI;AACvC,MAAE,gBAAgB,EAAE,aAAa,IAAI;AAErC,MAAE,MAAM,aAAa;AACrB,MAAE,MAAM,mBAAmB;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,gBAAgB;AAExB,MAAE,MAAM,aAAa;AACrB,MAAE,MAAM,mBAAmB;AAC3B,MAAE,MAAM,gBAAgB;AACxB,MAAE,MAAM,gBAAgB;AAGxB,SAAK,OAAO,MAAM,SAAS;AAC3B,SAAK,OAAO,MAAM,SAAS;AAC3B,IAAAA,UAAS,KAAK,MAAM,SAAS;AAG7B,mBAAe,KAAK,IAAI;AAGxB,SAAK,aAAa,iBAAiB,CAAC,IAAI,KAAK;AAAA,EACjD;AAGA,UAAQ,UAAU,KAAK;AAsBvB,MAAI,QAAQ,CAAC;AACb,aAAW,IAAI,IAAI,SAAU,IAAI,GAAG;AAEhC,QAAI,UAAU;AAAA,MACV,SAAS,kBAAkB,EAAE;AAAA,MAC7B,MAAM,MAAM,CAAC;AAAA,MACb,SAAS,SAAS,CAAC;AAAA,MACnB,SAAS,SAAS,CAAC;AAAA,MACnB,YAAY,YAAY,CAAC;AAAA,MACzB;AAAA,IACJ;AAEA,QAAI;AAEJ,QAAI,IAAI,GAAG;AAEP,aAAO;AAAA,QACH,GAAG,IAAI;AAAA,QACP,GAAG;AAAA,QACH,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AAEA,WAAK,WAAW,IAAI;AAAA,QAChB;AAAA,QACA,IAAI,MAAM;AAAA,QACV;AAAA,QACA;AAAA,MACJ;AACA,WAAK,WAAW,IAAI;AAAA,QAChB;AAAA,QACA;AAAA,QACA,MAAM,IAAI,SAAS;AAAA,QACnB;AAAA,MACJ;AAGA,UACI,wBAAwB,iBACxB,wBAAwB,kBAC1B;AACE,YAAI,OAAO,KAAK;AAChB,aAAK,IAAI,KAAK;AACd,aAAK,IAAI;AAAA,MACb;AAAA,IACJ;AAOA,QAAI,IAAI,GAAG;AACP,UAAI,gBAAgB,OAAO,GAAG,WAAW,QAAQ,OAAO;AACxD,oBAAc,eAAe,YAAY,CAAC;AAG1C,WAAK,mBAAmB,IAAI,cAAc,KAAK,IAAI;AAGnD,oBAAc,gBAAgB;AAAA,QAC1B;AAAA,QACA,KAAK,mBAAmB;AAAA,MAC5B;AACA,oBAAc,gBAAgB;AAAA,QAC1B;AAAA,QACA,KAAK,mBAAmB;AAAA,MAC5B;AAEA,aAAO,aAAa,eAAe,QAAQ,OAAO;AAElD,WAAK,SAAS;AAAA,IAClB;AAEA;AAAA,MACI,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,QACI;AAAA,QACA,MAAM;AAAA,QACN,MAAM,IAAI,SAAS;AAAA,QACnB;AAAA,MACJ;AAAA,MACA;AAAA,IACJ;AAIA,QAAI,IAAI,GAAG;AACP,YAAM,KAAK,IAAI;AAAA,IACnB;AAEA,WAAO;AAAA,EACX,CAAC;AAED,WAAS,YAAY,SAAS;AAC1B,QAAI,SAAS,QAAQ,MAAM,MAAM;AACjC,QAAI,OAAO,SAAS,MAAM,QAAQ,IAAI,CAAC,IAAI,MAAM,QAAQ,CAAC;AAE1D,mBAAe,KAAK,IAAI;AAExB,QAAI,OAAO,SACL,KAAK,OAAO,QAAQ,UAAU,KAAK,WAAW,IAC9C,QAAQ,UAAU,KAAK,WAAW;AAExC,WAAO,KAAK,MAAM,IAAI;AAAA,EAC1B;AAEA,WAAS,QAAQ,SAAU,SAAS;AAChC,QAAI,eAAe,QAAQ,QAAQ,qBAAqB,EAAE,EAAE,SAAS;AAErE,QAAI,eAAe,QAAQ,SAAS;AAChC,UAAI,aAAa;AACb,oBAAY,OAAO;AAAA,MACvB,OAAO;AAEH,gBAAQ,UAAU;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,WAAS,SAAS,UAAU;AACxB,QAAI,UAAU,UAAU,QAAQ;AAChC,YAAQ,QAAQ,SAAU,SAAS,GAAG;AAClC,UAAI,IAAI,GAAG;AACP,YAAI,OAAO,MAAM,IAAI,CAAC;AAEtB,YAAI,IAAI,SAAS,KAAK,CAAC;AACvB,YAAI,IAAI,SAAS,KAAK,CAAC;AAEvB,UAAE,OAAO,QAAQ,IAAI,CAAC;AACtB,UAAE,OAAO;AAET,uBAAe,EAAE,SAAS,EAAE,MAAM,KAAK,WAAW,GAAG,EAAE,CAAC;AACxD,uBAAe,EAAE,SAAS,EAAE,MAAM,KAAK,WAAW,GAAG,EAAE,CAAC;AAAA,MAC5D;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,WAAS,QAAQ,gBAAgB,gBAAgB;AAC7C,UAAM,QAAQ,SAAU,MAAM;AAC1B,UAAI,mBAAmB,MAAM;AACzB,aAAK,OAAO,YAAY,KAAK,MAAM;AAAA,MACvC,OAAO;AACH,aAAK,OAAO,mBAAmB;AAAA,UAC3B;AAAA,UACA,KAAK,mBAAmB;AAAA,QAC5B;AACA,aAAK,OAAO,mBAAmB;AAAA,UAC3B;AAAA,UACA,KAAK,mBAAmB;AAAA,QAC5B;AAAA,MACJ;AAEA,UAAI,mBAAmB,MAAM;AACzB,YAAI,QAAQ;AAAA,UACR;AAAA,UACA,KAAK,EAAE;AAAA,UACP,KAAK,WAAW;AAAA,QACpB;AAEA,eAAO,KAAK,KAAK,EAAE,QAAQ,SAAU,MAAM;AACvC,mBAAS,KAAK,CAAC,EAAE,QAAQ,MAAM,IAAI,IAAI;AACvC,mBAAS,KAAK,CAAC,EAAE,QAAQ,MAAM,IAAI,IAAI;AAAA,QAC3C,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,UAAU,SAAS,SAAS,GAAG;AAC3B,kBAAY,SAAS,CAAC,CAAC;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAO,mBAAQ;;;ADhwBf,SAAS,wBAAyB,KAAK,SAAS;AAAE,MAAI,SAAS,CAAC;AAAG,WAAS,KAAK,IAAK,KAAI,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,KAAK,QAAQ,QAAQ,CAAC,MAAM,GAAI,QAAO,CAAC,IAAI,IAAI,CAAC;AAAG,SAAO;AAAQ;AAExM,IAAI,eAA6B,SAAU,YAAY;AACnD,WAASC,gBAAgB;AACrB,eAAW,MAAM,MAAM,SAAS;AAAA,EACpC;AAEA,MAAK,WAAa,CAAAA,cAAa,YAAY;AAC3C,EAAAA,cAAa,YAAY,OAAO,OAAQ,cAAc,WAAW,SAAU;AAC3E,EAAAA,cAAa,UAAU,cAAcA;AAErC,EAAAA,cAAa,UAAU,oBAAoB,SAAS,oBAAqB;AACrE,QAAI,MAAM,KAAK;AACf,QAAI;AACJ,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,wBAAyB,KAAK,CAAC,YAAY,QAAQ,CAAE;AAChE,QAAI,UAAU;AAEd,YAAQ,SAAS,SAAU,OAAO,WAAW;AACzC,UAAI;AAEJ,UAAI,QAAQ;AACR,wBAAgB,OAAO,OAAO,SAAS;AAAA,MAC3C,OAAO;AACH,wBAAgB,SAAS,cAAc,KAAK;AAC5C,sBAAc,YAAY,mBAAmB;AAAA,MACjD;AAGA,oBAAc,kBAAkB;AAChC,aAAO;AAAA,IACX;AAEA,SAAK,QAAQ,iBAAM,KAAK,OAAO,UAAU,OAAO;AAAA,EACpD;AAEA,EAAAA,cAAa,UAAU,qBAAqB,SAAS,mBAAoB,WAAW;AAChF,QAAI,SAAS;AAEb,QAAI,MAAM,KAAK;AACf,QAAI;AACJ,QAAI,UAAU,IAAI;AAClB,QAAI,QAAQ,IAAI;AAChB,QAAI,YAAY,IAAI;AACpB,QAAI,OAAO,wBAAyB,KAAK,CAAC,YAAY,WAAW,SAAS,WAAW,CAAE;AACvF,QAAI,UAAU;AACd,QAAI,cAAc,UAAU;AAC5B,QAAI,YAAY,UAAU;AAC1B,QAAI,gBAAgB,UAAU;AAE9B,QAAI,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAS;AAEb,QAAI,gBAAgB,WAEf,IAAI,SAAU,MAAM;AAAE,aAAO,OAAO,MAAM,IAAI,MAAM,UAAU,IAAI;AAAA,IAAG,CAAC,EACtE,OAAO,SAAU,OAAO,MAAM;AAAE,aAAO,SAAS;AAAA,IAAM,GAAG,KAAK;AAGnE,QAAI,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,WAAW,GAAG;AACtD,UAAI,iBAAiB;AAErB,cAAQ,QAAQ,SAAU,UAAU,GAAG;AACnC,yBAAiB,kBAAkB,aAAa,YAAY,CAAC;AAAA,MACjE,CAAC;AAED,sBAAgB,iBAAiB;AAAA,IACrC,WAAW,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,WAAW,GAAG;AAC7D,sBAAgB;AAAA,IACpB,OAAO;AACH,sBAAgB,iBAAiB,YAAY;AAAA,IACjD;AAGA,QAAI,eAAe;AACf,cAAQ,UAAU;AAClB,cAAQ,QAAQ,SAAS,KAAK,MAAM,SAAS;AAC7C,WAAK,MAAM,QAAQ,MAAM,IAAI;AAC7B,cAAQ,SAAS,SAAU,OAAO,WAAW,OAAO;AAAE,eAAO,MAAM;AAAA,MAAiB;AACpF,WAAK,QAAQ;AAAA,QACT,MAAM,KAAK,KAAK,OAAO,QAAQ,EAAE;AAAA;AAAA,UAE7B,SAAU,SAAS;AAAE,mBAAO,CAAC,QAAQ;AAAA,UAAiB;AAAA,QAC1D;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,WAAW,OAAO;AAEd,UAAI,cAAc;AAElB,YAAM,QAAQ,SAAU,OAAO,GAAG;AAC9B,sBAAc,eAAe,UAAU,UAAU,CAAC;AAAA,MACtD,CAAC;AAED,UAAI,aAAa;AAEb,aAAK,MAAM,SAAS,KAAK,MAAM,KAAK;AAAA,MACxC;AAAA,IACJ;AAGA,QACI,OAAO,UAAU,SAAS,MACzB,cAAc,iBAAiB,gBAClC;AACE,WAAK,MAAM,SAAS,SAAS;AAAA,IACjC;AAAA,EACJ;AAEA,EAAAA,cAAa,UAAU,uBAAuB,SAAS,uBAAwB;AAC3E,SAAK,MAAM,QAAQ;AACnB,WAAO,KAAK;AAAA,EAChB;AAEA,EAAAA,cAAa,UAAU,SAAS,SAAS,SAAU;AAC/C,QAAI,SAAS;AAEb,QAAI,MAAM,KAAK;AACf,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,IAAI;AACnB,QAAI,SAAS,wBAAyB,KAAK,CAAC,SAAS,WAAW,WAAW,eAAe,cAAc,eAAe,cAAc,gBAAgB,aAAa,UAAU,UAAU,gBAAgB,eAAe,UAAU,eAAe,aAAa,aAAa,UAAU,CAAE;AACpR,QAAI,OAAO;AAEX,WACI,aAAAC,QAAM;AAAA,MAAe;AAAA,MAAO,OAAO;AAAA,QAAO,CAAC;AAAA,QACvC,EAAE,KAAK,SAAU,QAAQ;AACrB,iBAAO,SAAS;AAAA,QACpB,EAAE;AAAA,QAAG;AAAA,MAAI;AAAA,MACT;AAAA,IACJ;AAAA,EAER;AAEA,SAAOD;AACX,EAAE,aAAAC,QAAM,SAAS;AAEjB,aAAa,YAAY;AAAA,EACrB,OAAO,kBAAAC,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EACzC,SAAS,kBAAAA,QAAU,UAAU;AAAA,IACzB,kBAAAA,QAAU;AAAA,IACV,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EAAE,CAAC;AAAA,EACzC,SAAS,kBAAAA,QAAU,UAAU;AAAA,IACzB,kBAAAA,QAAU;AAAA,IACV,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EAAE,CAAC;AAAA,EACzC,aAAa,kBAAAA,QAAU;AAAA,EACvB,YAAY,kBAAAA,QAAU;AAAA,EACtB,aAAa,kBAAAA,QAAU;AAAA,EACvB,YAAY,kBAAAA,QAAU,UAAU;AAAA,IAC5B,kBAAAA,QAAU;AAAA,IACV,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EAAE,CAAC;AAAA,EACzC,cAAc,kBAAAA,QAAU;AAAA,EACxB,WAAW,kBAAAA,QAAU;AAAA,EACrB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,cAAc,kBAAAA,QAAU;AAAA,EACxB,aAAa,kBAAAA,QAAU;AAAA,EACvB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,aAAa,kBAAAA,QAAU;AAAA,EACvB,WAAW,kBAAAA,QAAU;AAAA,EACrB,WAAW,kBAAAA,QAAU;AAAA,EACrB,UAAU,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,OAAO;AACjD;AAEA,aAAa,eAAe;AAAA,EACxB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AACd;AAEA,IAAO,yBAAQ;", "names": ["document", "SplitWrapper", "React", "PropTypes"]}